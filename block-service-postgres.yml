version: '3.5'

services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-blockserviceuser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-wuMHstYVUVou1xeVeEqn6bLo2Uz9LBqy}
      POSTGRES_DB: ${POSTGRES_DB:-block_service}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      #  - ./db/init.sql:/docker-entrypoint-initdb.d/create_tables.sql
    ports:
      - "5432:5432"
      # networks:
      # - postgres
    restart: unless-stopped
    # restart: always
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB" ]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres-data:
    driver: local
    driver_opts:
      o: bind
      type: none
      device: ./data/block-service-postgres