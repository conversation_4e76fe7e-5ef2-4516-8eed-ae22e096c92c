{"name": "pumpfun-subservice", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:create": "npx typeorm-ts-node-esm migration:create", "proto-gen": "protoc --plugin=protoc-gen-ts_proto=./node_modules/.bin/protoc-gen-ts_proto.cmd --ts_proto_opt=nestJs=true --ts_proto_out=.", "migration:questdb:create": "QUESTDB_RUN_MIGRATIONS=false ts-node -r tsconfig-paths/register src/migration-cli.ts create", "migration:questdb:up": "QUESTDB_RUN_MIGRATIONS=false ts-node -r tsconfig-paths/register src/migration-cli.ts up", "migration:questdb:down": "QUESTDB_RUN_MIGRATIONS=false ts-node -r tsconfig-paths/register src/migration-cli.ts down", "prepare": "husky"}, "dependencies": {"@coral-xyz/anchor": "^0.30.1", "@grpc/grpc-js": "^1.13.3", "@grpc/proto-loader": "^0.7.15", "@nestjs/bullmq": "^10.1.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.1.1", "@nestjs/microservices": "^11.0.20", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.7", "@nestjs/schedule": "^4.1.1", "@nestjs/websockets": "^10.4.7", "@questdb/nodejs-client": "^3.0.0", "@raydium-io/raydium-sdk": "^1.3.1-beta.58", "@solana/buffer-layout": "^4.0.1", "@solana/buffer-layout-utils": "^0.2.0", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.95.4", "@triton-one/yellowstone-grpc": "^4.0.0", "axios": "^1.7.7", "base58": "^2.0.1", "bignumber.js": "^9.1.2", "bs58": "^6.0.0", "bullmq": "^5.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "google-protobuf": "^3.21.4", "ioredis": "^5.4.1", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "mongoose": "^8.15.0", "nest-winston": "^1.9.7", "pg": "^8.16.0", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "ts-proto": "^2.7.0", "umzug": "^3.8.2", "winston": "^3.16.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cron": "^2.4.3", "@types/express": "^4.17.17", "@types/google-protobuf": "^3.15.12", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.13", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.7", "jest": "^29.5.0", "lint-staged": "^16.1.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.{js,ts}": ["prettier --write", "eslint --fix"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}