import { IsInt, IsOptional, IsIn, Min, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

const SORTABLE_FIELDS = ['liquidity_usd', 'market_cap_usd'];

export class GetRecentPairsDto {
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit: number = 50;

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page: number = 1;

  @IsOptional()
  @IsIn(SORTABLE_FIELDS, {
    message: `sortBy must be one of: ${SORTABLE_FIELDS.join(', ')}`,
  })
  sortBy?: string;

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortDirection: 'asc' | 'desc' = 'desc';
}
