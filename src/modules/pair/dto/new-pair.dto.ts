export class NewPairDto {
  // Pair info
  pairAddress: string;
  createdAt: string;

  // Token info
  tokenName: string;
  tokenAddress: string;
  image: string;
  twitter?: string;
  website?: string;
  telegram?: string;
  symbol: string;

  // Detail info
  marketCapUsd: number;
  liquidityUsd: number;
  buyVolumeUsd: number;
  sellVolumeUsd: number;
  numOfBuyTxs: number;
  numOfSellTxs: number;
  numOfBuyers: number;
  numOfSellers: number;
  process: number | null;
}
