import { forwardRef, Module } from '@nestjs/common';
import { PairService } from './pair.service';
import { TokenModule } from '../token/token.module';
import { QueueModule } from '../queue/queue.module';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { QueueConfig } from '../queue/queue.interface';
import { RedisModule } from '../redis/redis.module';
import { NewPairPumpFunProcessor } from './processor/new-pair-transaction.processor';
import { SolanaModule } from '../solana/solana.module';
import { DefaultJobOptions } from 'bullmq';
import { UpdateMultiplePairProcessor } from './processor/update-multiple-pair.processor';
import { PairController } from './pair.controller';
import { PairDocumentPersistenceModule } from './infrastructure/persistence/document/document-persistence.module';
import { PairTimeSeriesPersistenceModule } from './infrastructure/persistence/time-series/time-series-persistence.module';
import { SwapTransactionModule } from '../swap-transaction/swap-transaction.module';

@Module({
  imports: [
    PairDocumentPersistenceModule,
    PairTimeSeriesPersistenceModule,
    RedisModule,
    QueueModule.register({
      queues: [PumpFunQueue.NEW_PAIR, PumpFunQueue.UPDATE_MULTIPLE_PAIR].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    TokenModule,
    SolanaModule,
    forwardRef(() => SwapTransactionModule),
  ],
  providers: [
    PairService,
    NewPairPumpFunProcessor,
    UpdateMultiplePairProcessor,
  ],
  controllers: [PairController],
  exports: [PairService],
})
export class PairModule {}
