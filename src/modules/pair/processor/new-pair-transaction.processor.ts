import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { Job } from 'bullmq';
import { PairService } from '../pair.service';
import { CreatePairDto } from '../dto/create-pair.dto';
import { PairErrorMessage } from 'src/common/constant/errors';
import { QueueHandleProcessException } from 'src/common/exceptions/queue-handle-process-exception';
import { PairDocument } from '../domain/pair-document';

@Processor(PumpFunQueue.NEW_PAIR, {
  concurrency: 100,
})
@Injectable()
export class NewPairPumpFunProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly pairService: PairService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<CreatePairDto, PairDocument | null, string>,
  ): Promise<PairDocument | null> {
    try {
      const begin = performance.now();
      const newPairDataDto = job.data;

      const savedPair =
        await this.pairService.createPumpFunPair(newPairDataDto);
      this.logger.log(
        `[SUCCESS] Processed pair ${newPairDataDto.tokenAddress}, elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );

      return savedPair;
    } catch (err) {
      this.logger.error(
        `${PumpFunQueue.NEW_PAIR} handleProcess error: ${err.message}`,
        err.stack,
      );
      throw new QueueHandleProcessException(
        `${PairErrorMessage.ERROR_QUEUE_NEW_PAIR}: ${err.message}`,
      );
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<CreatePairDto, PairDocument | null, string>) {
    const { id, returnvalue: returnValue } = job;
    if (returnValue) {
      this.logger.verbose(
        `Job id: ${id}, completed tokenAddress [${returnValue.tokenAddress}]`,
      );
    }
  }
}
