import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PairSchemaClass, PairSchema } from './entities/pair.schema';
import { PairDocumentRepository } from './repositories/pair-document.repository';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PairSchemaClass.name, schema: PairSchema },
    ]),
  ],
  providers: [PairDocumentRepository],
  exports: [PairDocumentRepository],
})
export class PairDocumentPersistenceModule {}
