import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PairSchemaClass, PairSchemaDocument } from '../entities/pair.schema';
import { PairDocumentMapper } from '../mappers/pair-document.mapper';
import { PairDocument } from '../../../../domain/pair-document';
import { NullableType } from '../../../../../../common/type/nullable.type';

@Injectable()
export class PairDocumentRepository {
  private readonly logger = new Logger(PairDocumentRepository.name);

  constructor(
    @InjectModel(PairSchemaClass.name)
    private readonly pairModel: Model<PairSchemaDocument>,
  ) {}

  async create(data: PairSchemaClass): Promise<PairSchemaClass> {
    const createdPair = new this.pairModel(data);
    const pairObject = await createdPair.save();
    return pairObject;
  }

  async findByTokenAddress(
    tokenAddress: string,
  ): Promise<PairSchemaClass | null> {
    return this.pairModel.findOne({ tokenAddress }).exec();
  }

  async findByPairAddresses(
    pairAddresses: string[],
  ): Promise<PairSchemaClass[]> {
    return this.pairModel
      .find({ pairAddress: { $in: pairAddresses } })
      .lean()
      .exec();
  }

  async findByPairAddress(
    pairAddress: string,
  ): Promise<NullableType<PairDocument>> {
    const pair = await this.pairModel
      .findOne({ pairAddress: pairAddress })
      .lean()
      .exec();

    return PairDocumentMapper.toDomain(pair);
  }

  async findRecentPairsSince(
    sinceTimestamp: number,
    limit: number,
    skip: number,
  ): Promise<PairDocument[]> {
    const pairs = await this.pairModel
      .find({ createdAt: { $gte: new Date(sinceTimestamp) } })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()
      .exec();

    return pairs.map(PairDocumentMapper.toDomain);
  }
}
