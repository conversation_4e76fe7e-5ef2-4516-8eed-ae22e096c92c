import { PairSchemaClass } from '../entities/pair.schema';
import { PairDocument } from '../../../../domain/pair-document';
import { NullableType } from '../../../../../../common/type/nullable.type';

export class PairDocumentMapper {
  public static toDomain(
    raw: NullableType<PairSchemaClass>,
  ): NullableType<PairDocument> {
    if (!raw) {
      return null;
    }

    const domainPair = new PairDocument();
    domainPair.pairAddress = raw.pairAddress;
    domainPair.tokenAddress = raw.tokenAddress;
    domainPair.signatureHash = raw.signatureHash;
    domainPair.associatedBondingCurve = raw.associatedBondingCurve;
    domainPair.creator = raw.creator;
    domainPair.createdAt = raw.createdAt;
    domainPair.updatedAt = raw.updatedAt;
    return domainPair;
  }
}
