import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { now, Document } from 'mongoose';

export type PairSchemaDocument = PairSchemaClass & Document;
@Schema({
  collection: 'pair',
})
export class PairSchemaClass {
  @Prop({ required: true })
  signatureHash: string;

  @Prop({ required: true })
  pairAddress: string;

  @Prop({ required: true })
  associatedBondingCurve: string;

  @Prop({ required: true })
  creator: string;

  @Prop({ required: true })
  tokenAddress: string;

  @Prop({ default: now })
  createdAt?: Date;

  @Prop({ default: now })
  updatedAt?: Date;
}

export const PairSchema = SchemaFactory.createForClass(PairSchemaClass);
