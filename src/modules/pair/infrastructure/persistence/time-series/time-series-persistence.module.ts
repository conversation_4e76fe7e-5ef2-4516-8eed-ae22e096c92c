import { Module } from '@nestjs/common';
import { QuestdbModule } from '../../../../questdb/questdb.module';
import { PairDetailRepository } from './repositories/pair-details.repository';
import { PairDetailTimeframesRepository } from './repositories/pair-details-timeframes.repository';

@Module({
  imports: [QuestdbModule],
  providers: [PairDetailRepository, PairDetailTimeframesRepository],
  exports: [PairDetailRepository, PairDetailTimeframesRepository],
})
export class PairTimeSeriesPersistenceModule {}
