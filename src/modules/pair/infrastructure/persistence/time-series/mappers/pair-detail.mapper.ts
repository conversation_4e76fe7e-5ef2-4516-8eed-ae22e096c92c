import { PairDetail } from '../../../../domain/pair-detail';
import { PairDetailEntity } from '../entities/pair-detail.entity';

export class PairDetailMapper {
  public static toDomain(row: PairDetailEntity): PairDetail {
    const pairDetail = new PairDetail();

    pairDetail.pairAddress = row.PAIR_ADDRESS;
    pairDetail.marketCapUsd = row.MARKET_CAP_USD;
    pairDetail.liquidityUsd = row.LIQUIDITY_USD;
    pairDetail.buyVolumeUsd = row.BUY_VOLUME_USD;
    pairDetail.sellVolumeUsd = row.SELL_VOLUME_USD;
    pairDetail.numOfBuyTxs = Number(row.NUM_OF_BUY_TXS);
    pairDetail.numOfSellTxs = Number(row.NUM_OF_SELL_TXS);
    pairDetail.process = row.PROCESS;
    pairDetail.numOfBuyers = Number(row.NUM_OF_BUYERS);
    pairDetail.numOfSellers = Number(row.NUM_OF_SELLERS);
    pairDetail.createdAt = row.CREATED_AT;
    pairDetail.updatedAt = row.UPDATED_AT;
    pairDetail.pooledSol = row.POOLED_SOL;
    pairDetail.priceUsd = row.PRICE_USD;
    pairDetail.ts = row.TS.getTime();

    return pairDetail;
  }
}
