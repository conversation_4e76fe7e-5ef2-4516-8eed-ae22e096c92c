import { PairDetailTimeframes } from '../../../../domain/pair-detail-timeframes';
import { PairDetailTimeframesEntity } from '../entities/pair-detail-timeframes.entity';

export class PairDetailTimeframesMapper {
  public static toDomain(
    raw: PairDetailTimeframesEntity,
  ): PairDetailTimeframes {
    const domain = new PairDetailTimeframes();
    domain.pairAddress = raw.PAIR_ADDRESS;
    domain.timeInterval = raw.TIME_INTERVAL;
    domain.buyVolumeUsd = raw.BUY_VOLUME_USD;
    domain.sellVolumeUsd = raw.SELL_VOLUME_USD;
    domain.numOfBuyTxs = Number(raw.NUM_OF_BUY_TXS);
    domain.numOfSellTxs = Number(raw.NUM_OF_SELL_TXS);
    domain.numOfBuyers = Number(raw.NUM_OF_BUYERS);
    domain.numOfSellers = Number(raw.NUM_OF_SELLERS);
    domain.updatedAt = raw.UPDATED_AT;
    domain.ts = raw.TS;
    return domain;
  }
}
