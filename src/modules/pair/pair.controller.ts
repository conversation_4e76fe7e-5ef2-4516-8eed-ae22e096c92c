import { Controller, Get, Query } from '@nestjs/common';
import { PairService } from './pair.service';
import { PairDto } from './dto/pair.dto';
import { GetRecentPairsDto } from './dto/get-recent-pairs.dto';
import { GetPairByAddressRequestDto } from './dto/get-pair-by-address-request.dto';

@Controller('pair')
export class PairController {
  constructor(private readonly pairService: PairService) {}

  @Get()
  async getPairsByAddress(
    @Query() query: GetPairByAddressRequestDto,
  ): Promise<PairDto> {
    return await this.pairService.getPairsByAddress(query.pairAddress);
  }

  @Get('recent')
  async getRecentNewPairs(@Query() query: GetRecentPairsDto) {
    const { limit, page, sortBy, sortDirection } = query;

    return this.pairService.getRecentNewPairsWithin24h(
      limit,
      page,
      sortBy,
      sortDirection,
    );
  }
}
