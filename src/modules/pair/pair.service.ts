import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import {
  PUMPFUN_AND_SOL_DIFFERENTIAL,
  PUMPFUN_CREATE_LOG_MESSAGE,
  PUMPFUN_PROGRAM_ID,
  PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT,
} from '../../common/constant/pumpfun';
import { CreatePumpfunPairRequestDto } from './dto/create-pumpfun-pair-request.dto';
import { TokenService } from '../token/token.service';
import { InjectQueue } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { Queue } from 'bullmq';
import {
  PUMP_FUN_NEW_PAIR_TRANSACTION_JOB,
  PUMP_FUN_UPDATE_PAIR_JOB,
} from '../../common/constant/job-name';
import {
  ASSOCIATED_TOKEN_PROGRAM_ID,
  TOKEN_PROGRAM_ID,
} from '@solana/spl-token';
import { TransactionData } from '../../common/type/transaction.data';
import { PairDetailRepository } from './infrastructure/persistence/time-series/repositories/pair-details.repository';
import { Connection, LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js';
import { BONDING_CURVE_LAYOUT } from '../../common/layout/bonding-curve';
import BigNumber from 'bignumber.js';
import { SolanaService } from '../solana/solana.service';
import _ from 'lodash';
import { SwapTransactionService } from '../swap-transaction/swap-transaction.service';
import { TxnsCount } from 'src/modules/swap-transaction/dto/txns-count.dto';
import { VolumeResult } from 'src/modules/swap-transaction/dto/volume-result.dto';
import { CreatePairDto, PumpfunExtra } from './dto/create-pair.dto';
import { PairSchemaClass } from './infrastructure/persistence/document/entities/pair.schema';
import { NewPairDto } from './dto/new-pair.dto';
import { PairData } from 'src/modules/swap-transaction/dto/swap-transaction.dto';
import { PairDetailTimeframesRepository } from './infrastructure/persistence/time-series/repositories/pair-details-timeframes.repository';
import {
  PairDetailTimeframes,
  TimeInterval,
} from 'src/modules/pair/domain/pair-detail-timeframes';
import { PairNotFoundException } from 'src/common/exceptions/pair-not-found-exception';
import { TokenNotFoundException } from 'src/common/exceptions/token-not-found-exception';
import { PairTimeFramesDto } from './dto/pair-timeframes.dto';
import { PairDto } from './dto/pair.dto';
import { RedisRepository } from 'src/modules/redis/redis.repository';
import { BuyerSellerResult } from 'src/modules/swap-transaction/dto/buyer-seller-result.dto';
import { SortDirection } from '../../common/constant/sort-direction';
import { PairDetail } from './domain/pair-detail';
import { PairDocumentRepository } from './infrastructure/persistence/document/repositories/pair-document.repository';
import { PairDocument } from './domain/pair-document';
import { PairDocumentMapper } from './infrastructure/persistence/document/mappers/pair-document.mapper';

@Injectable()
export class PairService {
  private readonly logger: Logger = new Logger(PairService.name);

  constructor(
    @Inject()
    private readonly pairDocumentRepository: PairDocumentRepository,
    @Inject()
    private readonly pairDetailRepository: PairDetailRepository,
    @Inject()
    private readonly redisRepository: RedisRepository,
    @Inject()
    private readonly pairDetailTimeFramesRepository: PairDetailTimeframesRepository,
    @Inject()
    private readonly tokenService: TokenService,
    @InjectQueue(PumpFunQueue.NEW_PAIR)
    private readonly newPairQueue: Queue,
    @InjectQueue(PumpFunQueue.UPDATE_MULTIPLE_PAIR)
    private readonly updateMultiplePairQueue: Queue,
    @Inject(Connection)
    private readonly connection: Connection,
    @Inject()
    private readonly solanaService: SolanaService,
    @Inject(forwardRef(() => SwapTransactionService))
    private readonly swapTransactionService: SwapTransactionService,
  ) {}

  async handlePumpFunPair(parsedTransactionData: TransactionData) {
    const { instructions, logs, signature, innerInstructions } =
      parsedTransactionData;
    const isFoundCreatedPair = logs?.some((log: string | string[]) => {
      return log === PUMPFUN_CREATE_LOG_MESSAGE;
    });

    if (!isFoundCreatedPair) {
      return;
    }
    const createPairInstructions = instructions.filter(
      (instruction) =>
        instruction.programId === PUMPFUN_PROGRAM_ID &&
        instruction.accounts.length === 14,
    );

    const decimals = _.chain(innerInstructions)
      .flatMap('instructions') // Flatten all instructions
      .filter((instr) => _.get(instr, 'parsed.type') === 'initializeMint2') // Filter by type
      .map('parsed.info.decimals') // Extract decimals
      .first() // Get the first value
      .value(); // Resolve the Lodash chain

    for (const createPairInstruction of createPairInstructions) {
      const accounts = createPairInstruction.accounts;
      const tokenAddress = accounts[0];
      const bondingCurve = accounts[2];
      const associatedBondingCurve = accounts[3];
      const creator = accounts[7];
      const createPumpFunPairRequestDto = new CreatePumpfunPairRequestDto();

      this.logger.log(
        `Found new Pump Fun Pair [${tokenAddress}], transaction [${signature}]`,
      );

      createPumpFunPairRequestDto.signatureHash = signature;
      createPumpFunPairRequestDto.pairAddress = bondingCurve;
      createPumpFunPairRequestDto.associatedBondingCurve =
        associatedBondingCurve;
      createPumpFunPairRequestDto.creator = creator;
      await Promise.all([
        this.newPairQueue.add(PUMP_FUN_NEW_PAIR_TRANSACTION_JOB, {
          signatureHash: createPumpFunPairRequestDto.signatureHash,
          pairAddress: bondingCurve,
          creator: creator,
          tokenAddress: tokenAddress,
          blockTime: new Date(),
          extra: {
            associatedBondingCurve,
          } as PumpfunExtra,
        } as CreatePairDto),

        this.tokenService.handlePumpFunToken(
          tokenAddress,
          decimals,
          new Date(),
          createPairInstruction.data,
        ),
      ]);
    }
  }

  async handleUpdateMultiplePumpFunPair(pairData: PairData[]) {
    await this.updateMultiplePairQueue.add(PUMP_FUN_UPDATE_PAIR_JOB, pairData);
  }

  async createPumpFunPair(
    createPairDto: CreatePairDto,
  ): Promise<PairDocument | null> {
    const pair = await this.savePair(createPairDto);

    if (!pair) {
      this.logger.error(
        `[FAILURE] Failed to store pair for token ${createPairDto.tokenAddress}`,
      );
      return null;
    }

    const pairDetail = await this.populatePairDetail(pair);

    if (!pairDetail) {
      this.logger.error(
        `[FAILURE] Failed to generate pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
      return pair;
    }

    const result =
      await this.pairDetailRepository.createPairDetailPumpFun(pairDetail);

    if (result.status !== 'inserted') {
      this.logger.error(
        `[FAILURE] Failed to store pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
    } else {
      this.logger.log(
        `[SUCCESS] Successfully stored pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
    }

    const pairDetailTimeframes = await this.populatePairDetailTimeframes(
      pair.pairAddress,
    );

    if (!pairDetailTimeframes) {
      this.logger.error(
        `[FAILURE] Failed to generate pairDetailTimeframes for bondingCurve ${createPairDto.pairAddress}`,
      );
      return pair;
    }

    for (const pairDetailTimeframe of pairDetailTimeframes) {
      const savedPairDetailTimeframes =
        await this.pairDetailTimeFramesRepository.createPairDetailTimeframesPumpFun(
          pairDetailTimeframe,
        );

      if (savedPairDetailTimeframes.status !== 'inserted') {
        this.logger.error(
          `[FAILURE] Failed to store pairDetailTimeframes [${pairDetailTimeframe.timeInterval}] for bondingCurve ${createPairDto.pairAddress}`,
        );
      } else {
        this.logger.log(
          `[SUCCESS] Successfully stored pairDetailTimeframes [${pairDetailTimeframe.timeInterval}] for bondingCurve ${createPairDto.pairAddress}`,
        );
      }
    }

    return pair;
  }

  async updateMultiplePumpfunDetailsPair(pairDatas: PairData[]): Promise<void> {
    if (!pairDatas || pairDatas.length === 0) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:pairAddress is empty, skipping`,
      );
      return;
    }

    await Promise.allSettled(
      pairDatas.map(async (data) => {
        const lock = await this.redisRepository.redlock.acquire(
          [data.pairAddress],
          10000,
        );
        try {
          await this.updatePumpfunDetailPair(data);
          await this.updatePumpfunDetailTimeframes(data.pairAddress);
        } catch (error) {
          this.logger.error(
            `updateMultiplePumpfunDetailsPair:Failed to update pair detail for ${data.pairAddress}: ${error.message}`,
          );
        } finally {
          await lock.release();
        }
      }),
    );
  }

  async getPairsByAddress(pairAddress: string): Promise<PairDto> {
    // Find Pair
    const pair =
      await this.pairDocumentRepository.findByPairAddress(pairAddress);
    if (!pair) {
      throw new PairNotFoundException('Pair not found');
    }

    // Find Token
    const token = await this.tokenService.getTokensByAddress(pair.tokenAddress);
    if (!token) {
      throw new TokenNotFoundException('Token not found');
    }

    // Find Pair Details
    const pairDetail =
      await this.pairDetailRepository.findOneByPairAddress(pairAddress);
    if (!pairDetail) {
      throw new PairNotFoundException('Pair Details not found');
    }

    // Find Pair Details Timeframes
    const timeIntervals: TimeInterval[] = Object.values(TimeInterval);
    const timeframes: Record<string, PairTimeFramesDto> = {};
    for (const interval of timeIntervals) {
      const pairDetailTimeFrames =
        await this.pairDetailTimeFramesRepository.findPairDetailsTimeframesByPairAddress(
          pairAddress,
          interval,
        );
      if (pairDetailTimeFrames) {
        timeframes[interval] = {
          buyVolumeUsd: pairDetailTimeFrames.buyVolumeUsd,
          sellVolumeUsd: pairDetailTimeFrames.sellVolumeUsd,
          numOfBuyers: pairDetailTimeFrames.numOfBuyers,
          numOfSellers: pairDetailTimeFrames.numOfSellers,
          numOfBuyTxs: pairDetailTimeFrames.numOfBuyTxs,
          numOfSellTxs: pairDetailTimeFrames.numOfSellTxs,
          updatedAt: pairDetailTimeFrames.updatedAt.getTime().toString(),
          ts: pairDetailTimeFrames.ts.getTime().toString(),
        };
      }
    }

    return {
      pairAddress: pairAddress,
      createdAt: pair.createdAt.toISOString(),
      tokenName: token.tokenName,
      tokenAddress: pair.tokenAddress,
      image: token.image,
      bannerImage: token.bannerImage ?? '',
      symbol: token.symbol,
      twitter: token.twitter ?? '',
      website: token.website ?? '',
      telegram: token.telegram ?? '',
      liquidityUsd: pairDetail?.liquidityUsd ?? 0,
      buyVolumeUsd: pairDetail?.buyVolumeUsd ?? 0,
      sellVolumeUsd: pairDetail?.sellVolumeUsd ?? 0,
      numOfBuyTxs: pairDetail?.numOfBuyTxs ?? 0,
      numOfSellTxs: pairDetail?.numOfSellTxs ?? 0,
      process: pairDetail?.process ?? 0,
      marketCapUsd: pairDetail?.marketCapUsd ?? 0,
      timeframes,
    };
  }

  async getRecentNewPairsWithin24h(
    limit: number,
    page = 1,
    sortBy?: string,
    sortDirection: 'asc' | 'desc' = 'desc',
  ): Promise<NewPairDto[]> {
    const since = Date.now() - 24 * 60 * 60 * 1000;
    const skip = (page - 1) * limit;

    const sortableFields = ['liquidity_usd', 'market_cap_usd'];
    const sortField = sortableFields.includes(sortBy || '') ? sortBy : 'ts';
    const direction =
      sortDirection === 'asc' ? SortDirection.ASC : SortDirection.DESC;

    const recentPairs = await this.pairDocumentRepository.findRecentPairsSince(
      since,
      limit,
      skip,
    );

    if (recentPairs.length === 0) return [];

    const pairAddresses = recentPairs.map((p) => p.pairAddress);
    const tokenAddresses = recentPairs.map((p) => p.tokenAddress);

    const [tokens, pairDetails] = await Promise.all([
      this.tokenService.getTokensByAddresses(tokenAddresses),
      this.pairDetailRepository.findLastestPairDetails(
        pairAddresses,
        sortField,
        direction,
      ),
    ]);

    const tokenMap = new Map(tokens.map((t) => [t.tokenAddress, t]));
    const pairMap = new Map(recentPairs.map((p) => [p.pairAddress, p]));

    return pairDetails
      .map((detail: PairDetail) => {
        const pair = pairMap.get(detail.pairAddress);
        const token = tokenMap.get(pair?.tokenAddress ?? '');
        if (!pair || !token) return null;

        return {
          pairAddress: pair.pairAddress,
          createdAt: pair.createdAt.toString(),

          tokenName: token.tokenName,
          tokenAddress: token.tokenAddress,
          image: token.image,
          twitter: token.twitter,
          website: token.website,
          telegram: token.telegram,
          symbol: token.symbol,

          liquidityUsd: detail.liquidityUsd,
          buyVolumeUsd: detail.buyVolumeUsd,
          sellVolumeUsd: detail.sellVolumeUsd,
          numOfBuyTxs: detail.numOfBuyTxs,
          numOfSellTxs: detail.numOfSellTxs,
          numOfBuyers: detail.numOfBuyers,
          numOfSellers: detail.numOfSellers,
          process: detail.process,
          marketCapUsd: detail.marketCapUsd,
        } as NewPairDto;
      })
      .filter((item) => item !== null);
  }

  private async updatePumpfunDetailPair(data: PairData): Promise<void> {
    const { tokenAddress, pairAddress } = data;
    const pairDetail =
      await this.pairDetailRepository.findOneByPairAddress(pairAddress);

    if (!pairDetail) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:pairDetail not found for pairAddress ${pairAddress}`,
      );
      return;
    }

    const updatedPairDetail = await this.populateUpdatedPairDetail(
      tokenAddress,
      pairAddress,
    );

    if (!updatedPairDetail) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:populateUpdatedPairDetail failed for pairAddress ${pairAddress}`,
      );
      return;
    }
    await this.pairDetailRepository.updatePairDetailPumpFun(updatedPairDetail);
  }

  private async updatePumpfunDetailTimeframes(pairAddress: string) {
    const updatedPairDetailTimeframes =
      await this.populateUpdatedPairDetailTimeframes(pairAddress);
    if (!updatedPairDetailTimeframes || !updatedPairDetailTimeframes.length) {
      this.logger.verbose(
        `updateMultiplePumpfunDetailsPair:updatePumpfunDetailTimeframes failed for pairAddress ${pairAddress}`,
      );
    }

    for (const updatedPairDetailTimeframe of updatedPairDetailTimeframes) {
      await this.pairDetailTimeFramesRepository.updatePairDetailTimeframesPumpFun(
        updatedPairDetailTimeframe,
      );
    }
  }

  private async populatePairDetail(
    pair: PairDocument,
  ): Promise<PairDetail | null> {
    try {
      const bondingCurve = new PublicKey(pair.pairAddress);
      const tokenAddress = pair.tokenAddress;

      let solPriceUsd = null;
      let accountInfoRaw = null;
      let rawPooledSol = null;
      let buySellCountsTxns: TxnsCount = null;
      let buySellVolume: VolumeResult = null;
      let buyerSeller: BuyerSellerResult = null;

      const results = await Promise.allSettled([
        this.solanaService.getSolPrice(),
        this.connection.getAccountInfo(bondingCurve),
        this.connection.getBalance(bondingCurve),

        this.swapTransactionService.getBuySellCountsTxnsByTokenAddress(
          tokenAddress,
        ),
        this.swapTransactionService.getBuySellVolumeByMint(tokenAddress),
        this.swapTransactionService.getAllPairBuyerAndSellerByPairAddress(
          bondingCurve.toBase58(),
        ),
        // this.swapTransactionPumpfunService.getVol24hByMint(tokenAddress),
      ]);

      if (results[0].status === 'fulfilled') solPriceUsd = results[0].value;
      if (results[1].status === 'fulfilled') accountInfoRaw = results[1].value;
      if (results[2].status === 'fulfilled') rawPooledSol = results[2].value;

      if (results[3].status === 'fulfilled') {
        buySellCountsTxns = results[3].value;
      }
      if (results[4].status === 'fulfilled') {
        buySellVolume = results[4].value;
      }
      if (results[5].status === 'fulfilled') {
        buyerSeller = results[5].value;
      }

      if (!accountInfoRaw) throw new Error('Account Info not found');

      const accountInfo = BONDING_CURVE_LAYOUT.decode(
        accountInfoRaw.data.slice(8),
      );
      const virtualTokenReserves = BigNumber(
        accountInfo.virtualTokenReserves.toString(),
      );
      const virtualSolReserves = BigNumber(
        accountInfo.virtualSolReserves.toString(),
      );
      const realTokenReserves = BigNumber(
        accountInfo.realTokenReserves.toString(),
      );

      const priceUsd = virtualSolReserves
        .div(virtualTokenReserves)
        .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
        .multipliedBy(solPriceUsd)
        .toNumber();

      const marketCapUsd = priceUsd * LAMPORTS_PER_SOL;
      const liquidityUsd = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .multipliedBy(new BigNumber(solPriceUsd))
        .toNumber();
      const process = BigNumber(1)
        .minus(
          BigNumber(100 / PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT)
            .multipliedBy(realTokenReserves)
            .div(100),
        )
        .dp(5)
        .toNumber();
      const pooledSol = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .toNumber();

      const blockTimeInMs = Date.parse(pair.createdAt.toString());
      if (isNaN(blockTimeInMs)) {
        this.logger.error('Failed to parse pair blockTime.');
        return;
      }

      return {
        pairAddress: pair.pairAddress,
        marketCapUsd,
        liquidityUsd,
        buyVolumeUsd: buySellVolume?.buyTotalVolume ?? 0,
        sellVolumeUsd: buySellVolume?.sellTotalVolume ?? 0,
        numOfBuyTxs: buySellCountsTxns?.buyTotalTxns ?? 0,
        numOfSellTxs: buySellCountsTxns?.sellTotalTxns ?? 0,
        numOfBuyers: buyerSeller?.numOfBuyers ?? 0,
        numOfSellers: buyerSeller?.numOfSellers ?? 0,
        process,
        priceUsd,
        pooledSol,
        createdAt: new Date(),
        updatedAt: new Date(),
        ts: blockTimeInMs,
      };
    } catch (error) {
      this.logger.error(`Error calculating pair detail: ${error.message}`);
      return null;
    }
  }

  private async populateUpdatedPairDetail(
    tokenAddress: string,
    pairAddress: string,
  ): Promise<PairDetail | undefined> {
    try {
      if (!pairAddress) {
        this.logger.warn(
          `populateUpdatedPairDetail:pairAddress is empty, skipping`,
        );
        return;
      }
      const bondingCurve = new PublicKey(pairAddress);
      const mintAccount = new PublicKey(tokenAddress).toBytes();

      const [associatedBondingCurve] = PublicKey.findProgramAddressSync(
        [bondingCurve.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), mintAccount],
        ASSOCIATED_TOKEN_PROGRAM_ID,
      );

      let solPriceUsd = null;
      let accountInfoRaw = null;
      let rawPooledSol = null;
      let _topHolderPercentages = null;
      let buySellCountsTxns: TxnsCount = null;
      let buySellVolume: VolumeResult = null;
      let buyerSeller: BuyerSellerResult = null;

      const results = await Promise.allSettled([
        this.solanaService.getSolPrice(),
        this.connection.getAccountInfo(bondingCurve),
        this.connection.getBalance(bondingCurve),
        this.calculateTopHolders(
          tokenAddress,
          associatedBondingCurve.toBase58(),
        ),
        this.swapTransactionService.getBuySellCountsTxnsByTokenAddress(
          tokenAddress,
        ),
        this.swapTransactionService.getBuySellVolumeByMint(tokenAddress),
        this.swapTransactionService.getAllPairBuyerAndSellerByPairAddress(
          bondingCurve.toBase58(),
        ),
        // this.swapTransactionPumpfunService.getVol24hByMint(tokenAddress),
      ]);

      if (results[0].status === 'fulfilled') solPriceUsd = results[0].value;
      if (results[1].status === 'fulfilled') accountInfoRaw = results[1].value;
      if (results[2].status === 'fulfilled') rawPooledSol = results[2].value;
      if (results[3].status === 'fulfilled')
        _topHolderPercentages = results[3].value; // TODO: Implement this field
      if (results[4].status === 'fulfilled') {
        buySellCountsTxns = results[4].value;
      }
      if (results[5].status === 'fulfilled') {
        buySellVolume = results[5].value;
      }
      if (results[6].status === 'fulfilled') {
        buyerSeller = results[6].value;
      }

      if (!accountInfoRaw) throw new Error('Account Info not found');
      const accountInfo = BONDING_CURVE_LAYOUT.decode(
        accountInfoRaw.data.slice(8),
      );

      const virtualTokenReserves = BigNumber(
        accountInfo.virtualTokenReserves.toString(),
      );
      const virtualSolReserves = BigNumber(
        accountInfo.virtualSolReserves.toString(),
      );
      const realTokenReserves = BigNumber(
        accountInfo.realTokenReserves.toString(),
      );
      const liquidityUsd = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .multipliedBy(new BigNumber(solPriceUsd))
        .toNumber();
      const process = BigNumber(1)
        .minus(
          BigNumber(100 / PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT)
            .multipliedBy(realTokenReserves)
            .div(100),
        )
        .dp(5)
        .toNumber();
      const priceUsd = virtualSolReserves
        .div(virtualTokenReserves)
        .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
        .multipliedBy(solPriceUsd)
        .toNumber();
      const marketCapUsd = priceUsd * LAMPORTS_PER_SOL;
      const pooledSol = new BigNumber(rawPooledSol)
        .dividedBy(new BigNumber(LAMPORTS_PER_SOL))
        .toNumber();

      return {
        pairAddress,
        pooledSol,
        priceUsd,
        marketCapUsd,
        process,
        liquidityUsd,
        buyVolumeUsd: buySellVolume?.buyTotalVolume ?? 0,
        sellVolumeUsd: buySellVolume?.sellTotalVolume ?? 0,
        numOfBuyTxs: buySellCountsTxns?.buyTotalTxns ?? 0,
        numOfSellTxs: buySellCountsTxns?.sellTotalTxns ?? 0,
        numOfBuyers: buyerSeller?.numOfBuyers ?? 0,
        numOfSellers: buyerSeller?.numOfSellers ?? 0,
        createdAt: undefined,
        updatedAt: undefined,
        ts: 0,
      };
    } catch (error) {
      this.logger.error(`Error calculating pair details: ${error.message}`);
      return null;
    }
  }

  private async populatePairDetailTimeframes(
    pairAddress: string,
  ): Promise<PairDetailTimeframes[] | null> {
    try {
      const pairDetailTimeframes: PairDetailTimeframes[] = [];
      const timeIntervals: TimeInterval[] = Object.values(TimeInterval);
      for (const interval of timeIntervals) {
        const pairDetailMetrics =
          await this.swapTransactionService.getAllPairDerailTimeframesByAddressAndTimeframes(
            pairAddress,
            interval,
          );

        if (!!pairDetailMetrics) {
          pairDetailTimeframes.push({
            pairAddress: pairAddress,
            buyVolumeUsd: pairDetailMetrics.buyVolumeUsd,
            numOfBuyTxs: pairDetailMetrics.numOfBuyTxs,
            numOfBuyers: pairDetailMetrics.numOfBuyers,
            numOfSellTxs: pairDetailMetrics.numOfSellTxs,
            numOfSellers: pairDetailMetrics.numOfSellers,
            sellVolumeUsd: pairDetailMetrics.sellVolumeUsd,
            timeInterval: interval,
            updatedAt: new Date(),
            ts: new Date(),
          });
        } else {
          pairDetailTimeframes.push({
            pairAddress: pairAddress,
            buyVolumeUsd: 0,
            numOfBuyTxs: 0,
            numOfBuyers: 0,
            numOfSellTxs: 0,
            numOfSellers: 0,
            sellVolumeUsd: 0,
            timeInterval: interval,
            updatedAt: new Date(),
            ts: new Date(),
          });
        }
      }
      return pairDetailTimeframes;
    } catch (error) {
      this.logger.error(`Error calculating pair detail: ${error.message}`);
      return null;
    }
  }

  private async populateUpdatedPairDetailTimeframes(
    pairAddress: string,
  ): Promise<PairDetailTimeframes[] | null> {
    try {
      const pairDetailTimeframes: PairDetailTimeframes[] = [];
      const timeIntervals: TimeInterval[] = Object.values(TimeInterval);
      for (const interval of timeIntervals) {
        const pairDetailMetrics =
          await this.swapTransactionService.getAllPairDerailTimeframesByAddressAndTimeframes(
            pairAddress,
            interval,
          );

        if (!!pairDetailMetrics) {
          pairDetailTimeframes.push({
            pairAddress: pairAddress,
            buyVolumeUsd: pairDetailMetrics.buyVolumeUsd,
            numOfBuyTxs: pairDetailMetrics.numOfBuyTxs,
            numOfBuyers: pairDetailMetrics.numOfBuyers,
            numOfSellTxs: pairDetailMetrics.numOfSellTxs,
            numOfSellers: pairDetailMetrics.numOfSellers,
            sellVolumeUsd: pairDetailMetrics.sellVolumeUsd,
            timeInterval: interval,
            updatedAt: new Date(),
            ts: new Date(),
          });
        }
      }
      return pairDetailTimeframes;
    } catch (error) {
      this.logger.error(`Error calculating pair detail: ${error.message}`);
      return null;
    }
  }

  private findBondingCurveByTokenAddress(tokenAddress: string) {
    const mintAccount = new PublicKey(tokenAddress).toBytes();
    const [bondingCurve] = PublicKey.findProgramAddressSync(
      [Buffer.from('bonding-curve'), mintAccount],
      new PublicKey(PUMPFUN_PROGRAM_ID),
    );
    return bondingCurve;
  }

  private async calculateTopHolders(
    tokenAddress: string,
    associatedBondingCurve: string,
  ) {
    const [holders, totalSupply] = await Promise.all([
      await this.solanaService.getTokenTopHolders(tokenAddress, 20),
      await this.solanaService.getTokenSupply(tokenAddress),
    ]);

    // Take top 10 and calculate percentage
    const holdersPercentage = holders
      .filter(
        (resultHolder) =>
          resultHolder.address.toString() !== associatedBondingCurve &&
          resultHolder.uiAmount > 0,
      )
      .slice(0, 10)
      .map((holder) => ({
        owner: holder.address,
        balance: holder.amount,
        decimals: holder.decimals,
        percentage: BigNumber(holder.amount).div(totalSupply.amount),
      }));

    return holdersPercentage
      .reduce((sum, holder) => sum.plus(holder.percentage), new BigNumber(0))
      .dp(5)
      .toNumber();
  }

  private async savePair(
    createPairDto: CreatePairDto,
  ): Promise<PairDocument | null> {
    const {
      tokenAddress,
      extra,
      pairAddress,
      signatureHash,
      creator,
      blockTime,
    } = createPairDto;

    try {
      const existPair =
        await this.pairDocumentRepository.findByTokenAddress(tokenAddress);

      if (existPair) {
        this.logger.debug(
          `Pair already exists for token address: ${tokenAddress}`,
        );
        return null;
      }

      const pairToCreate: Partial<PairSchemaClass> = {
        tokenAddress,
        associatedBondingCurve: extra.associatedBondingCurve,
        pairAddress,
        signatureHash,
        creator,
        createdAt: blockTime,
      };

      const savedPair = await this.pairDocumentRepository.create(
        pairToCreate as PairSchemaClass,
      );

      this.logger.log(
        `[SUCCESS] Completed storing pair for token: ${tokenAddress}`,
      );

      return PairDocumentMapper.toDomain(savedPair);
    } catch (error) {
      this.logger.error(
        `[FAILURE] Failed to save pair: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }
}
