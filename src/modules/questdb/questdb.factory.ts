import { Client } from 'pg';
import { ConfigService } from '@nestjs/config';
import { Sender } from '@questdb/nodejs-client';

export const createQuestdbPgClient = async (
  configService: ConfigService,
): Promise<Client> => {
  const cfg = configService.get('questdbPg');
  const client = new Client({
    host: cfg.host,
    port: cfg.port,
    user: cfg.user,
    password: cfg.password,
    database: cfg.database,
  });
  await client.connect();
  return client;
};

export const createQuestdbSender = (configService: ConfigService): Sender => {
  const cfg = configService.get('questdbSender');

  const protocol =
    cfg.mode === 'http'
      ? `http::addr=${cfg.host}:${cfg.port}`
      : `tcp::addr=${cfg.host}:${cfg.port}`;

  return Sender.fromConfig(protocol);
};
