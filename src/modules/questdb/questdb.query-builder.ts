import { LAMPORTS_PER_SOL } from '@solana/web3.js';
import { QuestDbTable } from 'src/common/constant/questdb.constants';
import {
  PairDetailTimeframes,
  TimeInterval,
} from '../pair/domain/pair-detail-timeframes';
import { SortDirection } from '../../common/constant/sort-direction';
import { PairDetail } from '../pair/domain/pair-detail';

export const buildBuySellVolumeQuery = (tokenAddress: string) => ({
  text: `
    SELECT trade_type,
           SUM((sol_amount / $1) * sol_price_usd) AS total_volume
    FROM ${QuestDbTable.SWAP_TRANSACTION}
    WHERE token_address = $2
    GROUP BY trade_type;
  `,
  values: [LAMPORTS_PER_SOL, tokenAddress],
});

export const buildBuySellCountsQuery = (tokenAddress: string) => ({
  text: `
    SELECT trade_type,
           COUNT(*) AS total_count
    FROM ${QuestDbTable.SWAP_TRANSACTION}
    WHERE token_address = $1
    GROUP BY trade_type;
  `,
  values: [tokenAddress],
});

export const buildVolume24hQuery = (tokenAddress: string) => ({
  text: `
    SELECT SUM((sol_amount / $1) * sol_price_usd) AS volume24h
    FROM ${QuestDbTable.SWAP_TRANSACTION}
    WHERE token_address = $2
      AND ts > dateadd('h', -24, now());
  `,
  values: [LAMPORTS_PER_SOL, tokenAddress],
});

export const buildGetPairDetailByAddressQuery = (pairAddress: string) => ({
  text: `
    SELECT *
    FROM ${QuestDbTable.PAIR_DETAIL}
    WHERE pair_address = $1;
  `,
  values: [pairAddress],
});

export const buildCheckPairExistsQuery = (pairAddress: string) => ({
  text: `
    SELECT 1
    FROM ${QuestDbTable.PAIR_DETAIL}
    WHERE pair_address = $1 LIMIT 1;
  `,
  values: [pairAddress],
});

export const buildUpdatePairDetailQuery = (pair: PairDetail) => ({
  text: `
    UPDATE ${QuestDbTable.PAIR_DETAIL}
    SET market_cap_usd  = $1,
        liquidity_usd   = $2,
        buy_volume_usd  = $3,
        sell_volume_usd = $4,
        num_of_buy_txs  = $5,
        num_of_sell_txs = $6,
        num_of_buyers   = $7,
        num_of_sellers  = $8,
        price_usd       = $9,
        pooled_sol      = $10
    WHERE pair_address = $11;
  `,
  values: [
    Number(pair.marketCapUsd),
    Number(pair.liquidityUsd),
    Number(pair.buyVolumeUsd),
    Number(pair.sellVolumeUsd),
    Number(pair.numOfBuyTxs),
    Number(pair.numOfSellTxs),
    Number(pair.numOfBuyers),
    Number(pair.numOfSellers),
    Number(pair.priceUsd),
    Number(pair.pooledSol),
    pair.pairAddress,
  ],
});

export const buildLatestPairDetailsQuery = (
  pairAddresses: string[],
  sortField: string,
  direction: SortDirection,
) => {
  const fieldValidated = ['liquidity_usd', 'market_cap_usd'].includes(sortField)
    ? sortField
    : 'liquidity_usd';

  const addressPlaceholders = pairAddresses
    .map((_, i) => `$${i + 1}`)
    .join(', ');

  return {
    text: `
      SELECT *
      FROM ${QuestDbTable.PAIR_DETAIL}
      WHERE pair_address IN (${addressPlaceholders})
        AND ts > dateadd('h', -24, now())
        LATEST
      ON ts PARTITION BY pair_address
      ORDER BY ${fieldValidated} ${direction}
    `,
    values: pairAddresses,
  };
};

export const buildGetPairDetailTimeframesByAddressAndTimeIntervalQuery = (
  pairAddress: string,
  interval: string,
) => {
  return {
    text: `SELECT *
           FROM ${QuestDbTable.PAIR_DETAIL_TIMEFRAMES}
           WHERE pair_address = $1 AND time_interval = $2
           LATEST ON ts PARTITION BY pair_address
    `,

    values: [pairAddress, interval],
  };
};

export const buildGetPairDetailsTimeframesAllByAddressAndTimeframes = (
  pairAddress: string,
  timeInterval: TimeInterval,
) => {
  const { unit, value } = getDateAddParams(timeInterval);

  return {
    text: `

      SELECT pair_address,
             SUM(CASE WHEN trade_type = 'BUY' THEN sol_amount / $1 * sol_price_usd ELSE 0 END) AS buy_volume_usd,
             COUNT(CASE WHEN trade_type = 'BUY' THEN 1 ELSE NULL END) AS num_of_buy_txs,
             COUNT(DISTINCT CASE WHEN trade_type = 'BUY' THEN user_public_key ELSE NULL END) AS num_of_buyers,
             SUM(CASE WHEN trade_type = 'SELL' THEN sol_amount / $1 * sol_price_usd ELSE 0 END) AS sell_volume_usd,
             COUNT(CASE WHEN trade_type = 'SELL' THEN 1 ELSE NULL END) AS num_of_sell_txs,
             COUNT(DISTINCT CASE WHEN trade_type = 'SELL' THEN user_public_key ELSE NULL END) AS num_of_sellers
      FROM swap_transaction
      WHERE ts > dateadd($2, $3, now()) AND pair_address = $4;
    `,
    values: [LAMPORTS_PER_SOL, unit, value, pairAddress],
  };
};

export const buildGetAllPairBuyerAndSellerByPairAddress = (
  pairAddress: string,
) => {
  return {
    text: `
      SELECT pair_address,
             COUNT(DISTINCT CASE WHEN trade_type = 'BUY' THEN user_public_key ELSE NULL END)  AS num_of_buyers,
             COUNT(DISTINCT CASE WHEN trade_type = 'SELL' THEN user_public_key ELSE NULL END) AS num_of_sellers
      FROM swap_transaction
      WHERE pair_address = $1;
    `,
    values: [pairAddress],
  };
};

export const buildUpdatePairDetailTimeframesQuery = (
  updatedPairDetailTimeframes: PairDetailTimeframes,
) => ({
  text: `
    UPDATE ${QuestDbTable.PAIR_DETAIL_TIMEFRAMES}
    SET buy_volume_usd  = $1,
        sell_volume_usd = $2,
        num_of_buy_txs  = $3,
        num_of_sell_txs = $4,
        num_of_sellers  = $5,
        num_of_buyers   = $6,
        updated_at      = $7
    WHERE pair_address = $8 AND time_interval = $9;
  `,
  values: [
    Number(updatedPairDetailTimeframes.buyVolumeUsd),
    Number(updatedPairDetailTimeframes.sellVolumeUsd),
    Number(updatedPairDetailTimeframes.numOfBuyTxs),
    Number(updatedPairDetailTimeframes.numOfSellTxs),
    Number(updatedPairDetailTimeframes.numOfSellers),
    Number(updatedPairDetailTimeframes.numOfBuyers),
    updatedPairDetailTimeframes.updatedAt.getTime() * 1_000,
    updatedPairDetailTimeframes.pairAddress,
    updatedPairDetailTimeframes.timeInterval,
  ],
});

function getDateAddParams(interval: TimeInterval): {
  unit: string;
  value: number;
} {
  switch (interval) {
    case TimeInterval.TWENTY_FOUR_HOUR:
      return { unit: 'h', value: -24 };
    case TimeInterval.SIX_HOUR:
      return { unit: 'h', value: -6 };
    case TimeInterval.ONE_HOUR:
      return { unit: 'h', value: -1 };
    case TimeInterval.FIVE_MINUTES:
      return { unit: 'm', value: -5 };
    default:
      throw new Error(`Unsupported interval: ${interval}`);
  }
}

export const buildGetOHCLUsdQuery = (
  pairAddress: string,
  startTime: number,
  endTime: number,
  interval: string,
) => {
  return {
    text: `
      SELECT
        MIN(SOL_PRICE_USD*PAIR_PRICE_SOL) AS low,
        MAX(SOL_PRICE_USD*PAIR_PRICE_SOL) AS high,
        FIRST(SOL_PRICE_USD*PAIR_PRICE_SOL) AS open,
        LAST(SOL_PRICE_USD*PAIR_PRICE_SOL) AS close,
        ts
      FROM SWAP_TRANSACTION
      WHERE PAIR_ADDRESS = $1
        SAMPLE BY ${interval} FROM $2 TO $3 FILL(PREV);
    `,
    values: [pairAddress, startTime, endTime],
  };
};

export const buildGetOHCLSolQuery = (
  pairAddress: string,
  startTime: number,
  endTime: number,
  interval: string,
) => {
  return {
    text: `
      SELECT
        MIN(PAIR_PRICE_SOL) AS low,
        MAX(PAIR_PRICE_SOL) AS high,
        FIRST(PAIR_PRICE_SOL) AS open,
        LAST(PAIR_PRICE_SOL) AS close,
        ts
      FROM SWAP_TRANSACTION
      WHERE PAIR_ADDRESS = $1
        SAMPLE BY ${interval} FROM $2 TO $3 FILL(PREV);
    `,
    values: [pairAddress, startTime, endTime],
  };
};
