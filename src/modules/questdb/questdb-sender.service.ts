import { Inject, Injectable, OnModuleDestroy } from '@nestjs/common';
import { Sender } from '@questdb/nodejs-client';
import { SwapTransaction } from '../swap-transaction/domain/swap-transaction';
import { QuestDbTable } from 'src/common/constant/questdb.constants';
import { PairDetailTimeframes } from '../pair/domain/pair-detail-timeframes';
import { PairDetail } from '../pair/domain/pair-detail';

@Injectable()
export class QuestdbSenderService implements OnModuleDestroy {
  constructor(
    @Inject('QUESTDB_SENDER')
    private readonly sender: Sender,
  ) {}

  async createSwapTransaction(tx: SwapTransaction) {
    await this.sender
      .table(QuestDbTable.SWAP_TRANSACTION)
      .symbol('trade_type', tx.tradeType || '')
      .stringColumn('token_address', tx.tokenAddress || '')
      .stringColumn('pair_address', tx.pairAddress || '')
      .stringColumn('user_public_key', tx.userPublicKey || '')
      .stringColumn('signature', tx.signature || '')
      .intColumn('sol_amount', tx.solAmount)
      .intColumn('token_amount', tx.tokenAmount)
      .floatColumn('pair_price_sol', tx.pairPriceSol)
      .floatColumn('sol_price_usd', tx.solPriceUsd)
      .intColumn('block', tx.block)
      .at(tx.timestamp * 1_000_000);
  }

  async createPairDetail(pair: PairDetail): Promise<void> {
    await this.sender
      .table(QuestDbTable.PAIR_DETAIL)
      .stringColumn('pair_address', pair.pairAddress)
      .floatColumn('market_cap_usd', pair.marketCapUsd)
      .floatColumn('liquidity_usd', Number(pair.liquidityUsd))
      .floatColumn('buy_volume_usd', pair.buyVolumeUsd)
      .floatColumn('sell_volume_usd', pair.sellVolumeUsd)
      .intColumn('num_of_buy_txs', pair.numOfBuyTxs)
      .intColumn('num_of_sell_txs', pair.numOfSellTxs)
      .intColumn('num_of_buyers', pair.numOfBuyers)
      .intColumn('num_of_sellers', pair.numOfSellers)
      .floatColumn('price_usd', pair.priceUsd)
      .floatColumn('pooled_sol', Number(pair.pooledSol))
      .floatColumn('process', pair.process)
      .timestampColumn('created_at', pair.createdAt.getTime() * 1_000)
      .timestampColumn('updated_at', pair.updatedAt.getTime() * 1_000)
      .at(pair.ts * 1_000); // microseconds
  }

  async queuePairDetailTimeframes(
    pairDetailTimeframes: PairDetailTimeframes,
  ): Promise<void> {
    await this.sender
      .table(QuestDbTable.PAIR_DETAIL_TIMEFRAMES)
      .symbol('time_interval', pairDetailTimeframes.timeInterval || '')
      .stringColumn('pair_address', pairDetailTimeframes.pairAddress || '')
      .floatColumn('buy_volume_usd', pairDetailTimeframes.buyVolumeUsd)
      .floatColumn('sell_volume_usd', pairDetailTimeframes.sellVolumeUsd)
      .intColumn('num_of_buy_txs', pairDetailTimeframes.numOfBuyTxs)
      .intColumn('num_of_sell_txs', pairDetailTimeframes.numOfSellTxs)
      .intColumn('num_of_buyers', pairDetailTimeframes.numOfBuyers)
      .intColumn('num_of_sellers', pairDetailTimeframes.numOfSellers)
      .timestampColumn(
        'updated_at',
        pairDetailTimeframes.updatedAt.getTime() * 1_000,
      )
      .atNow();
  }

  async flush() {
    await this.sender.flush();
  }

  async onModuleDestroy() {
    await this.sender.close();
  }
}
