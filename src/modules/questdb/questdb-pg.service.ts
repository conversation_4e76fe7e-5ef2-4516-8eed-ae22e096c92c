import { Inject, Injectable, OnModuleDestroy } from '@nestjs/common';
import { Client } from 'pg';

@Injectable()
export class QuestdbPgService implements OnModuleDestroy {
  constructor(@Inject('QUESTDB_PG_CLIENT') private readonly client: Client) {}

  async query<T = any[]>(sql: string, params?: any[]): Promise<T>;
  async query<T = any[]>(config: {
    name?: string;
    text: string;
    values?: any[];
  }): Promise<T>;
  async query<T = any[]>(sqlOrConfig: any, params?: any[]): Promise<T> {
    const res =
      typeof sqlOrConfig === 'string'
        ? await this.client.query(sqlOrConfig, params)
        : await this.client.query(sqlOrConfig);

    return res.rows;
  }

  async onModuleDestroy() {
    await this.client.end();
  }
}
