import { Module } from '@nestjs/common';
import { QuestdbSenderService } from './questdb-sender.service';
import { QuestdbPgService } from './questdb-pg.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { createQuestdbPgClient, createQuestdbSender } from './questdb.factory';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'QUESTDB_PG_CLIENT',
      useFactory: createQuestdbPgClient,
      inject: [ConfigService],
    },
    {
      provide: 'QUESTDB_SENDER',
      useFactory: createQuestdbSender,
      inject: [ConfigService],
    },
    QuestdbPgService,
    QuestdbSenderService,
  ],
  exports: [
    QuestdbSenderService,
    QuestdbPgService,
    'QUESTDB_SENDER',
    'QUESTDB_PG_CLIENT',
  ],
})
export class QuestdbModule {}
