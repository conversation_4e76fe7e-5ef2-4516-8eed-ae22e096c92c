import { Injectable, Logger } from '@nestjs/common';
import { KafkaMessage } from '../types/kafka.type';

@Injectable()
export class PumpswapHandler {
  private readonly logger = new Logger(PumpswapHandler.name);

  constructor() {}

  handlePumpSwapTransactions(data: KafkaMessage) {
    const { slot, transactionsInfo = [] } = data;

    const transactions = transactionsInfo.map(
      (transactionInfo) => transactionInfo.transaction,
    );

    this.logger.verbose(
      `Handling PumpSwap, slot [${slot}], found transactions [${transactions.length}]`,
    );

    try {
      // TODO: Handle below
      // const parsedTransactions = transactions.map(
      //   (transaction: TransactionData) => parsePumpswapTransaction(transaction),
      // );
      // await Promise.allSettled([
      //   this.swapTransactionPumpswapService.handleMultipleSwapPumpSwap(
      //     parsedTransactions,
      //     slot,
      //   ),
      //   ...parsedTransactions.map(async (transaction: any) => {
      //     await this.poolService.handlePumpSwapPool(transaction);
      //   }),
      // ]);
    } catch (error) {
      this.logger.warn(
        `Fail to handle PumpSwap data from grpc, ${error.message}`,
      );
      this.logger.error(error.stack);
    }
  }
}
