import { ConfigService } from '@nestjs/config';
import { KafkaOptions, Transport } from '@nestjs/microservices';
import { KafkaConfig } from '../../config/kafka.config';

export function createKafkaConfig(configService: ConfigService): KafkaOptions {
  return {
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: configService.getOrThrow<KafkaConfig>('kafka').clientId,
        brokers: configService.getOrThrow<KafkaConfig>('kafka').brokers,
        sasl: configService.getOrThrow<KafkaConfig>('kafka').sasl,
      },
      consumer: {
        groupId:
          configService.getOrThrow<KafkaConfig>('kafka').consumer.groupId,
        allowAutoTopicCreation: false,
      },
      producer: {
        allowAutoTopicCreation: false,
      },
    },
  };
}
