import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { KafkaConsumerController } from './kafka.consumer.controller';
import { PairModule } from 'src/modules/pair/pair.module';
import { PumpfunHandler } from './handlers/pumpfun.handler';
import { ClientsModule } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { createKafkaConfig } from './kafka.factory';
import { KAFKA_CLIENT_NAME } from '../../common/constant/kafka';
import { PumpswapHandler } from './handlers/pumpswap.handler';
import { SwapTransactionModule } from '../swap-transaction/swap-transaction.module';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: KAFKA_CLIENT_NAME,
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: createKafkaConfig,
      },
    ]),
    SwapTransactionModule,
    PairModule,
  ],
  controllers: [KafkaConsumerController],
  providers: [PumpfunHandler, PumpswapHandler],
})
export class KafkaConsumerModule {}
