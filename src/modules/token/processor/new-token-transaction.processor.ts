import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../queue/worker-host.processor';
import { Job } from 'bullmq';
import { TokenService } from '../token.service';
import { CreateTokenRequestDto } from '../dto/create-token-request.dto';
import { QueueHandleProcessException } from 'src/common/exceptions/queue-handle-process-exception';
import { TokenErrorMessage } from 'src/common/constant/errors';
import { TokenDocument } from '../domain/token-document';

@Processor(PumpFunQueue.NEW_TOKEN, {
  concurrency: 100_000,
})
@Injectable()
export class NewTokenTransactionProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly tokenService: TokenService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<CreateTokenRequestDto, TokenDocument, string>,
  ): Promise<TokenDocument> {
    try {
      const begin = performance.now();
      const tokenDataDto = job.data;

      const savedToken =
        await this.tokenService.createPumpFunToken(tokenDataDto);
      this.logger.log(
        `[SUCCESS] Processed token ${tokenDataDto.tokenAddress}, elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );
      return savedToken;
    } catch (error) {
      this.logger.error(
        `${PumpFunQueue.NEW_TOKEN} handleProcess error: ${error.message}`,
        error.stack,
      );
      throw new QueueHandleProcessException(
        TokenErrorMessage.ERROR_QUEUE_NEW_TOKEN,
      );
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<CreateTokenRequestDto, TokenDocument, string>) {
    const { id, queueName, data } = job;
    this.logger.verbose(
      `Job id: ${id}, Completed queue ${queueName}, token [${data.tokenAddress}].`,
    );
  }
}
