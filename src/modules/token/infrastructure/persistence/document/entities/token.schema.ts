import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, now } from 'mongoose';

export type TokenSchemaDocument = TokenSchemaClass & Document;

@Schema({
  collection: 'token',
})
export class TokenSchemaClass {
  @Prop({ required: true })
  tokenAddress: string;

  @Prop()
  tokenName: string;

  @Prop()
  symbol: string;

  @Prop()
  description: string;

  @Prop()
  image: string;

  @Prop()
  bannerImage: string;

  @Prop()
  decimals: number;

  @Prop()
  twitter?: string;

  @Prop()
  website?: string;

  @Prop()
  telegram?: string;

  @Prop({ default: now })
  createdAt?: Date;

  @Prop({ default: now })
  updatedAt?: Date;
}

export const TokenSchema = SchemaFactory.createForClass(TokenSchemaClass);
