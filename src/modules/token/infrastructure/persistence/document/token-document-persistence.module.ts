import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TokenSchema, TokenSchemaClass } from './entities/token.schema';
import { TokenDocumentRepository } from './repositories/token-document.repository';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: TokenSchemaClass.name, schema: TokenSchema },
    ]),
  ],
  providers: [TokenDocumentRepository],
  exports: [TokenDocumentRepository],
})
export class TokenDocumentPersistenceModule {}
