import { Module } from '@nestjs/common';
import { TokenService } from './token.service';
import { NewTokenTransactionProcessor } from './processor/new-token-transaction.processor';
import { RedisModule } from '../redis/redis.module';
import { QueueModule } from '../queue/queue.module';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { QueueConfig } from '../queue/queue.interface';
import { DefaultJobOptions } from 'bullmq';
import { TokenDocumentPersistenceModule } from './infrastructure/persistence/document/token-document-persistence.module';

@Module({
  imports: [
    RedisModule,
    QueueModule.register({
      queues: [
        {
          name: PumpFunQueue.NEW_TOKEN,
          defaultJobOptions: {
            attempts: 5,
            backoff: {
              type: 'exponential',
              delay: 1000,
            },
            removeOnComplete: true,
            removeOnFail: false,
          } as DefaultJobOptions,
        } as QueueConfig,
      ],
    }),
    TokenDocumentPersistenceModule,
  ],
  providers: [TokenService, NewTokenTransactionProcessor],
  exports: [TokenService],
})
export class TokenModule {}
