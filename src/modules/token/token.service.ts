import { Inject, Injectable, Logger } from '@nestjs/common';
import bs58 from 'bs58';
import { InjectQueue } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { Queue } from 'bullmq';
import { PUMP_FUN_NEW_TOKEN_TRANSACTION_JOB } from '../../common/constant/job-name';
import { CreateTokenRequestDto } from './dto/create-token-request.dto';
import { TokenDocumentRepository } from './infrastructure/persistence/document/repositories/token-document.repository';
import { TokenSchemaClass } from './infrastructure/persistence/document/entities/token.schema';
import { TokenDocument } from './domain/token-document';
import { PUMP_FUN_TOKEN_DATA_LAYOUT } from '../../common/layout/create-pump-fun-token-data';

@Injectable()
export class TokenService {
  private readonly logger: Logger = new Logger(TokenService.name);

  constructor(
    @Inject()
    private readonly tokenRepository: TokenDocumentRepository,
    @InjectQueue(PumpFunQueue.NEW_TOKEN)
    private readonly newTokenQueue: Queue,
  ) {}

  async handlePumpFunToken(
    tokenAddress: string,
    decimals: number,
    createdAt: Date,
    createPairInstruction?: any,
  ) {
    try {
      const bufferData = bs58.decode(createPairInstruction);
      // Remove first 8 bytes for event cpi
      const tokenInfo = PUMP_FUN_TOKEN_DATA_LAYOUT.decode(
        Buffer.from(bufferData),
      );

      await this.newTokenQueue.add(PUMP_FUN_NEW_TOKEN_TRANSACTION_JOB, {
        name: tokenInfo.name,
        symbol: tokenInfo.symbol,
        decimals: decimals,
        descriptionUri: tokenInfo.uri,
        createdAt: createdAt,
        tokenAddress: tokenAddress,
      } as CreateTokenRequestDto);
    } catch (err) {
      this.logger.error(
        `Failed to save token for address: ${tokenAddress} with reason: ${err.message}`,
      );
      this.logger.debug(err.stack);
    }
  }

  async createPumpFunToken(
    tokenDto: CreateTokenRequestDto,
  ): Promise<TokenDocument> {
    let tokenDescription = '';
    let tokenImage = '';
    let twitter = '';
    let website = '';
    let telegram = '';

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      try {
        const response = await fetch(tokenDto.descriptionUri, {
          signal: controller.signal,
        });
        clearTimeout(timeoutId);

        if (response.ok) {
          const responseObj = await response.json();

          tokenDescription = responseObj.description ?? '';
          tokenImage = responseObj.image ?? '';
          twitter = responseObj.twitter ?? '';
          website = responseObj.website ?? '';
          telegram = responseObj.telegram ?? '';
        } else {
          this.logger.warn(
            `[WARN] Description URI returned non-OK response: ${response.status} — ${tokenDto.descriptionUri}`,
          );
        }
      } finally {
        clearTimeout(timeoutId);
      }
    } catch (error) {
      this.logger.warn(
        `[WARN] Failed to fetch token description from ${tokenDto.descriptionUri}: ${error.message}`,
        error.stack,
      );
    }

    const tokenData: TokenSchemaClass = {
      tokenAddress: tokenDto.tokenAddress,
      tokenName: tokenDto.name,
      symbol: tokenDto.symbol,
      description: tokenDescription,
      image: tokenImage,
      decimals: tokenDto.decimals,
      twitter,
      website,
      telegram,
      bannerImage: '', // Always default on create because it does not exist on chain
      createdAt: tokenDto.createdAt,
    };

    try {
      const token = await this.tokenRepository.create(tokenData);
      this.logger.log(`[SUCCESS] Stored token: ${tokenDto.tokenAddress}`);
      return token;
    } catch (err) {
      this.logger.error(
        `[FAILURE] Failed to store token ${tokenDto.tokenAddress}: ${err.message}`,
        err.stack,
      );
      throw err;
    }
  }

  async getTokensByAddresses(
    tokenAddresses: string[],
  ): Promise<TokenDocument[]> {
    return await this.tokenRepository.findByTokenAddresses(tokenAddresses);
  }

  async getTokensByAddress(tokenAddresses: string): Promise<TokenDocument> {
    return await this.tokenRepository.findByTokenAddress(tokenAddresses);
  }
}
