import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  TokenAccountBalancePair,
  TokenAmount,
} from '@solana/web3.js';
import { RedisRepository } from '../redis/redis.repository';
import { <PERSON>ache<PERSON><PERSON>, RedisPrefix } from '../../common/constant/redis.prefix';
import { USDC_SOL } from '../../common/constant/onchainPubkey';
import { LIQUIDITY_STATE_LAYOUT_V4 } from '../../common/constant/structType';
import BigNumber from 'bignumber.js';

@Injectable()
export class SolanaService {
  private readonly logger = new Logger(SolanaService.name);

  constructor(
    @Inject(Connection) private readonly connection: Connection,
    @Inject(RedisRepository) private readonly redisRepository: RedisRepository,
  ) {}

  async getSolPrice() {
    try {
      const solPrice = await this.redisRepository.get(
        RedisPrefix.CACHE,
        CacheKey.SOL_PRICE_USD,
      );
      if (solPrice) {
        return parseFloat(solPrice);
      }
      const account = await this.connection.getAccountInfo(
        new PublicKey(USDC_SOL),
      );
      if (account) {
        const info = LIQUIDITY_STATE_LAYOUT_V4.decode(account.data);
        const baseVault = info.baseVault.toString();
        const quoteVault = info.quoteVault.toString();
        this.logger.debug(
          `Call getBalance, getSolPrice(), wallet [${quoteVault}`,
        );
        const balanceProcess = await Promise.all([
          this.connection.getBalance(new PublicKey(baseVault)),
          this.connection.getTokenAccountBalance(new PublicKey(quoteVault)),
        ]);
        const balance = BigNumber(balanceProcess[0])
          .div(LAMPORTS_PER_SOL)
          .toNumber();
        const tokenAccountBalance = balanceProcess[1];
        const total = Number(tokenAccountBalance.value.uiAmount);
        await this.redisRepository.setWithExpiry(
          RedisPrefix.CACHE,
          CacheKey.SOL_PRICE_USD,
          BigNumber(total).div(balance).toNumber().toString(),
          120,
        );
        return BigNumber(total).div(balance).toNumber();
      }
      return 0;
    } catch (error: any) {
      this.logger.error(`Error in getSolPrice ${JSON.stringify(error.stack)}`);
      return 0;
    }
  }

  async getTokenSupply(tokenAddress: string): Promise<TokenAmount> {
    try {
      const { value } = await this.connection.getTokenSupply(
        new PublicKey(tokenAddress),
      );

      return value;
    } catch (err) {
      this.logger.error(
        `Fail to get Token supply - ${tokenAddress} - ${JSON.stringify(err)}`,
      );
      return { amount: '0', decimals: 0, uiAmount: 0, uiAmountString: '0' };
    }
  }

  async getTokenTopHolders(
    tokenAddress: string,
    maxHolder: number = 20,
  ): Promise<TokenAccountBalancePair[]> {
    try {
      const { value } = await this.connection.getTokenLargestAccounts(
        new PublicKey(tokenAddress),
      );

      return value.sort((a, b) => b.uiAmount - a.uiAmount).slice(0, maxHolder);
    } catch (err) {
      this.logger.error(
        `Fail to get Token - ${tokenAddress} - top 20 largest Accounts: ${JSON.stringify(err)}`,
      );

      return [];
    }
  }
}
