import { Injectable, Logger } from '@nestjs/common';
import { SwapTransactionPumpfunRepository } from '../../pumpfun/repositories/swap-transaction-pumpfun.repository';
import { QuestdbPgService } from '../../../questdb/questdb-pg.service';
import {
  buildGetOHCLSolQuery,
  buildGetOHCLUsdQuery,
} from '../../../questdb/questdb.query-builder';
import { SwapTransactionData } from '../interfaces/transaction-data.interface';
import { ChartType } from '../enums/chart-type.enum';

@Injectable()
export class CandlestickRepository {
  private readonly logger = new Logger(SwapTransactionPumpfunRepository.name);

  constructor(private readonly questdbPgService: QuestdbPgService) {}

  async getTransactionData(
    pairAddress: string,
    startTime: number,
    endTime: number,
    interval: number,
    chartType: ChartType,
  ) {
    let query: { text: string; values: (string | number)[] };
    switch (chartType) {
      case ChartType.SOL:
        query = buildGetOHCLSolQuery(
          pairAddress,
          startTime * 1000000,
          endTime * 1000000,
          `${interval}s`,
        );
        break;
      case ChartType.USD:
        query = buildGetOHCLUsdQuery(
          pairAddress,
          startTime * 1000000,
          endTime * 1000000,
          `${interval}s`,
        );
        break;
      default:
        throw new Error(`Unsupported chart type: ${chartType}`);
    }
    return await this.questdbPgService.query<SwapTransactionData[]>(query);
  }
}
