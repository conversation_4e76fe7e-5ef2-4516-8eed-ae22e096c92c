import { forwardRef, Module } from '@nestjs/common';
import { SwapTransactionService } from './swap-transaction.service';
import { QueueModule } from '../queue/queue.module';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { DefaultJobOptions } from 'bullmq';
import { QueueConfig } from '../queue/queue.interface';
import { RedisModule } from '../redis/redis.module';
import { PairModule } from '../pair/pair.module';
import { SolanaModule } from '../solana/solana.module';
import { QuestdbModule } from '../questdb/questdb.module';
import { NewMultipleTradeTransactionProcessor } from './processor/new-multiple-trade-transaction.processor';
import { QuestdbSenderService } from '../questdb/questdb-sender.service';
import { SwapTransactionTimeSeriesPersistenceModule } from './infrastructure/persistence/time-series/time-series-persistence.module';

@Module({
  imports: [
    SwapTransactionTimeSeriesPersistenceModule,
    QueueModule.register({
      queues: [PumpFunQueue.NEW_MULTIPLE_TRADE_TRANSACTION].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    RedisModule,
    forwardRef(() => PairModule),
    SolanaModule,
    QuestdbModule,
  ],
  providers: [
    SwapTransactionService,
    NewMultipleTradeTransactionProcessor,
    QuestdbSenderService,
  ],
  exports: [SwapTransactionService],
})
export class SwapTransactionModule {}
