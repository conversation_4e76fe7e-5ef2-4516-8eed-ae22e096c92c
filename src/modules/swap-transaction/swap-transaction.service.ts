import { Inject, Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON>h<PERSON>oder, Event, EventParser, Idl } from '@coral-xyz/anchor';
import PumpFunIDL from '../../common/idl/pumpfun.json';
import { PublicKey } from '@solana/web3.js';
import {
  PUMPFUN_AND_SOL_DIFFERENTIAL,
  PUMPFUN_PROGRAM_ID,
  PUMPFUN_TRADE_LOG_MESSAGE,
  PumpfunEvent,
} from '../../common/constant/pumpfun';
import { CreateSwapTransactionRequestDto } from './dto/create-swap-transaction-request.dto';
import { TradeType } from '../../common/constant/trade-type';
import BigNumber from 'bignumber.js';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { PUMP_FUN_NEW_TRADE_TRANSACTION_JOB } from '../../common/constant/job-name';
import { TradeEventDataDto } from './dto/trade-event-data.dto';
import { TransactionData } from '../../common/type/transaction.data';
import { SolanaService } from '../solana/solana.service';
import { NewMultipleTradeTrxRequestDto } from './dto/new-multiple-trade-trx-request.dto';
import { QuestdbSenderService } from '../questdb/questdb-sender.service';
import { SwapTransaction } from './domain/swap-transaction';
import { VolumeResult } from './dto/volume-result.dto';
import { TxnsCount } from './dto/txns-count.dto';
import { FailedCreateSwapTransaction } from './dto/swap-transaction.dto';
import { findBondingCurveByTokenAddress } from 'src/modules/kafka/utils/solana-parser.util';
import { TimeInterval } from '../pair/domain/pair-detail-timeframes';
import { SwapTransactionRepository } from './infrastructure/persistence/time-series/repositories/swap-transaction.repository';

@Injectable()
export class SwapTransactionService {
  private readonly logger = new Logger(SwapTransactionService.name);

  constructor(
    @Inject()
    private readonly swapTransactionRepository: SwapTransactionRepository,
    @Inject()
    private readonly solanaService: SolanaService,
    @InjectQueue(PumpFunQueue.NEW_MULTIPLE_TRADE_TRANSACTION)
    private readonly newMultipleTradeTransactionPair: Queue,
    private readonly questdbSenderService: QuestdbSenderService,
  ) {}

  async handleMultipleSwapPumpFun(
    parsedTransactions: TransactionData[],
    slot: string,
  ) {
    const tradeEventDataDtos: TradeEventDataDto[] = [];
    for (const parsedTransactionData of parsedTransactions) {
      const { instructions, logs, signature, innerInstructions } =
        parsedTransactionData;
      const isPumpFunTx = instructions.some(
        (instruction) => instruction.programId === PUMPFUN_PROGRAM_ID,
      );
      const isSwapPumpFunTx = logs?.some((log) => {
        return PUMPFUN_TRADE_LOG_MESSAGE.includes(log);
      });
      const isPumpFunTxFromInnerInstruction = innerInstructions.some(
        (innerInstructions) => {
          return innerInstructions.instructions.some((innerInstruction) => {
            return innerInstruction.programId === PUMPFUN_PROGRAM_ID;
          });
        },
      );
      if (
        (!isPumpFunTx && !isPumpFunTxFromInnerInstruction) ||
        (!isSwapPumpFunTx && !isPumpFunTxFromInnerInstruction)
      ) {
        continue;
      }

      this.logger.debug(
        `Found swap transactions with signature: [${signature}]`,
      );
      try {
        const coder = new BorshCoder(PumpFunIDL as Idl);
        const parser = new EventParser(
          new PublicKey(PUMPFUN_PROGRAM_ID),
          coder,
        );
        const events = parser.parseLogs(logs);
        const tradeEvents: Event[] = [];
        for (const event of events) {
          if (event.name === PumpfunEvent.TRADE) {
            tradeEvents.push(event);
          }
        }

        tradeEventDataDtos.push(
          ...tradeEvents.map((event) => {
            const data = event.data;
            const mint = data.mint.toString();
            const isBuy = data.isBuy;
            const user = data.user.toString();
            const realSolReserves = data.realSolReserves.toString();
            const realTokenReserves = data.realTokenReserves.toString();
            const virtualSolReserves = data.virtualSolReserves.toString();
            const virtualTokenReserves = data.virtualTokenReserves.toString();
            const timestamp = data.timestamp.toNumber();
            const solAmount = data.solAmount.toString();
            const tokenAmount = data.tokenAmount.toString();

            let pairAddress = '';
            if (mint) {
              const bondingCurve = findBondingCurveByTokenAddress(mint);
              if (bondingCurve) {
                pairAddress = bondingCurve.toBase58();
              } else {
                this.logger.warn(`No bonding curve found for mint: ${mint}`);
              }
            }

            return {
              isBuy,
              mint,
              realSolReserves,
              realTokenReserves,
              virtualSolReserves,
              virtualTokenReserves,
              user,
              timestamp,
              solAmount,
              tokenAmount,
              signatureHash: signature,
              pairAddress: pairAddress,
            } as TradeEventDataDto;
          }),
        );
      } catch (error) {
        this.logger.error(error.message);
      }
    }
    await this.newMultipleTradeTransactionPair.add(
      PUMP_FUN_NEW_TRADE_TRANSACTION_JOB,
      {
        tradeEventData: tradeEventDataDtos,
        slot: slot,
      } as NewMultipleTradeTrxRequestDto,
    );
  }

  async handleMultipleTradeEventPumpfun(
    tradeEventDataDtos: TradeEventDataDto[],
    slot: string,
  ): Promise<CreateSwapTransactionRequestDto[]> {
    const solPrice = await this.solanaService.getSolPrice();
    const formattedPrice = BigNumber(solPrice)
      .dp(9, BigNumber.ROUND_DOWN)
      .toNumber();

    const swapTransactions: CreateSwapTransactionRequestDto[] = [];

    for (const dto of tradeEventDataDtos) {
      try {
        const pairPriceSol = BigNumber(dto.virtualSolReserves)
          .div(dto.virtualTokenReserves)
          .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
          .toNumber();

        const tx = new CreateSwapTransactionRequestDto();
        tx.pairAddress = dto.pairAddress;
        tx.tokenAddress = dto.mint;
        tx.solAmount = Number(dto.solAmount);
        tx.tokenAmount = Number(dto.tokenAmount);
        tx.tradeType = dto.isBuy ? TradeType.BUY : TradeType.SELL;
        tx.userPublicKey = dto.user;
        tx.timestamp = dto.timestamp;
        tx.signature = dto.signatureHash;
        tx.solPriceUsd = formattedPrice;
        tx.pairPriceSol = pairPriceSol;
        tx.block = Number(slot);
        swapTransactions.push(tx);
      } catch (err) {
        this.logger.warn(
          `Invalid tradeEventDataDto: ${dto.signatureHash} - ${err.message}`,
        );
      }
    }

    const { success } =
      await this.createMultipleSwapTransaction(swapTransactions);
    return success;
  }

  async createMultipleSwapTransaction(
    swapTransactions: CreateSwapTransactionRequestDto[],
  ): Promise<{
    success: CreateSwapTransactionRequestDto[];
    failed: FailedCreateSwapTransaction[];
  }> {
    const begin = performance.now();

    const results = await Promise.allSettled(
      swapTransactions.map(async (tx) => {
        const swapTx: SwapTransaction = {
          tokenAddress: tx.tokenAddress,
          solAmount: tx.solAmount,
          tokenAmount: tx.tokenAmount,
          userPublicKey: tx.userPublicKey,
          tradeType: tx.tradeType,
          signature: tx.signature,
          pairPriceSol: tx.pairPriceSol,
          solPriceUsd: tx.solPriceUsd,
          timestamp: tx.timestamp,
          pairAddress: tx.pairAddress,
          block: tx.block,
        };
        await this.questdbSenderService.createSwapTransaction(swapTx);
        return tx;
      }),
    );

    const success: CreateSwapTransactionRequestDto[] = [];
    const failed: FailedCreateSwapTransaction[] = [];

    results.forEach((result, i) => {
      const tx = swapTransactions[i];
      if (result.status === 'fulfilled') {
        success.push(result.value);
      } else {
        failed.push({ tx, reason: result.reason?.message ?? 'Unknown error' });
      }
    });

    await this.questdbSenderService.flush();

    this.logger.log(
      `[INFO] Completed storing Pump.fun transactions: ${success.length} success, ${failed.length} failed, elapsed ${(performance.now() - begin).toFixed()}ms`,
    );

    if (failed.length > 0) {
      this.logger.warn(
        failed.map((f) => `${f.tx.signature}: ${f.reason}`).join(', '),
      );
    }

    return { success, failed };
  }

  async getBuySellCountsTxnsByTokenAddress(
    tokenAddress: string,
  ): Promise<TxnsCount> {
    const begin = performance.now();

    const buySellCountsTxns =
      await this.swapTransactionRepository.findBuySellCountsTxnsByTokenAddress(
        tokenAddress,
      );
    this.logger.verbose(
      `findBuySellCountsTxnsByTokenAddress: [${(performance.now() - begin).toFixed()}]ms`,
    );

    return buySellCountsTxns;
  }

  async getBuySellVolumeByMint(tokenAddress: string): Promise<VolumeResult> {
    const begin = performance.now();

    const buySellVolume =
      await this.swapTransactionRepository.findBuySellVolumeByMint(
        tokenAddress,
      );
    this.logger.verbose(
      `findBuySellVolumeByMint: [${(performance.now() - begin).toFixed()}]ms`,
    );

    return buySellVolume;
  }

  async getVol24hByMint(tokenAddress: string) {
    const begin = performance.now();

    const vol24h =
      await this.swapTransactionRepository.findVol24hByMint(tokenAddress);
    this.logger.verbose(
      `findVol24hByMint: [${(performance.now() - begin).toFixed()}]ms`,
    );

    return vol24h;
  }

  async getAllPairDerailTimeframesByAddressAndTimeframes(
    pairAddress: string,
    timeInterval: TimeInterval,
  ) {
    const begin = performance.now();

    const swapTransaction =
      await this.swapTransactionRepository.findPairDetailsTimeframesAllByAddressAndTimeframes(
        pairAddress,
        timeInterval,
      );
    this.logger.verbose(
      `getAllByAddressAndTimeframes: [${(performance.now() - begin).toFixed()}]ms`,
    );

    return swapTransaction;
  }

  async getAllPairBuyerAndSellerByPairAddress(pairAddress: string) {
    const begin = performance.now();

    const pairBuyerAndSeller =
      await this.swapTransactionRepository.findAllPairBuyerAndSellerByPairAddress(
        pairAddress,
      );
    this.logger.verbose(
      `getAllByAddressAndTimeframes: [${(performance.now() - begin).toFixed()}]ms`,
    );

    return pairBuyerAndSeller;
  }
}
