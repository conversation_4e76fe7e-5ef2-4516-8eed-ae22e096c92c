import { Injectable, Logger } from '@nestjs/common';
import { CandlestickRepository } from './repositories/candlestick.repository';
import { ChartType } from './enums/chart-type.enum';

@Injectable()
export class CandlestickService {
  private readonly logger = new Logger(CandlestickService.name);

  constructor(private readonly candlestickRepository: CandlestickRepository) {}

  async getTransactionData(
    pairAddress: string,
    startTime: number,
    endTime: number,
    interval: number,
    chartType: ChartType,
  ) {
    const ticks = await this.candlestickRepository.getTransactionData(
      pairAddress,
      startTime,
      endTime,
      interval,
      chartType,
    );
    for (let i = 1; i < ticks.length; i++) {
      ticks[i].open = ticks[i - 1].close;
    }
    return ticks;
  }
}