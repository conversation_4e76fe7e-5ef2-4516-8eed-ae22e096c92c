import {
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  <PERSON>,
  <PERSON>,
  IsInt,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ChartType } from '../enums/chart-type.enum';

export class GetTicksRequestDto {
  @IsString()
  @IsNotEmpty()
  pairAddress: string;

  @IsInt()
  @Type(() => Number)
  @Min(1)
  @Max(2592000) //30 days
  tickType: number = 15;

  @IsNumber()
  @Type(() => Number)
  startTime: number;

  @IsNumber()
  @Type(() => Number)
  endTime: number;

  @IsEnum(ChartType)
  chartType: ChartType = ChartType.SOL;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  @Type(() => Number)
  limit?: number = 100;
}