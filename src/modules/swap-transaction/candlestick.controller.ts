import { Controller, Get, Query } from '@nestjs/common';
import { GetTicksRequestDto } from './dto/get-ticks-request.dto';
import { CandlestickService } from './candlestick.service';

@Controller('candlestick')
export class CandlestickController {
  constructor(private readonly candlestickService: CandlestickService) {}

  @Get('ticks')
  async getTicks(@Query() query: GetTicksRequestDto): Promise<any> {
    return await this.candlestickService.getTransactionData(
      query.pairAddress,
      query.startTime,
      query.endTime,
      query.tickType,
      query.chartType,
    );
  }
}
