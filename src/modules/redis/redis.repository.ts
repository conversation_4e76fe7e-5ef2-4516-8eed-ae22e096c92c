import { Inject, Injectable, OnModule<PERSON><PERSON>roy } from '@nestjs/common';
import Redis from 'ioredis';
import { RedisPrefix } from '../../common/constant/redis.prefix';
import Redlock from 'redlock';

@Injectable()
export class RedisRepository implements OnModuleDestroy {
  public readonly redlock: Redlock;

  constructor(@Inject('RedisClient') private readonly redisClient: Redis) {
    this.redlock = new Redlock([redisClient], {
      driftFactor: 0.01, // time in ms
      retryCount: 10,
      retryDelay: 200, // time in ms
      retryJitter: 200, // time in ms
    });
  }

  onModuleDestroy(): void {
    this.redisClient.disconnect();
  }

  async get(prefix: RedisPrefix, key: string): Promise<string | null> {
    return this.redisClient.get(`${prefix}:${key}`);
  }

  async set(prefix: RedisPrefix, key: string, value: string): Promise<void> {
    await this.redisClient.set(`${prefix}:${key}`, value);
  }

  async delete(prefix: RedisPrefix, key: string): Promise<void> {
    await this.redisClient.del(`${prefix}:${key}`);
  }

  async setWithExpiry(
    prefix: RedisPrefix,
    key: string,
    value: string,
    expiry: number,
  ): Promise<void> {
    await this.redisClient.set(`${prefix}:${key}`, value, 'EX', expiry);
  }
}
