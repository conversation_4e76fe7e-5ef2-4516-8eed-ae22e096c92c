import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Pair, PairDocument } from '../entities/pair.schema';

@Injectable()
export class PairRepository {
  private readonly logger = new Logger(PairRepository.name);

  constructor(
    @InjectModel(Pair.name)
    private readonly pairModel: Model<PairDocument>,
  ) {}

  async create(data: Pair): Promise<Pair> {
    const createdPair = new this.pairModel(data);
    const pairObject = await createdPair.save();
    return pairObject;
  }

  async findByTokenAddress(tokenAddress: string): Promise<Pair | null> {
    return this.pairModel.findOne({ tokenAddress }).exec();
  }

  async findByPairAddresses(pairAddresses: string[]): Promise<Pair[]> {
    return this.pairModel
      .find({ pairAddress: { $in: pairAddresses } })
      .lean()
      .exec();
  }

  findRecentPairsSince(
    sinceTimestamp: number,
    limit: number,
    skip: number,
  ): Promise<Pair[]> {
    return this.pairModel
      .find({ createdAt: { $gte: new Date(sinceTimestamp) } })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()
      .exec();
  }
}
