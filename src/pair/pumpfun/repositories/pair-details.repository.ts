import { Inject, Injectable, Logger } from '@nestjs/common';
import { QuestdbPgService } from 'src/questdb/questdb-pg.service';
import { QuestdbSenderService } from 'src/questdb/questdb-sender.service';
import { PairDetail } from 'src/pair-detail/interfaces/pair-detail.interface';
import { Sender } from '@questdb/nodejs-client';
import { buildGetPairDetailsByAddressQuery } from 'src/questdb/questdb.query-builder';
import { QuestDbTable } from 'src/common/constant/questdb.constants';

@Injectable()
export class PairDetailRepository {
  private readonly logger: Logger = new Logger(PairDetailRepository.name);

  constructor(
    private readonly questdbPgService: QuestdbPgService,
    private readonly questdbSenderService: QuestdbSenderService,
    @Inject('QUESTDB_SENDER')
    private readonly sender: Sender,
  ) {}

  async createPairDetailPumpFun(pairDetail: PairDetail) {
    const begin = performance.now();
    const nowSec = Math.floor(Date.now() / 1000); // current time in seconds

    try {
      await this.questdbSenderService.queuePairDetail({
        pairAddress: pairDetail.pairAddress,
        marketCapUsd: pairDetail.marketCapUsd,
        liquidityUsd: pairDetail.liquidityUsd,
        buyVolumeUsd: pairDetail.buyVolumeUsd,
        sellVolumeUsd: pairDetail.sellVolumeUsd,
        numOfBuyTxs: pairDetail.numOfBuyTxs,
        numOfSellTxs: pairDetail.numOfSellTxs,
        process: pairDetail.process,
        marketCapUsd24h: pairDetail.marketCapUsd24h,
        volumeUsd24h: pairDetail.volumeUsd24h,
        numOfTxs24h: pairDetail.numOfTxs24h,
        priceUsd: pairDetail.priceUsd,
        pooledSol: pairDetail.pooledSol,
        ts: nowSec.toString(),
      });

      await this.questdbSenderService.flush();

      this.logger.log(
        `[SUCCESS] Completed storing pair detail for [${pairDetail.pairAddress}], elapsed: ${(performance.now() - begin).toFixed()} ms`,
      );

      return { status: 'inserted' };
    } catch (error) {
      this.logger.error(
        `[FAILURE] Failed to store pair detail for [${pairDetail.pairAddress}]: ${error.message}`,
        error.stack,
      );
      return { status: 'failed', reason: error.message };
    }
  }

  async findMultipleByBondingCurve(bondingCurves: string[]) {
    let result;

    try {
      if (!bondingCurves.length) return [];

      const inList = bondingCurves.map((curve) => `'${curve}'`).join(',');

      const sql = buildGetPairDetailsByAddressQuery(inList);

      result = await this.questdbPgService.query(sql);
      return result;
    } catch (error) {
      console.log('error', error);
    }

    return result;
  }

  async updateMultiplePairDetailPumpFun(pairs: PairDetail[]) {
    const nowMs = Date.now(); // current time in ms

    const filterPairs = pairs.filter((pair) => {
      if (!pair) return false;
      const ts = Date.parse(pair.ts);
      return !isNaN(ts) && nowMs - ts > 2000;
    });

    if (filterPairs.length === 0) return;

    for (const pair of filterPairs) {
      if (pair.pairAddress) {
        // Check if pairAddress exists
        const checkSql = `
          SELECT 1 FROM ${QuestDbTable.PAIR_DETAIL}
          WHERE pair_address = '${pair.pairAddress}'
          LIMIT 1
        `;
        const exists = await this.questdbPgService.query(checkSql);

        if (exists && exists.length > 0) {
          // Build and execute an UPDATE statement for each pair
          const sql = `
            UPDATE ${QuestDbTable.PAIR_DETAIL}
            SET
              market_cap_usd = ${Number(pair.marketCapUsd)},
              liquidity_usd = ${Number(pair.liquidityUsd)},
              buy_volume_usd = ${Number(pair.buyVolumeUsd)},
              sell_volume_usd = ${Number(pair.sellVolumeUsd)},
              num_of_buy_txs = ${Number(pair.numOfBuyTxs)},
              num_of_sell_txs = ${Number(pair.numOfSellTxs)},
              market_cap_usd_24h = ${Number(pair.marketCapUsd24h)},
              volume_usd_24h = ${Number(pair.volumeUsd24h)},
              num_of_txs_24h = ${Number(pair.numOfTxs24h)},
              price_usd = ${Number(pair.priceUsd)},
              pooled_sol = ${Number(pair.pooledSol)}
            WHERE pair_address = '${pair.pairAddress}'
          `;
          try {
            await this.questdbPgService.query(sql);
          } catch (err) {
            this.logger.error(
              `Failed to update pair detail for [${pair.pairAddress}]: ${err.message}`,
            );
          }
        } else {
          this.logger.warn(
            `Pair address [${pair.pairAddress}] does not exist in ${QuestDbTable.PAIR_DETAIL}`,
          );
        }
      } else {
        this.logger.verbose('Pair address not found');
      }
    }

    this.logger.log(
      `[SUCCESS] Updated ${filterPairs.length} ${QuestDbTable.PAIR_DETAIL} entries via UPDATE`,
    );
  }
}
