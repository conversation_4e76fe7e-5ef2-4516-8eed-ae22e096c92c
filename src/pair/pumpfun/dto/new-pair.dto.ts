export class NewPairDto {
  // Pair info
  pairAddress: string;
  createdAt: string;

  // Token info
  tokenName: string;
  tokenAddress: string;
  image: string;
  twitter?: string;
  website?: string;
  telegram?: string;
  symbol: string;

  // Detail info
  liquidityUsd: number;
  buyVolumeUsd: number;
  sellVolumeUsd: number;
  numOfBuyTxs: number;
  numOfSellTxs: number;
  process: number | null;
  marketCapUsd: number;
  marketCapUsd24h: number;
  volumeUsd24h: number;
  numOfTxs24h: number;
}
