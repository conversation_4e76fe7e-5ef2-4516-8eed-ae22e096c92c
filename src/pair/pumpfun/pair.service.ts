import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import {
  PUMPFUN_AND_SOL_DIFFERENTIAL,
  PUMPFUN_CREATE_LOG_MESSAGE,
  PUMPFUN_PROGRAM_ID,
  PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT,
} from '../../common/constant/pumpfun';
import { CreatePumpfunPairRequestDto } from './dto/create-pumpfun-pair-request.dto';
import { TokenService } from '../../token/pumpfun/token.service';
import { InjectQueue } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { Queue } from 'bullmq';
import {
  PUMP_FUN_NEW_PAIR_TRANSACTION_JOB,
  PUMP_FUN_UPDATE_PAIR_JOB,
} from '../../common/constant/job-name';
import {
  ASSOCIATED_TOKEN_PROGRAM_ID,
  TOKEN_PROGRAM_ID,
} from '@solana/spl-token';
import { TransactionData } from '../../common/type/transaction.data';
import { PairDetailRepository } from './repositories/pair-details.repository';
import { Connection, LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js';
import { BONDING_CURVE_LAYOUT } from '../../layout/bonding-curve';
import BigNumber from 'bignumber.js';
import { SolanaService } from '../../solana/solana.service';
import _ from 'lodash';
import { SwapTransactionPumpfunService } from '../../swap-transaction/pumpfun/swap-transaction-pumpfun.service';
import { PairDetail } from 'src/pair-detail/interfaces/pair-detail.interface';
import { TxnsCount } from 'src/swap-transaction/pumpfun/types/txns-count';
import { VolumeResult } from 'src/swap-transaction/pumpfun/types/volume-result';
import { PairRepository } from './repositories/pair.repository';
import { CreatePairDto } from './dto/create-pair.dto';
import { Pair } from './entities/pair.schema';
import { QuestdbPgService } from 'src/questdb/questdb-pg.service';
import { NewPairDto } from './dto/new-pair.dto';

@Injectable()
export class PairService {
  private readonly logger: Logger = new Logger(PairService.name);

  constructor(
    @Inject()
    private readonly pairRepository: PairRepository,
    @Inject()
    private readonly pairDetailRepository: PairDetailRepository,
    @Inject()
    private readonly tokenService: TokenService,
    @InjectQueue(PumpFunQueue.NEW_PAIR)
    private readonly newPairQueue: Queue,
    @InjectQueue(PumpFunQueue.UPDATE_MULTIPLE_PAIR)
    private readonly updateMultiplePairQueue: Queue,
    @Inject(Connection)
    private readonly connection: Connection,
    @Inject()
    private readonly solanaService: SolanaService,
    @Inject(forwardRef(() => SwapTransactionPumpfunService))
    private readonly swapTransactionPumpfunService: SwapTransactionPumpfunService,
    private readonly questdbPgService: QuestdbPgService,
  ) {}

  async handlePumpFunPair(parsedTransactionData: TransactionData) {
    const { instructions, logs, signature, innerInstructions } =
      parsedTransactionData;
    const isFoundCreatedPair = logs?.some((log: string | string[]) => {
      return log === PUMPFUN_CREATE_LOG_MESSAGE;
    });

    if (!isFoundCreatedPair) {
      return;
    }
    const createPairInstructions = instructions.filter(
      (instruction) =>
        instruction.programId === PUMPFUN_PROGRAM_ID &&
        instruction.accounts.length === 14,
    );

    const decimals = _.chain(innerInstructions)
      .flatMap('instructions') // Flatten all instructions
      .filter((instr) => _.get(instr, 'parsed.type') === 'initializeMint2') // Filter by type
      .map('parsed.info.decimals') // Extract decimals
      .first() // Get the first value
      .value(); // Resolve the Lodash chain

    for (const createPairInstruction of createPairInstructions) {
      const accounts = createPairInstruction.accounts;
      const tokenAddress = accounts[0];
      const bondingCurve = accounts[2];
      const associatedBondingCurve = accounts[3];
      const creator = accounts[7];
      const createPumpFunPairRequestDto = new CreatePumpfunPairRequestDto();

      this.logger.log(
        `Found new Pump Fun Pair [${tokenAddress}], transaction [${signature}]`,
      );

      createPumpFunPairRequestDto.signatureHash = signature;
      createPumpFunPairRequestDto.pairAddress = bondingCurve;
      createPumpFunPairRequestDto.associatedBondingCurve =
        associatedBondingCurve;
      createPumpFunPairRequestDto.creator = creator;
      await Promise.all([
        this.newPairQueue.add(PUMP_FUN_NEW_PAIR_TRANSACTION_JOB, {
          signatureHash: createPumpFunPairRequestDto.signatureHash,
          pairAddress: bondingCurve,
          associatedBondingCurve: associatedBondingCurve,
          creator: creator,
          tokenAddress: tokenAddress,
          blockTime: new Date(),
        } as CreatePairDto),

        this.tokenService.handlePumpFunToken(
          tokenAddress,
          decimals,
          new Date(),
          createPairInstruction.data,
        ),
      ]);
    }
  }

  async handleUpdateMultiplePumpFunPair(tokenAddresses: string[]) {
    await this.updateMultiplePairQueue.add(
      PUMP_FUN_UPDATE_PAIR_JOB,
      tokenAddresses,
    );
  }

  async createPumpFunPair(createPairDto: CreatePairDto): Promise<Pair | null> {
    const pair = await this.savePair(createPairDto);

    if (!pair) {
      this.logger.error(
        `[FAILURE] Failed to store pair for token ${createPairDto.tokenAddress}`,
      );
      return null;
    }

    const pairDetail = await this.populatePairDetail(pair);

    if (!pairDetail) {
      this.logger.error(
        `[FAILURE] Failed to generate pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
      return pair;
    }

    const result =
      await this.pairDetailRepository.createPairDetailPumpFun(pairDetail);

    if (result.status !== 'inserted') {
      this.logger.error(
        `[FAILURE] Failed to store pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
    } else {
      this.logger.log(
        `[SUCCESS] Successfully stored pairDetail for bondingCurve ${createPairDto.pairAddress}`,
      );
    }

    return pair;
  }

  async updateMultiplePumpfunDetailsPair(tokenAddresses: string[]) {
    let begin = performance.now();
    const bondingCurves = tokenAddresses.map((tokenAddress) => ({
      bondingCurve: this.findBondingCurveByTokenAddress(tokenAddress),
      tokenAddress: tokenAddress,
    }));
    const pairDetailEntities =
      await this.pairDetailRepository.findMultipleByBondingCurve(
        bondingCurves.map(({ bondingCurve }) => bondingCurve.toBase58()),
      );
    this.logger.verbose(
      `updateMultiplePumpfunDetailsPair:find [${(performance.now() - begin).toFixed()}]ms`,
    );
    begin = performance.now();
    const updatedPairDetailEntities = await Promise.all(
      pairDetailEntities
        .map(async (pairDetailEntity) => {
          const bondingCurve = bondingCurves.find(
            ({ bondingCurve }) =>
              bondingCurve.toBase58() === pairDetailEntity.pair_address,
          );
          return await this.populateUpdatedPairDetail(
            bondingCurve.tokenAddress,
            pairDetailEntity,
          );
        })
        .filter((promise) => promise !== null),
    );
    this.logger.verbose(
      `updateMultiplePumpfunDetailsPair:populate [${(performance.now() - begin).toFixed()}]ms`,
    );
    begin = performance.now();
    if (updatedPairDetailEntities.length > 0) {
      await this.pairDetailRepository.updateMultiplePairDetailPumpFun(
        updatedPairDetailEntities,
      );
    }

    this.logger.verbose(
      `updateMultiplePumpfunDetailsPair:store [${(performance.now() - begin).toFixed()}]ms`,
    );
    return updatedPairDetailEntities;
  }

  private async populatePairDetail(pair: Pair): Promise<PairDetail | null> {
    try {
      const bondingCurve = new PublicKey(pair.pairAddress);
      const tokenAddress = pair.tokenAddress;

      let solPriceUsd = null;
      let accountInfoRaw = null;
      let pooledSol = null;
      let buySellCountsTxns: TxnsCount = null;
      let buySellVolume: VolumeResult = null;
      let vol24h = null;

      const results = await Promise.allSettled([
        this.solanaService.getSolPrice(),
        this.connection.getAccountInfo(bondingCurve),
        this.connection.getBalance(bondingCurve),

        this.swapTransactionPumpfunService.getBuySellCountsTxnsByTokenAddress(
          tokenAddress,
        ),
        this.swapTransactionPumpfunService.getBuySellVolumeByMint(tokenAddress),
        this.swapTransactionPumpfunService.getVol24hByMint(tokenAddress),
        this.swapTransactionPumpfunService.getVol24hByMint(tokenAddress),
      ]);

      if (results[0].status === 'fulfilled') solPriceUsd = results[0].value;
      if (results[1].status === 'fulfilled') accountInfoRaw = results[1].value;
      if (results[2].status === 'fulfilled') pooledSol = results[2].value;

      if (results[3].status === 'fulfilled') {
        buySellCountsTxns = results[3].value;
      }
      if (results[4].status === 'fulfilled') {
        buySellVolume = results[4].value;
      }
      if (results[5].status === 'fulfilled') {
        vol24h = results[5].value;
      }

      if (!accountInfoRaw) throw new Error('Account Info not found');

      const accountInfo = BONDING_CURVE_LAYOUT.decode(
        accountInfoRaw.data.slice(8),
      );
      const virtualTokenReserves = BigNumber(
        accountInfo.virtualTokenReserves.toString(),
      );
      const virtualSolReserves = BigNumber(
        accountInfo.virtualSolReserves.toString(),
      );
      const realTokenReserves = BigNumber(
        accountInfo.realTokenReserves.toString(),
      );

      const priceUsd = virtualSolReserves
        .div(virtualTokenReserves)
        .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
        .multipliedBy(solPriceUsd)
        .toNumber();

      const marketCapUsd = priceUsd * LAMPORTS_PER_SOL;
      const liquidityUsd = (pooledSol / LAMPORTS_PER_SOL) * solPriceUsd;
      const process = BigNumber(1)
        .minus(
          BigNumber(100 / PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT)
            .multipliedBy(realTokenReserves)
            .div(100),
        )
        .dp(5)
        .toNumber();

      return {
        pairAddress: pair.pairAddress,
        marketCapUsd,
        liquidityUsd,
        buyVolumeUsd: buySellVolume.buyTotalVolume,
        sellVolumeUsd: buySellVolume.sellTotalVolume,
        numOfBuyTxs: buySellCountsTxns.buyTotalTxns,
        numOfSellTxs: buySellCountsTxns.sellTotalTxns,
        process,
        marketCapUsd24h: 0,
        volumeUsd24h: vol24h,
        numOfTxs24h: 0,
        priceUsd,
        pooledSol: pooledSol / LAMPORTS_PER_SOL,
        ts: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Error calculating pair detail: ${error.message}`);
      return null;
    }
  }

  private async populateUpdatedPairDetail(
    tokenAddress: string,
    pairDetailEntity: PairDetail,
  ): Promise<PairDetail | null> {
    try {
      const mintAccount = new PublicKey(tokenAddress).toBytes();
      const [bondingCurve] = PublicKey.findProgramAddressSync(
        [Buffer.from('bonding-curve'), mintAccount],
        new PublicKey(PUMPFUN_PROGRAM_ID),
      );
      const [associatedBondingCurve] = PublicKey.findProgramAddressSync(
        [bondingCurve.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), mintAccount],
        ASSOCIATED_TOKEN_PROGRAM_ID,
      );

      let solPriceUsd = null;
      let accountInfoRaw = null;
      let pooledSol = null;
      let topHolderPercentages = null;
      let buySellCountsTxns: TxnsCount = null;
      let buySellVolume: VolumeResult = null;
      let vol24h = null;

      const results = await Promise.allSettled([
        this.solanaService.getSolPrice(),
        this.connection.getAccountInfo(bondingCurve),
        this.connection.getBalance(bondingCurve),
        this.calculateTopHolders(
          tokenAddress,
          associatedBondingCurve.toBase58(),
        ),
        this.swapTransactionPumpfunService.getBuySellCountsTxnsByTokenAddress(
          tokenAddress,
        ),
        this.swapTransactionPumpfunService.getBuySellVolumeByMint(tokenAddress),
        this.swapTransactionPumpfunService.getVol24hByMint(tokenAddress),
      ]);

      if (results[0].status === 'fulfilled') solPriceUsd = results[0].value;
      if (results[1].status === 'fulfilled') accountInfoRaw = results[1].value;
      if (results[2].status === 'fulfilled') pooledSol = results[2].value;
      if (results[3].status === 'fulfilled')
        topHolderPercentages = results[3].value;
      if (results[4].status === 'fulfilled') {
        buySellCountsTxns = results[4].value;
      }
      if (results[5].status === 'fulfilled') {
        buySellVolume = results[5].value;
      }
      if (results[6].status === 'fulfilled') {
        vol24h = results[6].value;
      }

      if (!accountInfoRaw) throw new Error('Account Info not found');
      const accountInfo = BONDING_CURVE_LAYOUT.decode(
        accountInfoRaw.data.slice(8),
      );

      const virtualTokenReserves = BigNumber(
        accountInfo.virtualTokenReserves.toString(),
      );
      const virtualSolReserves = BigNumber(
        accountInfo.virtualSolReserves.toString(),
      );
      const realTokenReserves = BigNumber(
        accountInfo.realTokenReserves.toString(),
      );

      return Object.assign(pairDetailEntity, {
        bondingCurve: bondingCurve,
        isCompleted: accountInfo.complete,
        pooledSol: (pooledSol / LAMPORTS_PER_SOL).toFixed(9),
        priceUsd: virtualSolReserves
          .div(virtualTokenReserves)
          .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
          .multipliedBy(solPriceUsd)
          .toNumber()
          .toFixed(9),
        marketCapUsd: virtualSolReserves
          .div(virtualTokenReserves)
          .multipliedBy(PUMPFUN_AND_SOL_DIFFERENTIAL)
          .multipliedBy(solPriceUsd)
          .multipliedBy(LAMPORTS_PER_SOL)
          .toNumber()
          .toFixed(9),
        burned: 100, // PUMPFUN LOCKED 100%
        holders: '0', // TODO: Get total holders
        process: BigNumber(1)
          .minus(
            BigNumber(100 / PUMPFUN_TO_RAYDIUM_MIGRATION_AMOUNT)
              .multipliedBy(realTokenReserves)
              .div(100),
          )
          .dp(5)
          .toNumber(),
        topHolderPercentage: topHolderPercentages,
        liquidityUsd: ((pooledSol / LAMPORTS_PER_SOL) * solPriceUsd).toFixed(9),
        buyVolumeUsd: buySellVolume.buyTotalVolume,
        sellVolumeUsd: buySellVolume.sellTotalVolume,
        buyTxns: buySellCountsTxns.buyTotalTxns,
        sellTxns: buySellCountsTxns.sellTotalTxns,
        volumeUsd24h: vol24h,
      });
    } catch (error) {
      this.logger.error(`Error calculating pair details: ${error.message}`);
      return null;
    }
  }

  private findBondingCurveByTokenAddress(tokenAddress: string) {
    const mintAccount = new PublicKey(tokenAddress).toBytes();
    const [bondingCurve] = PublicKey.findProgramAddressSync(
      [Buffer.from('bonding-curve'), mintAccount],
      new PublicKey(PUMPFUN_PROGRAM_ID),
    );
    return bondingCurve;
  }

  private async calculateTopHolders(
    tokenAddress: string,
    associatedBondingCurve: string,
  ) {
    const [holders, totalSupply] = await Promise.all([
      await this.solanaService.getTokenTopHolders(tokenAddress, 20),
      await this.solanaService.getTokenSupply(tokenAddress),
    ]);

    // Take top 10 and calculate percentage
    const holdersPercentage = holders
      .filter(
        (resultHolder) =>
          resultHolder.address.toString() !== associatedBondingCurve &&
          resultHolder.uiAmount > 0,
      )
      .slice(0, 10)
      .map((holder) => ({
        owner: holder.address,
        balance: holder.amount,
        decimals: holder.decimals,
        percentage: BigNumber(holder.amount).div(totalSupply.amount),
      }));

    return holdersPercentage
      .reduce((sum, holder) => sum.plus(holder.percentage), new BigNumber(0))
      .dp(5)
      .toNumber();
  }

  private async savePair(createPairDto: CreatePairDto): Promise<Pair | null> {
    const {
      tokenAddress,
      associatedBondingCurve,
      pairAddress,
      signatureHash,
      creator,
      blockTime,
    } = createPairDto;

    try {
      const existPair =
        await this.pairRepository.findByTokenAddress(tokenAddress);

      if (existPair) {
        this.logger.debug(
          `Pair already exists for token address: ${tokenAddress}`,
        );
        return null;
      }

      const pairToCreate: Partial<Pair> = {
        tokenAddress,
        associatedBondingCurve,
        pairAddress,
        signatureHash,
        creator,
        createdAt: blockTime,
      };

      const savedPair = await this.pairRepository.create(pairToCreate as Pair);

      this.logger.log(
        `[SUCCESS] Completed storing pair for token: ${tokenAddress}`,
      );

      return savedPair;
    } catch (error) {
      this.logger.error(
        `[FAILURE] Failed to save pair: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  async getPairsByAddresses(pairAddresses: string[]): Promise<Pair[]> {
    return this.pairRepository.findByPairAddresses(pairAddresses);
  }

  async getRecentNewPairsWithin24h(
    limit: number,
    page = 1,
    sortBy?: string,
    sortDirection: 'asc' | 'desc' = 'desc',
  ): Promise<NewPairDto[]> {
    const since = Date.now() - 24 * 60 * 60 * 1000;
    const skip = (page - 1) * limit;

    const sortableFields = [
      'liquidity_usd',
      'market_cap_usd',
      'volume_usd_24h',
      'num_of_txs_24h',
    ];
    const sortField = sortableFields.includes(sortBy || '') ? sortBy : 'ts';
    const direction = sortDirection === 'asc' ? 'ASC' : 'DESC';

    const recentPairs = await this.pairRepository.findRecentPairsSince(
      since,
      limit,
      skip,
    );

    if (recentPairs.length === 0) return [];

    const pairAddresses = recentPairs.map((p) => p.pairAddress);
    const tokenAddresses = recentPairs.map((p) => p.tokenAddress);

    const [tokens, pairDetails] = await Promise.all([
      this.tokenService.getTokensByAddresses(tokenAddresses),
      this.questdbPgService.query(`
      SELECT *
      FROM pair_detail
      WHERE pair_address IN (${pairAddresses.map((a) => `'${a}'`).join(',')})
        AND ts > dateadd('h', -24, now())
      LATEST ON ts PARTITION BY pair_address
      ORDER BY ${sortField} ${direction}
    `),
    ]);

    const tokenMap = new Map(tokens.map((t) => [t.tokenAddress, t]));
    const pairMap = new Map(recentPairs.map((p) => [p.pairAddress, p]));

    return pairDetails
      .map((detail) => {
        const pair = pairMap.get(detail.pair_address);
        const token = tokenMap.get(pair?.tokenAddress ?? '');
        if (!pair || !token) return null;

        return {
          pairAddress: pair.pairAddress,
          createdAt: pair.createdAt.toString(),

          tokenName: token.tokenName,
          tokenAddress: token.tokenAddress,
          image: token.image,
          twitter: token.twitter,
          website: token.website,
          telegram: token.telegram,
          symbol: token.symbol,

          liquidityUsd: detail.liquidity_usd,
          buyVolumeUsd: detail.buy_volume_usd,
          sellVolumeUsd: detail.sell_volume_usd,
          numOfBuyTxs: detail.num_of_buy_txs,
          numOfSellTxs: detail.num_of_sell_txs,
          process: detail.process,
          marketCapUsd: detail.market_cap_usd,
          marketCapUsd24h: detail.market_cap_usd_24h,
          volumeUsd24h: detail.volume_usd_24h,
          numOfTxs24h: detail.num_of_txs_24h,
        } as NewPairDto;
      })
      .filter((item) => item !== null);
  }
}
