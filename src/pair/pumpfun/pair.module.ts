import { forwardRef, Module } from '@nestjs/common';
import { PairService } from './pair.service';
import { TokenModule } from '../../token/pumpfun/token.module';
import { QueueModule } from '../../queue/queue.module';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { QueueConfig } from '../../queue/queue.interface';
import { RedisModule } from '../../redis/redis.module';
import { NewPairPumpFunProcessor } from './processor/new.pair.transaction.processor';
import { SolanaModule } from '../../solana/solana.module';
import { PairDetailRepository } from './repositories/pair-details.repository';
import { SwapTransactionPumpfunModule } from '../../swap-transaction/pumpfun/swap-transaction-pumpfun.module';
import { DefaultJobOptions } from 'bullmq';
import { UpdateMultiplePairProcessor } from './processor/update-multiple-pair.processor';
import { QuestdbModule } from 'src/questdb/questdb.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Pair, PairSchema } from './entities/pair.schema';
import { PairRepository } from './repositories/pair.repository';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Pair.name, schema: PairSchema }]),
    forwardRef(() => TokenModule),
    RedisModule,
    QueueModule.register({
      queues: [PumpFunQueue.NEW_PAIR, PumpFunQueue.UPDATE_MULTIPLE_PAIR].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    SolanaModule,
    forwardRef(() => SwapTransactionPumpfunModule),
    QuestdbModule,
  ],
  providers: [
    PairService,
    PairRepository,
    PairDetailRepository,
    NewPairPumpFunProcessor,
    UpdateMultiplePairProcessor,
  ],
  exports: [PairService],
})
export class PairModule {}
