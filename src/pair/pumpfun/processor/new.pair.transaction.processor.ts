import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../../queue/worker-host.processor';
import { Job } from 'bullmq';
import { PairService } from '../pair.service';
import { CreatePairDto } from '../dto/create-pair.dto';
import { Pair } from '../entities/pair.schema';
import { PairError } from 'src/common/constant/errors';
import { QueueNewPairException } from 'src/common/exceptions/queue-new-pair-exception';

@Processor(PumpFunQueue.NEW_PAIR, {
  concurrency: 100_000,
})
@Injectable()
export class NewPairPumpFunProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly pairService: PairService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<CreatePairDto, Pair, string>,
  ): Promise<Pair | null> {
    try {
      const begin = performance.now();
      const newPairDataDto = job.data;

      const savedPair =
        await this.pairService.createPumpFunPair(newPairDataDto);
      this.logger.log(
        `[SUCCESS] Processed pair ${newPairDataDto.tokenAddress}, elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );

      return savedPair;
    } catch (err) {
      this.logger.error(
        `${PumpFunQueue.NEW_PAIR} handleProcess error: ${err.message}`,
        err.stack,
      );
      throw new QueueNewPairException(PairError.ERROR_QUEUE_NEW_PAIR);
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<CreatePairDto, Pair, string>) {
    const { id, returnvalue: returnValue } = job;
    if (returnValue) {
      this.logger.verbose(
        `Job id: ${id}, completed tokenAddress [${returnValue.tokenAddress}]`,
      );
    }
  }
}
