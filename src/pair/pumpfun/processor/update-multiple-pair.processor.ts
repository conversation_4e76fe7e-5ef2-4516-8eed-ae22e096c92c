import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../../queue/worker-host.processor';
import { PairService } from '../pair.service';
import { Job } from 'bullmq';
import { QueueUpdatePairDetailException } from 'src/common/exceptions/queue-update-pair-detail-exception';
import { PairDetailError } from 'src/common/constant/errors';
import { Pair } from '../entities/pair.schema';
import { PairDetail } from 'src/pair-detail/interfaces/pair-detail.interface';

@Processor(PumpFunQueue.UPDATE_MULTIPLE_PAIR, {
  concurrency: 100_000,
})
@Injectable()
export class UpdateMultiplePairProcessor extends WorkerHostProcessor {
  constructor(
    @Inject()
    private readonly pairService: PairService,
  ) {
    super();
  }

  async handleProcess(job: Job<string[], Pair[], string>): Promise<any> {
    const begin = performance.now();
    const tokenAddresses = job.data;
    try {
      return await this.pairService.updateMultiplePumpfunDetailsPair(
        tokenAddresses,
      );
    } catch (error) {
      this.logger.error(
        `[UpdateMultiplePairProcessor] Job id=${job.id} failed: ${error}`,
      );
      throw new QueueUpdatePairDetailException(
        PairDetailError.ERROR_QUEUE_UPDATE_PAIR_DETAIL,
      );
    } finally {
      this.logger.verbose(
        `Completed store pairs [${tokenAddresses.length}], elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<string[], PairDetail[], string>) {
    const { id, returnvalue } = job;
    if (returnvalue.length > 0) {
      this.logger.debug(`Job id: ${id}, completed update multiple pairDetails`);
    }
  }
}
