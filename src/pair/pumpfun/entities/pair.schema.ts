import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { now } from 'mongoose';

export type PairDocument = Pair & Document;
@Schema({
  collection: 'pair',
})
export class Pair {
  @Prop({ required: true })
  signatureHash: string;

  @Prop({ required: true })
  pairAddress: string;

  @Prop({ required: true })
  associatedBondingCurve: string;

  @Prop({ required: true })
  creator: string;

  @Prop({ required: true })
  tokenAddress: string;

  @Prop({ default: now })
  createdAt?: Date;

  @Prop({ default: now })
  updatedAt?: Date;
}

export const PairSchema = SchemaFactory.createForClass(Pair);
