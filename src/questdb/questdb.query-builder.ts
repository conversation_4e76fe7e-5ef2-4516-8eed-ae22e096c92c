import { LAMPORTS_PER_SOL } from '@solana/web3.js';
import { QuestDbTable } from 'src/common/constant/questdb.constants';

export const buildBuySellVolumeQuery = (tokenAddress: string) => `
      SELECT trade_type,
             SUM((sol_amount / ${LAMPORTS_PER_SOL}) * sol_price_usd) AS total_volume
      FROM ${QuestDbTable.SWAP_TRANSACTION}
      WHERE token_address = '${tokenAddress}'
      GROUP BY trade_type;
    `;

export const buildBuySellCountsQuery = (tokenAddress: string) => `
      SELECT trade_type, COUNT(*) AS total_count
      FROM ${QuestDbTable.SWAP_TRANSACTION}
      WHERE token_address = '${tokenAddress}'
      GROUP BY trade_type;
    `;

export const buildVolume24hQuery = (tokenAddress: string) => `
      SELECT SUM((sol_amount / ${LAMPORTS_PER_SOL}) * sol_price_usd) AS volume24h
      FROM ${QuestDbTable.SWAP_TRANSACTION}
      WHERE token_address = '${tokenAddress}'
        AND ts
          > dateadd('h'
          , -24
          , now());
    `;

export const buildGetPairDetailsByAddressQuery = (inList: string) => `
    SELECT *
    FROM ${QuestDbTable.PAIR_DETAIL}
    WHERE pair_address IN (${inList})
  `;
