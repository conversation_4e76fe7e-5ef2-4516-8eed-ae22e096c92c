import { Inject, Injectable, OnModuleDestroy } from '@nestjs/common';
import { Sender } from '@questdb/nodejs-client';
import { SwapTransaction } from '../swap-transaction/interfaces/swap-transaction.interface';
import { PairDetail } from 'src/pair-detail/interfaces/pair-detail.interface';
import { QuestDbTable } from 'src/common/constant/questdb.constants';

@Injectable()
export class QuestdbSenderService implements OnModuleDestroy {
  constructor(
    @Inject('QUESTDB_SENDER')
    private readonly sender: Sender,
  ) {}

  async queuePumpfunTransaction(tx: SwapTransaction) {
    await this.sender
      .table(QuestDbTable.SWAP_TRANSACTION)
      .symbol('token_address', tx.tokenAdress || '')
      .symbol('pair_address', tx.pairAdress || '')
      .symbol('trade_user', tx.tradeUser || '')
      .symbol('trade_type', tx.tradeType || '')
      .symbol('signature', tx.signature || '')
      .floatColumn('sol_amount', tx.solAmount)
      .floatColumn('token_amount', tx.tokenAmount)
      .floatColumn('sol_price_usd', tx.solPriceUsd)
      .at(tx.timestamp * 1_000_000); // nanoseconds
  }

  async queuePairDetail(pair: PairDetail): Promise<void> {
    await this.sender
      .table(QuestDbTable.PAIR_DETAIL)
      .symbol('pair_address', pair.pairAddress || '')
      .floatColumn('market_cap_usd', pair.marketCapUsd)
      .floatColumn('liquidity_usd', pair.liquidityUsd)
      .floatColumn('buy_volume_usd', pair.buyVolumeUsd)
      .floatColumn('sell_volume_usd', pair.sellVolumeUsd)
      .floatColumn('num_of_buy_txs', pair.numOfBuyTxs)
      .floatColumn('num_of_sell_txs', pair.numOfSellTxs)
      .floatColumn('market_cap_usd_24h', pair.marketCapUsd24h)
      .floatColumn('volume_usd_24h', pair.volumeUsd24h)
      .floatColumn('num_of_txs_24h', pair.numOfTxs24h)
      .floatColumn('price_usd', pair.priceUsd)
      .floatColumn('pooled_sol', pair.pooledSol)
      .floatColumn('process', pair.process)
      .atNow();
  }

  async flush() {
    await this.sender.flush();
  }

  async onModuleDestroy() {
    await this.sender.close();
  }
}
