// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.29.3
// source: pair.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "pair";

export interface GetRecentPairsRequest {
  limit: number;
  page: number;
  sortBy: string;
  /** 'asc' or 'desc' */
  sortDirection: string;
}

export interface GetRecentPairsResponse {
  pairs: NewPairDto[];
}

export interface NewPairDto {
  pairAddress: string;
  createdAt: string;
  tokenName: string;
  tokenAddress: string;
  image: string;
  twitter?: string | undefined;
  website?: string | undefined;
  telegram?: string | undefined;
  symbol: string;
  liquidityUsd: number;
  buyVolumeUsd: number;
  sellVolumeUsd: number;
  numOfBuyTxs: number;
  numOfSellTxs: number;
  process: number;
  marketCapUsd: number;
  marketCapUsd24h: number;
  volumeUsd24h: number;
  numOfTxs24h: number;
}

export const PAIR_PACKAGE_NAME = "pair";

export interface PairServiceClient {
  getRecentNewPairs(request: GetRecentPairsRequest): Observable<GetRecentPairsResponse>;
}

export interface PairServiceController {
  getRecentNewPairs(
    request: GetRecentPairsRequest,
  ): Promise<GetRecentPairsResponse> | Observable<GetRecentPairsResponse> | GetRecentPairsResponse;
}

export function PairServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getRecentNewPairs"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PairService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PairService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PAIR_SERVICE_NAME = "PairService";
