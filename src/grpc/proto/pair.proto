syntax = "proto3";

package pair;

service PairService {
  rpc GetRecentNewPairs (GetRecentPairsRequest) returns (GetRecentPairsResponse);
}

message GetRecentPairsRequest {
  int32 limit = 1;
  int32 page = 2;
  string sortBy = 3;
  string sortDirection = 4; // 'asc' or 'desc'
}

message GetRecentPairsResponse {
  repeated NewPairDto pairs = 1;
}

message NewPairDto {
  string pairAddress = 1;
  string createdAt = 2;

  string tokenName = 3;
  string tokenAddress = 4;
  string image = 5;
  optional string twitter = 6;
  optional string website = 7;
  optional string telegram = 8;
  string symbol = 9;

  double liquidityUsd = 10;
  double buyVolumeUsd = 11;
  double sellVolumeUsd = 12;
  int64 numOfBuyTxs = 13;
  int64 numOfSellTxs = 14;
  double process = 15;
  double marketCapUsd = 16;
  double marketCapUsd24h = 17;
  double volumeUsd24h = 18;
  int64 numOfTxs24h = 19;
}
