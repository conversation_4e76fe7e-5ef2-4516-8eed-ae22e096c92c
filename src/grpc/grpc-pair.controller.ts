import { Controller, UseInterceptors } from '@nestjs/common';
import {
  GetRecentPairsRequest,
  GetRecentPairsResponse,
  PairServiceController,
  PairServiceControllerMethods,
} from './proto/pair';
import { GprcLoggingInterceptor } from '../common/interceptor/grpc.logging.interceptor';
import { PairService } from 'src/pair/pumpfun/pair.service';

@Controller()
@UseInterceptors(new GprcLoggingInterceptor())
@PairServiceControllerMethods()
export class PairController implements PairServiceController {
  constructor(private readonly pairService: PairService) {}

  async getRecentNewPairs(
    request: GetRecentPairsRequest,
  ): Promise<GetRecentPairsResponse> {
    const { limit = 50, page = 1, sortBy, sortDirection } = request;

    const validatedSortDirection: 'asc' | 'desc' =
      sortDirection === 'asc' || sortDirection === 'desc'
        ? sortDirection
        : 'desc';

    const pairs = await this.pairService.getRecentNewPairsWithin24h(
      limit,
      page,
      sortBy,
      validatedSortDirection,
    );

    return { pairs };
  }
}
