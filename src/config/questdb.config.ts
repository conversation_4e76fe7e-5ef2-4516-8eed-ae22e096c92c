import { registerAs } from '@nestjs/config';

export const questdbPgConfig = registerAs('questdbPg', () => ({
  host: process.env.QUESTDB_PG_HOST ?? 'localhost',
  port: parseInt(process.env.QUESTDB_PG_PORT ?? '8812'),
  database: process.env.QUESTDB_PG_DATABASE ?? 'qdb',
  user: process.env.QUESTDB_PG_USER ?? 'admin',
  password: process.env.QUESTDB_PG_PASSWORD ?? 'quest',
}));

export const questdbSenderConfig = registerAs('questdbSender', () => ({
  mode: process.env.QUESTDB_SENDER_MODE ?? 'http',
  host: process.env.QUESTDB_SENDER_HOST ?? 'localhost',
  port: parseInt(process.env.QUESTDB_SENDER_PORT ?? '9000'),
}));
