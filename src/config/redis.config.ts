import { registerAs } from '@nestjs/config';
import { IsEnum, IsInt, IsOptional, IsString, Max, Min } from 'class-validator';
import { RedisDbConfig, RedisMode } from './redis-config.type';
import validateConfig from 'src/common/utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  REDIS_HOST: string;

  @IsInt()
  @Min(0)
  @Max(65535)
  @IsOptional()
  REDIS_PORT: number;

  @IsString()
  REDIS_PASSWORD: string;

  @IsInt()
  @Min(0)
  @Max(65535)
  REDIS_DB: number;

  @IsEnum(RedisMode)
  @IsOptional()
  REDIS_MODE: RedisMode;

  @IsOptional()
  @IsString()
  REDIS_SENTINEL_NODES: string;

  @IsString()
  @IsOptional()
  REDIS_SENTINEL_MASTER_NAME: string;

  @IsString()
  @IsOptional()
  REDIS_SENTINEL_PASSWORD: string;
}

export default registerAs<RedisDbConfig>('redis', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  if (process.env.REDIS_MODE === RedisMode.SENTINEL) {
    let sentinelNodes = [];
    try {
      sentinelNodes = process.env.REDIS_SENTINEL_NODES
        ? JSON.parse(process.env.REDIS_SENTINEL_NODES)
        : undefined;
    } catch (_ignoredErr) {}
    return {
      sentinels: sentinelNodes,
      sentinelPassword: process.env.REDIS_SENTINEL_PASSWORD,
      name: process.env.REDIS_SENTINEL_MASTER_NAME,
      password: process.env.REDIS_PASSWORD,
      db: process.env.REDIS_DB ?? 1,
    } as RedisDbConfig;
  }

  return {
    host: process.env.REDIS_HOST ?? 'localhost',
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT, 10) : 6379,
    password: process.env.REDIS_PASSWORD ?? undefined,
    db: process.env.REDIS_DB ? parseInt(process.env.REDIS_DB, 10) : 0,
  } as RedisDbConfig;
});
