import * as process from 'node:process';

export interface AppConfig {
  port: number;
  wsPort: number;
  nodeEnv: string;
  logLevel: string;
  wsAuthToken: string;
}

export const appConfig = () => ({
  app: {
    port: process.env.APP_PORT ?? 3000,
    wsPort: process.env.WS_PORT ?? 0,
    nodeEnv: process.env.NODE_ENV ?? 'development',
    logLevel: process.env.LOG_LEVEL ?? 'info',
    wsAuthToken: process.env.WS_AUTH_TOKEN,
  },
});
