import { registerAs } from '@nestjs/config';
import * as process from 'node:process';
import { IsOptional, IsString } from 'class-validator';
import validateConfig from 'src/common/utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  GRPC_URL: string;
}

export interface GrpcConfig {
  url: string;
}

export default registerAs<GrpcConfig>('grpc', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    url: process.env.GRPC_URL,
  } as GrpcConfig;
});
