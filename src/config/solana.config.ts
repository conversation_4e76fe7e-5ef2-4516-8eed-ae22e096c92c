import * as process from 'node:process';

import { ConnectionConfig } from '@solana/web3.js';

export interface SolanaConfig {
  wssRpcUrl: string;
  rpcUrl: string;
  connectionConfig: ConnectionConfig;
  grpcUrl: string;
  isUsingWS: boolean;
  grpcXToken: string;
}

export const solanaConfig = () => ({
  solana: {
    rpcUrl: process.env.SOLANA_RPC_URL,
    wssRpcUrl: process.env.SOLANA_WSRPC_URL,
    isUsingWS: process.env.SOLANA_WSRPC_URL !== undefined,
    connectionConfig: {
      confirmTransactionInitialTimeout:
        process.env.SOLANA_CONFIRM_TRX_TIMEOUT ?? 60_000,
      commitment: process.env.SOLANA_COMMITMENT ?? 'confirmed',
      disableRetryOnRateLimit:
        process.env.SOLANA_DISABLE_RETRY_ON_RATE_LIMIT === 'true',
    } as ConnectionConfig,
    grpcUrl: process.env.SOLANA_GRPC_URL,
    grpcXToken: process.env.SOLANA_GRPC_X_TOKEN,
  },
});
