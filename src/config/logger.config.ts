import { WinstonModule } from 'nest-winston';
import { format, transports } from 'winston';
import 'winston-daily-rotate-file';

export const loggerConfig = (logLevel: string) => {
  return WinstonModule.createLogger({
    transports: [
      new transports.DailyRotateFile({
        filename: 'logs/pumpfun-service.%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        level: logLevel,
        maxSize: '20m',
        zippedArchive: true,
        format: format.combine(
          format.splat(),
          format.timestamp(),
          format.printf((log) => {
            return `${log.timestamp}\t${log.level.toUpperCase()} [${log.context}] ${log.message}`;
          }),
        ),
      }),
      new transports.Console({
        level: logLevel,
        format: format.combine(
          format.cli(),
          format.splat(),
          format.timestamp(),
          format.printf((log) => {
            return `${log.timestamp}\t${log.level} [${log.context}] ${log.message}`;
          }),
        ),
      }),
    ],
  });
};
