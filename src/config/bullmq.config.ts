import { IsEnum, IsInt, IsOptional, IsString } from 'class-validator';
import { registerAs } from '@nestjs/config';

import { RedisOptions } from 'ioredis';
import { RedisMode } from './redis-config.type';
import validateConfig from 'src/common/utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  REDIS_BULL_HOST: string;

  @IsInt()
  @IsOptional()
  REDIS_BULL_PORT: number;

  @IsString()
  @IsOptional()
  REDIS_BULL_PASSWORD: string;

  @IsInt()
  @IsOptional()
  REDIS_BULL_DB: number;

  @IsEnum(RedisMode)
  @IsOptional()
  REDIS_BULL_MODE: RedisMode;

  @IsOptional()
  @IsString()
  REDIS_BULL_SENTINEL_NODES: string;

  @IsString()
  @IsOptional()
  REDIS_BULL_SENTINEL_MASTER_NAME: string;

  @IsString()
  @IsOptional()
  REDIS_BULL_SENTINEL_PASSWORD: string;
}

export default registerAs<RedisOptions>('queue', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  if (process.env.REDIS_BULL_MODE === RedisMode.SENTINEL) {
    let sentinelNodes = [];
    try {
      sentinelNodes = process.env.REDIS_BULL_SENTINEL_NODES
        ? JSON.parse(process.env.REDIS_BULL_SENTINEL_NODES)
        : undefined;
    } catch (_ignoredErr) {}
    return {
      sentinels: sentinelNodes,
      name: process.env.REDIS_BULL_SENTINEL_MASTER_NAME,
      password: process.env.REDIS_BULL_PASSWORD,
      sentinelPassword: process.env.REDIS_BULL_SENTINEL_PASSWORD,
      db: process.env.REDIS_BULL_DB ?? 0,
    } as RedisOptions;
  }
  return {
    host: process.env.REDIS_BULL_HOST ?? 'localhost',
    password: process.env.REDIS_BULL_PASSWORD,
    port: process.env.REDIS_BULL_PORT ?? 6379,
    db: process.env.REDIS_BULL_DB ?? 0,
  } as RedisOptions;
});
