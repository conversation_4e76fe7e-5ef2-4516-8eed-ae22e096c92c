import { IsString, IsNot<PERSON>mpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsInt } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class GetTicksRequestDto {
  @IsString()
  @IsNotEmpty()
  pairAddress: string;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => value || '15s')
  tickType: string = '15s';

  @IsNumber()
  @Type(() => Number)
  startTime: number;

  @IsNumber()
  @Type(() => Number)
  endTime: number;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(1000)
  @Type(() => Number)
  limit?: number = 100;
}
