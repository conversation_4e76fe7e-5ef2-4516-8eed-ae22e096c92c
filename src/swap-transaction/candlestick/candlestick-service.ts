import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { QuestdbPgService } from '../../questdb/questdb-pg.service';
import { GetTicksRequestDto } from './dto/get-ticks-request.dto';
import { GetTicksResponseDto } from './dto/get-ticks-response.dto';
import { TickData } from './interfaces/tick-data.interface';
import { QuestDbTable } from '../../common/constant/questdb.constants';

@Injectable()
export class CandlestickService {
  private readonly logger = new Logger(CandlestickService.name);

  constructor(private readonly questdbPgService: QuestdbPgService) {}

  private async getTransactionData(
    pairAddress: string,
    startTime: number,
    endTime: number,
  ) {
    // Extend the query range to get transactions before startTime for interpolation
    const extendedStartTime = new Date(startTime / 1000); // 1 hour before for better interpolation
    const extendedEndTime = new Date(endTime / 1000) // 1 hour after for better interpolation

    const query = `
      SELECT
        timestamp,
        sol_price_usd as price,
        sol_amount,
        token_amount,
        trade_type
      FROM ${QuestDbTable.SWAP_TRANSACTION}
      WHERE pair_address = $1
        AND timestamp >= $2
        AND timestamp <= $3
      ORDER BY timestamp ASC
    `;

    const params = [pairAddress, extendedStartTime, extendedEndTime];
    const result = await this.questdbPgService.query(query, params);

    // Check if pair exists by looking for any transactions
    if (result.length === 0) {
      // Check if pair exists at all
      const pairCheckQuery = `
        SELECT COUNT(*) as count
        FROM ${QuestDbTable.SWAP_TRANSACTION}
        WHERE pair_address = $1
        LIMIT 1
      `;
      const pairCheck = await this.questdbPgService.query(pairCheckQuery, [
        pairAddress,
      ]);

      if (pairCheck[0]?.count === 0) {
        throw new BadRequestException('Invalid pair address - pair not found');
      }
    }

    return result;
  }

  async getTicks(request: GetTicksRequestDto): Promise<GetTicksResponseDto> {
    const {
      pairAddress,
      tickType,
      startTime,
      endTime,
      page = 1,
      limit = 100,
    } = request;

    // Validate time range
    if (startTime >= endTime) {
      throw new BadRequestException('startTime must be earlier than endTime');
    }

    // Parse tick interval
    const tickIntervalSeconds = this.parseTickType(tickType);

    // Calculate tick timestamps
    const tickTimestamps = this.generateTickTimestamps(
      startTime,
      endTime,
      tickIntervalSeconds,
    );

    if (tickTimestamps.length === 0) {
      return { ticks: [] };
    }

    // Get transaction data for the time range
    const transactions = await this.getTransactionData(
      pairAddress,
      startTime,
      endTime,
    );

    // Generate ticks with price calculations
    const allTicks = this.calculateTicks(
      tickTimestamps,
      transactions,
      tickIntervalSeconds,
    );

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTicks = allTicks.slice(startIndex, endIndex);

    return { ticks: paginatedTicks };
  }

  private parseTickType(tickType: string): number {
    const match = tickType.match(/^(\d+)s$/);
    if (!match) {
      throw new BadRequestException(
        'Invalid tickType format. Expected format: "15s", "30s", etc.',
      );
    }

    const seconds = parseInt(match[1], 10);
    if (seconds <= 0) {
      throw new BadRequestException(
        'tickType must be a positive number of seconds',
      );
    }

    return seconds;
  }

  private generateTickTimestamps(
    startTime: number,
    endTime: number,
    intervalSeconds: number,
  ): number[] {
    const timestamps: number[] = [];
    let currentTime = startTime;

    // Generate ticks that start on or before endTime
    // The last tick starts on or before endTime but may extend beyond it
    while (currentTime <= endTime) {
      timestamps.push(currentTime);
      currentTime += intervalSeconds;
    }

    return timestamps;
  }

  private calculateTicks(
    tickTimestamps: number[],
    transactions: any[],
    intervalSeconds: number,
  ): TickData[] {
    const ticks: TickData[] = [];
    let previousEndPrice: number | null = null;

    for (let i = 0; i < tickTimestamps.length; i++) {
      const tickStart = tickTimestamps[i];
      const tickEnd = tickStart + intervalSeconds;

      // Find transactions within this tick interval
      const tickTransactions = transactions.filter(
        (tx) => tx.timestamp >= tickStart && tx.timestamp < tickEnd,
      );

      const tick = this.calculateSingleTick(
        tickStart,
        tickEnd,
        tickTransactions,
        transactions,
        previousEndPrice,
      );
      ticks.push(tick);

      // Ensure continuity: next tick's startPrice should equal current tick's endPrice
      previousEndPrice = tick.endPrice;
    }

    // Ensure continuity between ticks
    for (let i = 1; i < ticks.length; i++) {
      ticks[i].startPrice = ticks[i - 1].endPrice;
    }

    return ticks;
  }

  private calculateSingleTick(
    tickStart: number,
    tickEnd: number,
    tickTransactions: any[],
    allTransactions: any[],
    previousEndPrice: number | null = null,
  ): TickData {
    let startPrice: number;
    let endPrice: number;
    let highestPrice: number;
    let lowestPrice: number;

    if (tickTransactions.length > 0) {
      // Calculate prices from actual transactions
      const prices = tickTransactions.map((tx) =>
        this.calculateTransactionPrice(tx),
      );

      // Use previousEndPrice for continuity if available, otherwise calculate startPrice
      startPrice =
        previousEndPrice !== null
          ? previousEndPrice
          : this.getStartPrice(tickStart, tickTransactions, allTransactions);
      endPrice = this.getEndPrice(tickEnd, tickTransactions, allTransactions);

      // Include startPrice in high/low calculations for proper range
      const allPrices = [startPrice, ...prices];
      highestPrice = Math.max(...allPrices);
      lowestPrice = Math.min(...allPrices);
    } else {
      // No transactions in this interval - use interpolation
      const interpolatedStartPrice =
        previousEndPrice !== null
          ? previousEndPrice
          : this.interpolatePrice(tickStart, allTransactions);
      const interpolatedEndPrice = this.interpolatePrice(
        tickEnd,
        allTransactions,
      );

      startPrice = interpolatedStartPrice;
      endPrice = interpolatedEndPrice;
      highestPrice = Math.max(interpolatedStartPrice, interpolatedEndPrice);
      lowestPrice = Math.min(interpolatedStartPrice, interpolatedEndPrice);
    }

    return {
      timestamp: tickStart,
      startPrice,
      endPrice,
      highestPrice,
      lowestPrice,
    };
  }

  private calculateTransactionPrice(transaction: any): number {
    // Calculate price based on SOL amount, token amount, and SOL price in USD
    if (!transaction.token_amount || transaction.token_amount === 0) {
      return transaction.price || 0;
    }

    // Handle different possible data formats
    let solAmount: number;
    if (transaction.sol_amount > 1_000_000) {
      // Assume it's in lamports, convert to SOL
      solAmount = transaction.sol_amount / 1_000_000_000;
    } else {
      // Assume it's already in SOL
      solAmount = transaction.sol_amount;
    }

    const tokenAmount = transaction.token_amount;
    const solPriceUsd = transaction.price;

    if (tokenAmount === 0 || solPriceUsd === 0) {
      return 0;
    }

    // Price per token in USD
    return (solAmount * solPriceUsd) / tokenAmount;
  }

  private getStartPrice(
    tickStart: number,
    tickTransactions: any[],
    allTransactions: any[],
  ): number {
    // Check if there's a transaction exactly at tick start
    const exactTransaction = tickTransactions.find(
      (tx) => tx.timestamp === tickStart,
    );
    if (exactTransaction) {
      return this.calculateTransactionPrice(exactTransaction);
    }

    // Use the first transaction in the interval
    if (tickTransactions.length > 0) {
      return this.calculateTransactionPrice(tickTransactions[0]);
    }

    // Interpolate if no transactions in interval
    return this.interpolatePrice(tickStart, allTransactions);
  }

  private getEndPrice(
    tickEnd: number,
    tickTransactions: any[],
    allTransactions: any[],
  ): number {
    // Check if there's a transaction exactly at tick end
    const exactTransaction = allTransactions.find(
      (tx) => tx.timestamp === tickEnd,
    );
    if (exactTransaction) {
      return this.calculateTransactionPrice(exactTransaction);
    }

    // Use the last transaction in the interval
    if (tickTransactions.length > 0) {
      return this.calculateTransactionPrice(
        tickTransactions[tickTransactions.length - 1],
      );
    }

    // Interpolate if no transactions in interval
    return this.interpolatePrice(tickEnd, allTransactions);
  }

  private interpolatePrice(targetTime: number, allTransactions: any[]): number {
    if (allTransactions.length === 0) return 0;

    // Find the nearest transactions before and after the target time
    let beforeTx = null;
    let afterTx = null;

    for (const tx of allTransactions) {
      if (tx.timestamp <= targetTime) {
        beforeTx = tx;
      } else if (tx.timestamp > targetTime && !afterTx) {
        afterTx = tx;
        break;
      }
    }

    // If no transaction before, use the first transaction's price
    if (!beforeTx && afterTx) {
      return this.calculateTransactionPrice(afterTx);
    }

    // If no transaction after, use the last transaction's price
    if (beforeTx && !afterTx) {
      return this.calculateTransactionPrice(beforeTx);
    }

    // If we have both, interpolate linearly
    if (beforeTx && afterTx) {
      const beforePrice = this.calculateTransactionPrice(beforeTx);
      const afterPrice = this.calculateTransactionPrice(afterTx);
      const timeDiff = afterTx.timestamp - beforeTx.timestamp;
      const targetDiff = targetTime - beforeTx.timestamp;
      const ratio = timeDiff > 0 ? targetDiff / timeDiff : 0;

      return beforePrice + (afterPrice - beforePrice) * ratio;
    }

    // Fallback to first transaction price
    return this.calculateTransactionPrice(allTransactions[0]);
  }
}
