import { Module } from '@nestjs/common';
import { SwapTransactionPumpfunService } from './swap-transaction-pumpfun.service';
import { SwapTransactionPumpfunRepository } from './repositories/swap-transaction-pumpfun.repository';
import { RedisModule } from '../../redis/redis.module';
import { PairModule } from '../../pair/pumpfun/pair.module';
import { QueueModule } from '../../queue/queue.module';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { QueueConfig } from '../../queue/queue.interface';
import { SolanaModule } from '../../solana/solana.module';
import { DefaultJobOptions } from 'bullmq';
import { NewMultipleTradeTransactionProcessor } from './processor/new-multiple-trade-transaction.processor';
import { QuestdbModule } from 'src/questdb/questdb.module';
import { QuestdbSenderService } from 'src/questdb/questdb-sender.service';

@Module({
  imports: [
    QueueModule.register({
      queues: [PumpFunQueue.NEW_MULTIPLE_TRADE_TRANSACTION].map(
        (queueName) =>
          ({
            name: queueName,
            defaultJobOptions: {
              attempts: 5,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
              removeOnFail: false,
            } as DefaultJobOptions,
          }) as QueueConfig,
      ),
    }),
    RedisModule,
    PairModule,
    SolanaModule,
    QuestdbModule,
  ],
  providers: [
    SwapTransactionPumpfunService,
    SwapTransactionPumpfunRepository,
    NewMultipleTradeTransactionProcessor,
    QuestdbSenderService,
  ],
  exports: [SwapTransactionPumpfunService],
})
export class SwapTransactionPumpfunModule {}
