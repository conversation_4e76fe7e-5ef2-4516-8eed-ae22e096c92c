import { OnWorkerEvent, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../../queue/worker-host.processor';
import { Job } from 'bullmq';
import { SwapTransactionPumpfunService } from '../swap-transaction-pumpfun.service';
import { PairService } from '../../../pair/pumpfun/pair.service';
import { NewMultipleTradeTrxRequestDto } from '../dto/new-multiple-trade-trx-request.dto';
import { uniq } from 'lodash';
import { CreateSwapTransactionRequestDto } from '../dto/create-swap-transaction-request.dto';

@Processor(PumpFunQueue.NEW_MULTIPLE_TRADE_TRANSACTION, {
  concurrency: 100_000,
})
@Injectable()
export class NewMultipleTradeTransactionProcessor extends WorkerHostProcessor {
  private begin: number;

  constructor(
    @Inject()
    private readonly swapTransactionService: SwapTransactionPumpfunService,
    @Inject()
    private readonly pairService: PairService,
  ) {
    super();
  }

  async handleProcess(
    job: Job<NewMultipleTradeTrxRequestDto>,
  ): Promise<CreateSwapTransactionRequestDto[]> {
    this.begin = performance.now();

    const { tradeEventData } = job.data;
    const successfulTransactions =
      await this.swapTransactionService.handleMultipleTradeEventPumpfun(
        tradeEventData,
      );

    return successfulTransactions;
  }

  @OnWorkerEvent('completed')
  async onCompleted(
    job: Job<NewMultipleTradeTrxRequestDto, CreateSwapTransactionRequestDto[]>,
  ) {
    const { id, queueName, data, returnvalue } = job;

    this.logger.verbose(
      `Job id: ${id}, queue: ${queueName}, slot: ${data.slot}, elapsed: ${(performance.now() - this.begin).toFixed(0)}ms`,
    );

    try {
      const mints = uniq(returnvalue.map((tx) => tx.tokenAdress));
      await this.pairService.handleUpdateMultiplePumpFunPair(mints);
    } catch (error) {
      this.logger.error(
        `Failed to update pairs: ${error.message}, tokens: ${returnvalue
          .map((tx) => tx.tokenAdress)
          .join(', ')}`,
      );
    }
  }
}
