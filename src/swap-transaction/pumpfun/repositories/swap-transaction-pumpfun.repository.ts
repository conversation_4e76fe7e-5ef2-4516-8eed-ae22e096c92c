import { Injectable, Logger } from '@nestjs/common';

import { TxnsCount } from '../types/txns-count';
import { VolumeResult } from '../types/volume-result';
import { QuestdbPgService } from 'src/questdb/questdb-pg.service';
import {
  buildBuySellCountsQuery,
  buildBuySellVolumeQuery,
  buildVolume24hQuery,
} from 'src/questdb/questdb.query-builder';

@Injectable()
export class SwapTransactionPumpfunRepository {
  private readonly logger = new Logger(SwapTransactionPumpfunRepository.name);

  constructor(private readonly questdbPgService: QuestdbPgService) {}

  async findBuySellCountsTxnsByTokenAddress(
    tokenAddress: string,
  ): Promise<TxnsCount> {
    const query = buildBuySellCountsQuery(tokenAddress);

    const results = await this.questdbPgService.query(query);

    let buyTotalTxns = 0;
    let sellTotalTxns = 0;

    for (const row of results) {
      if (row.trade_type === 'BUY') {
        buyTotalTxns = Number(row.total_count);
      } else if (row.trade_type === 'SELL') {
        sellTotalTxns = Number(row.total_count);
      }
    }

    return { buyTotalTxns, sellTotalTxns };
  }

  async findBuySellVolumeByMint(tokenAddress: string): Promise<VolumeResult> {
    const query = buildBuySellVolumeQuery(tokenAddress);

    const results = await this.questdbPgService.query(query);

    let buyTotalVolume = 0;
    let sellTotalVolume = 0;

    for (const row of results) {
      if (row.trade_type === 'BUY') {
        buyTotalVolume = Number(row.total_volume);
      } else if (row.trade_type === 'SELL') {
        sellTotalVolume = Number(row.total_volume);
      }
    }

    return { buyTotalVolume, sellTotalVolume };
  }

  async findVol24hByMint(tokenAddress: string): Promise<number> {
    const query = buildVolume24hQuery(tokenAddress);

    const result = await this.questdbPgService.query(query);

    const volume = result?.[0]?.volume24h;
    return volume !== null && volume !== undefined ? Number(volume) : 0;
  }
}
