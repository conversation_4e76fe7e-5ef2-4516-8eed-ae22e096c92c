import { ArgumentsHost, Catch, HttpStatus, Logger } from '@nestjs/common';
import { BaseWsExceptionFilter, WsException } from '@nestjs/websockets';
import { AppError } from '../errors/app-error';
import { ErrorService } from '../errors/error.service';
import { v4 as uuidv4 } from 'uuid';

@Catch(WsException)
export class WsExceptionFilter extends BaseWsExceptionFilter {
  private readonly logger = new Logger(WsExceptionFilter.name);

  constructor(private readonly errorService: ErrorService) {
    super();
  }

  catch(exception: WsException, host: ArgumentsHost) {
    const client = host.switchToWs().getClient();
    const data = host.switchToWs().getData();
    const error = exception.getError();
    const traceId = uuidv4();

    let errorResponse;

    if (error instanceof AppError) {
      // If the error is already an AppError, use it directly
      errorResponse = error.toResponse();
    } else {
      // For standard WsExceptions, convert to our error format
      let message: string;
      let details: Record<string, any> = {};

      if (typeof error === 'string') {
        message = error;
      } else if (typeof error === 'object') {
        const errorObj = error as Record<string, any>;
        message = errorObj.message ?? 'WebSocket error';
        details = errorObj;
      } else {
        message = 'Unknown WebSocket error';
      }

      const appError = this.errorService.createCommonError(
        HttpStatus.BAD_REQUEST,
        message,
        details,
      );
      appError.withTraceId(traceId);
      errorResponse = appError.toResponse();
    }

    this.logger.error(`WebSocket Error: ${errorResponse.error.message}`, {
      clientId: client.id,
      requestId: data?.rid,
      traceId: errorResponse.error.trace_id,
      errorCode: errorResponse.error.code,
      stack: exception.stack,
    });

    client.send(
      JSON.stringify({
        event: 'error',
        data: {
          id: client.id,
          rid: data?.rid,
          code: errorResponse.error.code,
          message: errorResponse.error.message,
          details: errorResponse.error.details,
          trace_id: errorResponse.error.trace_id,
        },
      }),
    );
  }
}
