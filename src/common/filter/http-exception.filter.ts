import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AppError } from '../errors/app-error';
import { ErrorService } from '../errors/error.service';
import { v4 as uuidv4 } from 'uuid';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  constructor(private readonly errorService: ErrorService) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const traceId = uuidv4();

    // Get error details from exception
    let errorResponse;

    if (exception instanceof AppError) {
      // If it's already an AppError, use it directly
      errorResponse = exception.toResponse();
    } else {
      // For standard HttpExceptions, convert to our error format
      const exceptionResponse = exception.getResponse();
      let message: string;
      const details: Record<string, any> = {};

      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object') {
        const responseObj = exceptionResponse as Record<string, any>;
        message = responseObj.message ?? exception.message;

        // Include validation errors if they exist
        if (responseObj.message && Array.isArray(responseObj.message)) {
          details.validation = responseObj.message;
          message = 'Validation failed';
        }
      } else {
        message = exception.message;
      }

      const commonError = this.errorService.createCommonError(
        status,
        message,
        details,
      );
      commonError.withTraceId(traceId);
      errorResponse = commonError.toResponse();
    }

    // Add request information for logging purposes
    const logInfo = {
      method: request.method,
      path: request.url,
      statusCode: status,
      traceId: errorResponse.error.trace_id,
      errorCode: errorResponse.error.code,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
    };

    this.logger.error(`HTTP Error: ${errorResponse.error.message}`, {
      ...logInfo,
      stack: exception.stack,
    });

    response.status(status).json(errorResponse);
  }
}
