import {
  Catch,
  RpcExceptionFilter,
  ArgumentsHost,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { RpcException } from '@nestjs/microservices';
import { ErrorService } from '../errors/error.service';
import { AppError } from '../errors/app-error';
import { v4 as uuidv4 } from 'uuid';

@Catch(RpcException)
export class GrpcExceptionFilter implements RpcExceptionFilter<RpcException> {
  private readonly logger = new Logger(GrpcExceptionFilter.name);

  constructor(private readonly errorService: ErrorService) {}

  catch(exception: RpcException, _host: ArgumentsHost): Observable<any> {
    const error = exception.getError();
    const traceId = uuidv4();

    let errorResponse;

    // Handle different error types
    if (error instanceof AppError) {
      // If the error is already an AppError, use it directly
      errorResponse = error.toResponse();
    } else {
      // For standard RpcExceptions, convert to our error format
      let message: string;
      let details: Record<string, any> = {};
      let status = HttpStatus.INTERNAL_SERVER_ERROR;

      if (typeof error === 'string') {
        message = error;
      } else if (typeof error === 'object') {
        const errorObj = error as Record<string, any>;
        message = errorObj.message ?? 'gRPC error';
        details = errorObj;

        // Try to parse HTTP status if available
        if (errorObj.code && typeof errorObj.code === 'number') {
          status = errorObj.code;
        }
      } else {
        message = 'Unknown gRPC error';
      }

      const appError = this.errorService.createCommonError(
        status,
        message,
        details,
      );
      appError.withTraceId(traceId);
      errorResponse = appError.toResponse();
    }

    this.logger.error(`gRPC Error: ${errorResponse.error.message}`, {
      traceId: errorResponse.error.trace_id,
      errorCode: errorResponse.error.code,
      stack: exception.stack,
    });

    return throwError(() => errorResponse);
  }
}
