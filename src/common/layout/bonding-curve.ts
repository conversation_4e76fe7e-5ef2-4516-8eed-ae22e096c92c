import { struct } from '@solana/buffer-layout';
import { bool, u64 } from '@solana/buffer-layout-utils';

export const BONDING_CURVE_LAYOUT = struct<BondingCurveLayout>([
  u64('virtualTokenReserves'),
  u64('virtualSolReserves'),
  u64('realTokenReserves'),
  u64('realSolReserves'),
  u64('tokenTotalSupply'),
  bool('complete'),
]);

export interface BondingCurveLayout {
  virtualTokenReserves: bigint;
  virtualSolReserves: bigint;
  realTokenReserves: bigint;
  realSolReserves: bigint;
  tokenTotalSupply: bigint;
  complete: boolean;
}
