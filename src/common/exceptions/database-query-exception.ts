import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_SERVER_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when there's a database query error
 */
export class DatabaseQueryException extends AppError {
  constructor(message: string, details?: Record<string, any>) {
    super(
      HttpStatus.INTERNAL_SERVER_ERROR,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_SERVER_ERROR_ID.DATABASE_QUERY_ERROR,
      `Database query error: ${message}`,
      details,
    );
  }
}
