import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { COMMON_CUSTOM_ERROR_ID } from '../constant/error-codes';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';

/**
 * Exception thrown when validation fails
 */
export class ValidationException extends AppError {
  constructor(message: string, details?: Record<string, any>) {
    super(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.VALIDATION,
      message,
      details,
    );
  }
}
