import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when a pair is not found
 */
export class PairNotFoundException extends AppError {
  constructor(pairAddress: string) {
    super(
      HttpStatus.NOT_FOUND,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.PAIR_NOT_FOUND,
      `Pair not found: ${pairAddress}`,
      { pairAddress },
    );
  }
}
