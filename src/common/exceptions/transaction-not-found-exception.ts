import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when a transaction is not found
 */
export class TransactionNotFoundException extends AppError {
  constructor(transactionHash: string) {
    super(
      HttpStatus.NOT_FOUND,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.TRANSACTION_NOT_FOUND,
      `Transaction not found: ${transactionHash}`,
      { transactionHash },
    );
  }
}
