import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { COMMON_CUSTOM_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when there's an external service error
 */
export class ExternalServiceException extends AppError {
  constructor(
    serviceName: string,
    message: string,
    details?: Record<string, any>,
  ) {
    const errorDetails = {
      serviceName,
      ...details,
    };

    super(
      HttpStatus.INTERNAL_SERVER_ERROR,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.EXTERNAL,
      `External service error (${serviceName}): ${message}`,
      errorDetails,
    );
  }
}
