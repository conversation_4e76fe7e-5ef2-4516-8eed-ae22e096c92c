import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { COMMON_CUSTOM_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when there's a database error
 */
export class DatabaseException extends AppError {
  constructor(message: string, details?: Record<string, any>) {
    super(
      HttpStatus.INTERNAL_SERVER_ERROR,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.DATABASE,
      `Database error: ${message}`,
      details,
    );
  }
}
