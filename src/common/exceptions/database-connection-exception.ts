import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_SERVER_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when there's a database connection error
 */
export class DatabaseConnectionException extends AppError {
  constructor(message: string, details?: Record<string, any>) {
    super(
      HttpStatus.INTERNAL_SERVER_ERROR,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_SERVER_ERROR_ID.DATABASE_CONNECTION_ERROR,
      `Database connection error: ${message}`,
      details,
    );
  }
}
