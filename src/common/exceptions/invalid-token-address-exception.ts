import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when a token address format is invalid
 */
export class InvalidTokenAddressException extends AppError {
  constructor(tokenAddress: string) {
    super(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.INVALID_TOKEN_ADDRESS,
      `Invalid token address format: ${tokenAddress}`,
      { tokenAddress },
    );
  }
}
