import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { COMMON_CUSTOM_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown for business logic errors
 */
export class BusinessLogicException extends AppError {
  constructor(message: string, details?: Record<string, any>) {
    super(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.BUSINESS,
      message,
      details,
    );
  }
}
