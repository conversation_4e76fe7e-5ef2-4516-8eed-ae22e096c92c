import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when a pair address format is invalid
 */
export class InvalidPairAddressException extends AppError {
  constructor(pairAddress: string) {
    super(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.INVALID_PAIR_ADDRESS,
      `Invalid pair address format: ${pairAddress}`,
      { pairAddress },
    );
  }
}
