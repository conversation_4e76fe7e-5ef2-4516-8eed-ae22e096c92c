import { HttpStatus } from '@nestjs/common';
import { AppError } from '../errors/app-error';
import { POOL_DISCOVERY_SERVICE_ID } from '../errors/error.service';
import { POOL_DISCOVERY_CLIENT_ERROR_ID } from '../constant/error-codes';

/**
 * Exception thrown when a token is not found
 */
export class TokenNotFoundException extends AppError {
  constructor(tokenAddress: string) {
    super(
      HttpStatus.NOT_FOUND,
      POOL_DISCOVERY_SERVICE_ID,
      POOL_DISCOVERY_CLIENT_ERROR_ID.TOKEN_NOT_FOUND,
      `Token not found: ${tokenAddress}`,
      { tokenAddress },
    );
  }
}
