import { HttpStatus, Injectable } from '@nestjs/common';
import { AppError } from './app-error';
import {
  SERVICE_ID,
  COMMON_CLIENT_ERROR_ID,
  COMMON_SERVER_ERROR_ID,
  COMMON_CUSTOM_ERROR_ID,
} from '../constant/error-codes';

/**
 * Service identifier for Pool Discovery Service
 */
export const POOL_DISCOVERY_SERVICE_ID = SERVICE_ID.POOL_DISCOVERY;

@Injectable()
export class ErrorService {
  /**
   * Create a new service-specific error
   */
  createError(
    status: HttpStatus,
    serviceId: string,
    errorId: string,
    message: string,
    details?: Record<string, any>,
  ): AppError {
    return new AppError(status, serviceId, errorId, message, details);
  }

  /**
   * Create a common error with standard error IDs
   */
  createCommonError(
    status: HttpStatus,
    message?: string,
    details?: Record<string, any>,
  ): AppError {
    // Use the standardized error IDs for common errors
    let errorId: string;
    let defaultMessage: string;

    if (status >= 400 && status < 500) {
      // Client errors
      switch (status) {
        case HttpStatus.BAD_REQUEST:
          errorId = COMMON_CLIENT_ERROR_ID.BAD_REQUEST;
          defaultMessage = 'Bad Request';
          break;
        case HttpStatus.UNAUTHORIZED:
          errorId = COMMON_CLIENT_ERROR_ID.UNAUTHORIZED;
          defaultMessage = 'Unauthorized';
          break;
        case HttpStatus.PAYMENT_REQUIRED:
          errorId = COMMON_CLIENT_ERROR_ID.PAYMENT_REQUIRED;
          defaultMessage = 'Payment Required';
          break;
        case HttpStatus.FORBIDDEN:
          errorId = COMMON_CLIENT_ERROR_ID.FORBIDDEN;
          defaultMessage = 'Forbidden';
          break;
        case HttpStatus.NOT_FOUND:
          errorId = COMMON_CLIENT_ERROR_ID.NOT_FOUND;
          defaultMessage = 'Not Found';
          break;
        case HttpStatus.METHOD_NOT_ALLOWED:
          errorId = COMMON_CLIENT_ERROR_ID.METHOD_NOT_ALLOWED;
          defaultMessage = 'Method Not Allowed';
          break;
        case HttpStatus.NOT_ACCEPTABLE:
          errorId = COMMON_CLIENT_ERROR_ID.NOT_ACCEPTABLE;
          defaultMessage = 'Not Acceptable';
          break;
        case HttpStatus.REQUEST_TIMEOUT:
          errorId = COMMON_CLIENT_ERROR_ID.REQUEST_TIMEOUT;
          defaultMessage = 'Request Timeout';
          break;
        case HttpStatus.CONFLICT:
          errorId = COMMON_CLIENT_ERROR_ID.CONFLICT;
          defaultMessage = 'Conflict';
          break;
        case HttpStatus.GONE:
          errorId = COMMON_CLIENT_ERROR_ID.GONE;
          defaultMessage = 'Gone';
          break;
        case HttpStatus.UNPROCESSABLE_ENTITY:
          errorId = COMMON_CLIENT_ERROR_ID.UNPROCESSABLE_ENTITY;
          defaultMessage = 'Unprocessable Entity';
          break;
        case HttpStatus.TOO_MANY_REQUESTS:
          errorId = COMMON_CLIENT_ERROR_ID.TOO_MANY_REQUESTS;
          defaultMessage = 'Too Many Requests';
          break;
        default:
          errorId = COMMON_CLIENT_ERROR_ID.BAD_REQUEST;
          defaultMessage = 'Client Error';
      }
    } else if (status >= 500) {
      // Server errors
      switch (status) {
        case HttpStatus.INTERNAL_SERVER_ERROR:
          errorId = COMMON_SERVER_ERROR_ID.INTERNAL_SERVER_ERROR;
          defaultMessage = 'Internal Server Error';
          break;
        case HttpStatus.NOT_IMPLEMENTED:
          errorId = COMMON_SERVER_ERROR_ID.NOT_IMPLEMENTED;
          defaultMessage = 'Not Implemented';
          break;
        case HttpStatus.BAD_GATEWAY:
          errorId = COMMON_SERVER_ERROR_ID.BAD_GATEWAY;
          defaultMessage = 'Bad Gateway';
          break;
        case HttpStatus.SERVICE_UNAVAILABLE:
          errorId = COMMON_SERVER_ERROR_ID.SERVICE_UNAVAILABLE;
          defaultMessage = 'Service Unavailable';
          break;
        case HttpStatus.GATEWAY_TIMEOUT:
          errorId = COMMON_SERVER_ERROR_ID.GATEWAY_TIMEOUT;
          defaultMessage = 'Gateway Timeout';
          break;
        case HttpStatus.HTTP_VERSION_NOT_SUPPORTED:
          errorId = COMMON_SERVER_ERROR_ID.HTTP_VERSION_NOT_SUPPORTED;
          defaultMessage = 'HTTP Version Not Supported';
          break;
        default:
          errorId = COMMON_SERVER_ERROR_ID.INTERNAL_SERVER_ERROR;
          defaultMessage = 'Server Error';
      }
    } else {
      errorId = COMMON_CLIENT_ERROR_ID.BAD_REQUEST;
      defaultMessage = 'Unknown Error';
    }

    return new AppError(
      status,
      POOL_DISCOVERY_SERVICE_ID,
      errorId,
      message || defaultMessage,
      details,
    );
  }

  /**
   * Create a new validation error (400 Bad Request)
   */
  createValidationError(
    message: string,
    details?: Record<string, any>,
  ): AppError {
    return new AppError(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.VALIDATION,
      message,
      details,
    );
  }

  /**
   * Create a new database error (500 Internal Server Error)
   */
  createDatabaseError(
    message: string,
    details?: Record<string, any>,
  ): AppError {
    return new AppError(
      HttpStatus.INTERNAL_SERVER_ERROR,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.DATABASE,
      message,
      details,
    );
  }

  /**
   * Create a new external service error (500 Internal Server Error)
   */
  createExternalError(
    message: string,
    details?: Record<string, any>,
  ): AppError {
    return new AppError(
      HttpStatus.INTERNAL_SERVER_ERROR,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.EXTERNAL,
      message,
      details,
    );
  }

  /**
   * Create a new business logic error (400 Bad Request)
   */
  createBusinessError(
    message: string,
    details?: Record<string, any>,
  ): AppError {
    return new AppError(
      HttpStatus.BAD_REQUEST,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.BUSINESS,
      message,
      details,
    );
  }

  /**
   * Create a new security error (403 Forbidden)
   */
  createSecurityError(
    message: string,
    details?: Record<string, any>,
  ): AppError {
    return new AppError(
      HttpStatus.FORBIDDEN,
      POOL_DISCOVERY_SERVICE_ID,
      COMMON_CUSTOM_ERROR_ID.SECURITY,
      message,
      details,
    );
  }

  /**
   * Create a new not found error (404 Not Found)
   */
  createNotFoundError(
    resourceType: string,
    identifier: string,
    details?: Record<string, any>,
  ): AppError {
    const message = `${resourceType} not found`;
    const errorDetails = {
      resource_type: resourceType,
      identifier,
      ...details,
    };

    return this.createCommonError(HttpStatus.NOT_FOUND, message, errorDetails);
  }
}

/**
 * Helper function to send a standardized success response
 */
export function sendSuccessResponse(data: any = null): Record<string, any> {
  return {
    success: true,
    error: null,
    data,
  };
}
