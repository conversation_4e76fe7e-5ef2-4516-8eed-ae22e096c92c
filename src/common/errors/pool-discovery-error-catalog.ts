/**
 * Error catalog for Pool Discovery Service (Service ID: 06)
 * Contains all defined error codes and their descriptions
 */
export class PoolDiscoveryErrorCatalog {
  // Client error codes (4xx)
  static readonly CLIENT_ERRORS = {
    // 400 Bad Request errors
    '0001': 'Invalid token address format',
    '0002': 'Invalid pair address format',
    '0003': 'Invalid request parameters',
    '0004': 'Invalid pagination parameters',
    '0005': 'Invalid sort direction',
    '0006': 'Invalid date range',
    '0007': 'Invalid time interval',

    // 404 Not Found errors
    '0100': 'Token not found',
    '0101': 'Pair not found',
    '0102': 'Transaction not found',

    // 409 Conflict errors
    '0200': 'Token already exists',
    '0201': 'Pair already exists',
  };

  // Server error codes (5xx)
  static readonly SERVER_ERRORS = {
    // 500 Internal Server Error - Database related
    '5001': 'Failed to save token',
    '5002': 'Failed to save pair',
    '5003': 'Failed to save transaction',
    '5004': 'Failed to update pair details',
    '5005': 'Database connection error',
    '5006': 'Database query error',

    // 500 Internal Server Error - Queue related
    '5100': 'Failed to queue new token',
    '5101': 'Failed to queue new pair',
    '5102': 'Failed to queue transaction',
    '5103': 'Failed to process queue job',

    // 500 Internal Server Error - External service related
    '5200': 'Failed to fetch blockchain data',
    '5201': 'Failed to process blockchain event',
    '5202': 'Solana RPC error',

    // 503 Service Unavailable
    '5300': 'Service temporarily unavailable',
  };
}
