import { HttpException, HttpStatus } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

/**
 * Standardized application error that follows the error convention.
 * Error codes follow the format: XXXYYZZZZ where:
 * - XXX: HTTP status code (3 digits)
 * - YY: Service identifier (2 digits)
 * - ZZZZ: Error identifier within the service (4 digits)
 */
export class AppError extends HttpException {
  private readonly _code: string;
  private _details: Record<string, any> = {};
  private _traceId: string;

  constructor(
    status: HttpStatus,
    serviceId: string,
    errorId: string,
    message: string,
    details?: Record<string, any>,
    traceId?: string,
  ) {
    super(message, status);
    this._code = this.formatErrorCode(status, serviceId, errorId);
    this._details = details || {};
    this._traceId = traceId || uuidv4();
  }

  /**
   * Get the error code in the format XXXYYZZZZ
   */
  get code(): string {
    return this._code;
  }

  /**
   * Get the additional error details
   */
  get details(): Record<string, any> {
    return this._details;
  }

  /**
   * Get the trace ID for this error
   */
  get traceId(): string {
    return this._traceId;
  }

  /**
   * Format the error code based on HTTP status, service ID, and error ID
   */
  private formatErrorCode(
    status: HttpStatus,
    serviceId: string,
    errorId: string,
  ): string {
    const statusCode = status.toString().padStart(3, '0');
    const paddedServiceId = serviceId.padStart(2, '0');
    const paddedErrorId = errorId.padStart(4, '0');

    return `${statusCode}${paddedServiceId}${paddedErrorId}`;
  }

  /**
   * Add additional details to the error
   */
  withDetails(details: Record<string, any>): this {
    this._details = {
      ...this._details,
      ...details,
    };
    return this;
  }

  /**
   * Set a specific trace ID for this error
   */
  withTraceId(traceId: string): this {
    this._traceId = traceId;
    return this;
  }

  /**
   * Create the standard error response structure
   */
  toResponse(): Record<string, any> {
    return {
      success: false,
      error: {
        code: this.code,
        message: this.message,
        details: this.details,
        trace_id: this.traceId,
      },
      data: null,
    };
  }
}
