import {
  <PERSON><PERSON><PERSON>ler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { tap } from 'rxjs/operators';

@Injectable()
export class GprcLoggingInterceptor implements NestInterceptor {
  private readonly logger: Logger = new Logger(GprcLoggingInterceptor.name);
  intercept(context: ExecutionContext, next: CallHandler) {
    this.logger.log('Incoming gRPC call...');
    return (
      next
        .handle()
        //TODO
        .pipe(tap((data) => this.logger.log('Outgoing response:', data)))
    );
  }
}
