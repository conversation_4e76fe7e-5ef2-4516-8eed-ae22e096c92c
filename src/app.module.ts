import { Inject, Logger, Module, OnModuleDestroy } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { appConfig } from './config/app.config';
import { solanaConfig } from './config/solana.config';
import { ScheduleModule, SchedulerRegistry } from '@nestjs/schedule';
import { SwapTransactionPumpfunModule } from './swap-transaction/pumpfun/swap-transaction-pumpfun.module';
import { AuthModule } from './auth/auth.module';
import { TokenModule } from './token/pumpfun/token.module';
import { PairModule } from './pair/pumpfun/pair.module';
import { queueConfig } from './config/queue.config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import redisConfig from './config/redis.config';
import bullConfig from './config/bullmq.config';
import { QuestdbModule } from './questdb/questdb.module';
import { KafkaConsumerModule } from './kafka/kafka.consumer.module';
import kafkaConfig from './config/kafka.config';
import grpcConfig from './config/grpc.config';
import { questdbPgConfig, questdbSenderConfig } from './config/questdb.config';
import { mongoDbConfig } from './config/mongo.config';
import { MongooseModule } from '@nestjs/mongoose';
import { GrpcPairModule } from './grpc/grpc-pair.module';

@Module({
  imports: [
    EventEmitterModule.forRoot({
      newListener: true,
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
      load: [
        appConfig,
        grpcConfig,
        solanaConfig,
        mongoDbConfig,
        redisConfig,
        bullConfig,
        kafkaConfig,
        questdbPgConfig,
        questdbSenderConfig,
      ],
    }),
    ScheduleModule.forRoot(),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const uri = configService.get<string>('mongoDb.uri');
        const dbName = configService.get<string>('mongoDb.dbName');
        const username = configService.get<string>('mongoDb.username');
        const password = configService.get<string>('mongoDb.password');
        const logger = new Logger('Mongoose');

        logger.log(
          `MongoDB connecting to [${uri}], database [${dbName}], user [${username}]`,
        );

        return {
          uri,
          dbName,
          auth: { username, password },
          authSource: 'admin', // often needed for non-default users
        };
      },
      inject: [ConfigService],
    }),
    queueConfig,
    SwapTransactionPumpfunModule,
    AuthModule,
    TokenModule,
    PairModule,
    KafkaConsumerModule,
    QuestdbModule,
    GrpcPairModule,
  ],
})
export class AppModule implements OnModuleDestroy {
  private readonly logger = new Logger(AppModule.name);

  constructor(
    @Inject(SchedulerRegistry)
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {}

  onModuleDestroy(): any {
    const cronJobs = this.schedulerRegistry.getCronJobs();
    cronJobs.forEach((job, jobName) => {
      job.stop();
      this.schedulerRegistry.deleteCronJob(jobName);
      this.logger.log(`Deleted Job [${jobName}].`);
    });
  }
}
