import { <PERSON><PERSON>, <PERSON>du<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import appConfig from './config/app.config';
import solanaConfig from './config/solana.config';
import { ScheduleModule } from '@nestjs/schedule';
import { TokenModule } from './modules/token/token.module';
import { PairModule } from './modules/pair/pair.module';
import { queueConfig } from './config/queue.config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import redisConfig from './config/redis.config';
import bullConfig from './config/bullmq.config';
import { QuestdbModule } from './modules/questdb/questdb.module';
import { KafkaConsumerModule } from './modules/kafka/kafka.consumer.module';
import kafkaConfig from './config/kafka.config';
import grpcConfig from './config/grpc.config';
import { questdbPgConfig, questdbSenderConfig } from './config/questdb.config';
import mongoDbConfig from './config/mongo.config';
import { MongooseModule } from '@nestjs/mongoose';
import { DatabaseModule } from './database/database.module';
import { SwapTransactionModule } from './modules/swap-transaction/swap-transaction.module';
import { ErrorModule } from './common/errors/error.module';

@Module({
  imports: [
    EventEmitterModule.forRoot({
      newListener: true,
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
      load: [
        appConfig,
        grpcConfig,
        solanaConfig,
        mongoDbConfig,
        redisConfig,
        bullConfig,
        kafkaConfig,
        questdbPgConfig,
        questdbSenderConfig,
      ],
    }),
    ErrorModule,
    ScheduleModule.forRoot(),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const uri = configService.get<string>('mongoDb.uri');
        const dbName = configService.get<string>('mongoDb.dbName');
        const username = configService.get<string>('mongoDb.username');
        const password = configService.get<string>('mongoDb.password');
        const logger = new Logger('Mongoose');

        logger.log(
          `MongoDB connecting to [${uri}], database [${dbName}], user [${username}]`,
        );

        return {
          uri,
          dbName,
          auth: { username, password },
          authSource: 'admin', // often needed for non-default users
        };
      },
      inject: [ConfigService],
    }),
    queueConfig,
    TokenModule,
    PairModule,
    SwapTransactionModule,
    KafkaConsumerModule,
    QuestdbModule,
    DatabaseModule,
  ],
})
export class AppModule {}
