import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from '../config/app.config';
import { Keypair } from '@solana/web3.js';

@Injectable()
export class AuthService implements OnModuleInit {
  private readonly logger = new Logger(AuthService.name);

  private wsAuthToken: string;

  constructor(private readonly configService: ConfigService) {
    const appConfig = configService.get<AppConfig>('app');
    this.wsAuthToken = appConfig.wsAuthToken;
  }

  onModuleInit(): any {
    if (!this.wsAuthToken) {
      const newToken = this.generateAuthToken();
      this.wsAuthToken = newToken;
      this.logger.log(`New Websocket Token [${newToken}]`);
    }
  }

  verifyToken(token: string): boolean {
    return token === this.wsAuthToken;
  }

  private generateAuthToken(): string {
    return new Keypair().publicKey.toString();
  }
}
