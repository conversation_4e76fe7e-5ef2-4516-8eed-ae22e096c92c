import { Modu<PERSON> } from '@nestjs/common';
import { KafkaConsumerController } from './kafka.consumer.controller';
import { PairModule } from 'src/pair/pumpfun/pair.module';
import { SwapTransactionPumpfunModule } from 'src/swap-transaction/pumpfun/swap-transaction-pumpfun.module';
import { PumpfunHandler } from './handlers/pumpfun.handler';
import { ClientsModule } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { createKafkaConfig } from './kafka.factory';
import { KAFKA_CLIENT_NAME } from '../common/constant/kafka';
import { PumpswapHandler } from './handlers/pumpswap.handler';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: KAFKA_CLIENT_NAME,
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: createKafkaConfig,
      },
    ]),
    SwapTransactionPumpfunModule,
    PairModule,
  ],
  controllers: [KafkaConsumerController],
  providers: [PumpfunHandler, PumpswapHandler],
})
export class KafkaConsumerModule {}
