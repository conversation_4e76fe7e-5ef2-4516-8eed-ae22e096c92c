import { <PERSON>, Inject, Logger } from '@nestjs/common';
import { KafkaTopic } from '../common/constant/kafka';
import { EventPattern } from '@nestjs/microservices';
import { PumpfunHandler } from './handlers/pumpfun.handler';
import { PumpswapHandler } from './handlers/pumpswap.handler';

@Controller()
export class KafkaConsumerController {
  private readonly logger = new Logger(KafkaConsumerController.name);

  constructor(
    @Inject(PumpswapHandler)
    private readonly pumpSwapHandler: PumpswapHandler,
    @Inject(PumpfunHandler)
    private readonly pumpFunHandler: PumpfunHandler,
  ) {}

  @EventPattern(KafkaTopic.PUMP_SWAP)
  async handleEventPumpSwap(messages: any) {
    try {
      if (!messages) return;
      await this.pumpSwapHandler.handlePumpSwapTransactions(messages);
    } catch (error) {
      this.logger.warn(`Fail to handle data from grpc, ${error.message}`);
      this.logger.error(error.stack);
    }
  }

  @EventPattern(KafkaTopic.PUMP_FUN)
  async handleEventPumpFun(messages: any) {
    try {
      if (!messages) return;

      await this.pumpFunHandler.handlePumpFunTransactions(messages);
    } catch (error) {
      this.logger.warn(`Fail to handle data from grpc, ${error.message}`);
      this.logger.error(error.stack);
    }
  }
}
