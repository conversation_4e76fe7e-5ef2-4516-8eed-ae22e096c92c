import base58 from 'bs58';
import { TransactionData } from '../../common/type/transaction.data';
import { AccountKey } from '../../common/type/account.key';
import { InnerInstruction, Instruction } from '../../common/type/instruction';
import { PublicKey } from '@solana/web3.js';
import { PUMPFUN_PROGRAM_ID } from 'src/common/constant/pumpfun';

const parsePumpfunTransaction = (request: any): TransactionData => {
  const transaction = request.transaction;
  const meta = request.meta;

  const signatureBuffer = new Uint8Array(request.signature.data);
  const signature = base58.encode(signatureBuffer);

  const message = transaction.message;

  // 🔧 Normalize Buffer-wrapped accounts to number[]
  const instructions = transaction.message.instructions.map((ix: any) => ({
    ...ix,
    accounts:
      ix.accounts?.type === 'Buffer' && Array.isArray(ix.accounts.data)
        ? ix.accounts.data
        : [],
  }));
  const innerInstructions = meta.innerInstructions;
  const logMessages = meta.logMessages;
  const accountKeys = message.accountKeys.map(
    (accountKey: { type: string; data: number[] }) =>
      ({
        pubkey: base58.encode(new Uint8Array(accountKey.data)),
      }) as AccountKey,
  );

  const parsedInstructions = instructions.map((instruction: any) =>
    parsePumpfunInstruction(instruction, accountKeys),
  );

  const parsedInnerInstructions = innerInstructions.map(
    (innerInstruction: any) => {
      const index = innerInstruction.index;
      const instructions = innerInstruction.instructions.map(
        (instruction: any) => parsePumpfunInstruction(instruction, accountKeys),
      );
      return {
        index,
        instructions,
      } as InnerInstruction;
    },
  );

  return {
    isParsedData: false,
    instructions: parsedInstructions,
    logs: logMessages,
    signature: signature,
    innerInstructions: parsedInnerInstructions,
    accountKeys: accountKeys,
  };
};

const parsePumpfunInstruction = (
  instruction: any,
  accountKeys: AccountKey[],
): Instruction => {
  const accounts: string[] = [];
  try {
    for (const idx of instruction.accounts) {
      accounts.push(accountKeys[idx].pubkey);
    }
  } catch (e) {}
  const data = base58.encode(new Uint8Array(instruction.data.data));
  const programId = accountKeys[instruction.programIdIndex];
  return {
    accounts: accounts,
    data: data,
    programId: programId ? programId.pubkey : '',
  } as Instruction;
};

const parsePumpswapTransaction = (request: any): TransactionData => {
  const transaction = request.transaction;
  const meta = request.meta;

  const signatureBuffer = new Uint8Array(request.signature.data);
  const signature = base58.encode(signatureBuffer);

  const message = transaction.message;

  // 🔧 Normalize Buffer-wrapped accounts to number[]
  const instructions = transaction.message.instructions.map((ix: any) => ({
    ...ix,
    accounts:
      ix.accounts?.type === 'Buffer' && Array.isArray(ix.accounts.data)
        ? ix.accounts.data
        : [],
  }));
  const innerInstructions = meta.innerInstructions;
  const logMessages = meta.logMessages;
  const accountKeys = message.accountKeys.map(
    (accountKey: { type: string; data: number[] }) =>
      ({
        pubkey: base58.encode(new Uint8Array(accountKey.data)),
      }) as AccountKey,
  );

  const parsedInstructions = instructions.map((instruction: any) =>
    parsePumpswapInstruction(instruction, accountKeys),
  );

  const parsedInnerInstructions = innerInstructions.map(
    (innerInstruction: any) => {
      const index = innerInstruction.index;
      const instructions = innerInstruction.instructions.map(
        (instruction: any) =>
          parsePumpswapInstruction(instruction, accountKeys),
      );
      return {
        index,
        instructions,
      } as InnerInstruction;
    },
  );

  return {
    isParsedData: false,
    instructions: parsedInstructions,
    logs: logMessages,
    signature: signature,
    innerInstructions: parsedInnerInstructions,
    accountKeys: accountKeys,
  };
};

const parsePumpswapInstruction = (
  instruction: any,
  accountKeys: AccountKey[],
): Instruction => {
  const accounts: string[] = [];
  try {
    for (const idx of instruction.accounts) {
      accounts.push(accountKeys[idx].pubkey);
    }
  } catch (e) {
    //TODO get from account table lookup
  }
  const data = base58.encode(instruction.data);
  const programId = accountKeys[instruction.programIdIndex];
  return {
    accounts: accounts,
    data: data,
    programId: programId ? programId.pubkey : '',
  } as Instruction;
};

const findBondingCurveByTokenAddress = (tokenAddress: string) => {
  const mintAccount = new PublicKey(tokenAddress).toBytes();
  const [bondingCurve] = PublicKey.findProgramAddressSync(
    [Buffer.from('bonding-curve'), mintAccount],
    new PublicKey(PUMPFUN_PROGRAM_ID),
  );
  return bondingCurve;
};

export {
  parsePumpfunTransaction,
  parsePumpswapTransaction,
  findBondingCurveByTokenAddress,
};
