import { QuestdbPgService } from 'src/modules/questdb/questdb-pg.service';

export async function up(questdbPgService: QuestdbPgService): Promise<void> {
  // 1. Create the new table
  await questdbPgService.query(`
    CREATE TABLE IF NOT EXISTS SWAP_TRANSACTION_NEW (
      PAIR_ADDRESS VARCHAR NOT NULL,
      TOKEN_ADDRESS VARCHAR NOT NULL,
      SOL_AMOUNT LONG,
      TOKEN_AMOUNT LONG,
      USER_PUBLIC_KEY VARCHAR NOT NULL,
      TRADE_TYPE SYMBOL CAPACITY 8 CACHE,
      SIGNATURE VARCHAR NOT NULL,
      PAIR_PRICE_SOL DOUBLE,
      SOL_PRICE_USD DOUBLE,
      BLOCK LONG,
      TS TIMESTAMP
    ) TIMESTAMP(TS)
    PARTITION BY DAY WAL;
  `);

  // 2. Migrate existing data
  await questdbPgService.query(`
    INSERT INTO SWAP_TRANSACTION_NEW (
      PAIR_ADDRESS,
      <PERSON>OL_AMOUNT,
      TOKEN_AMOUNT,
      TRA<PERSON>_TYPE,
      <PERSON>IGNATURE,
      SOL_PRICE_USD,
      TS
    )
    SELECT
      PAIR_ADDRESS,
      SOL_AMOUNT,
      TOKEN_AMOUNT,
      TRADE_TYPE,
      SIGNATURE,
      SOL_PRICE_USD,
      TS
    FROM SWAP_TRANSACTION;
  `);

  // 3. Drop old table and rename new one
  await questdbPgService.query(`DROP TABLE SWAP_TRANSACTION;`);
  await questdbPgService.query(
    `RENAME TABLE SWAP_TRANSACTION_NEW TO SWAP_TRANSACTION;`,
  );
}

export async function down(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`DROP TABLE IF EXISTS SWAP_TRANSACTION;`);
}
