import { QuestdbPgService } from '../../modules/questdb/questdb-pg.service';

export async function up(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`
    CREATE TABLE IF NOT EXISTS PAIR_DETAIL (
      PAIR_ADDRESS VARCHAR NOT NULL,
      MARKET_CAP_USD DOUBLE,
      LIQUIDITY_USD DOUBLE,
      BUY_VOLUME_USD DOUBLE,
      SELL_VOLUME_USD DOUBLE,
      NUM_OF_BUY_TXS LONG,
      NUM_OF_SELL_TXS LONG,
      PROCESS DOUBLE,
      MARKET_CAP_USD_24H DOUBLE,
      VOLUME_USD_24H DOUBLE,
      NUM_OF_TXS_24H LONG,
      PRICE_USD DOUBLE,
      POOLED_SOL DOUBLE,
      TS TIMESTAMP
    ) TIMESTAMP(TS)
    PARTITION BY DAY
    WAL;
  `);
}

export async function down(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`
    DROP TABLE IF EXISTS PAIR_DETAIL;
  `);
}
