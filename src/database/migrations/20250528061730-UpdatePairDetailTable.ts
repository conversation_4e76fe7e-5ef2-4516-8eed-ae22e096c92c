import { QuestdbPgService } from 'src/modules/questdb/questdb-pg.service';

export async function up(questdbPgService: QuestdbPgService): Promise<void> {
  // 1. Create the new table with updated schema
  await questdbPgService.query(`
    CREATE TABLE IF NOT EXISTS PAIR_DETAIL_NEW (
       PAIR_ADDRESS VARCHAR NOT NULL,
       MARKET_CAP_USD DOUBLE,
       LIQUIDITY_USD DOUBLE,
       BUY_VOLUME_USD DOUBLE,
       SELL_VOLUME_USD DOUBLE,
       NUM_OF_SELLERS LONG,
       NUM_OF_BUYERS LONG,
       NUM_OF_SELL_TXS LONG,
       NUM_OF_BUY_TXS LONG,
       PROCESS DOUBLE,
       PRICE_USD DOUBLE,
       POOLED_SOL DOUBLE,
       CREATED_AT TIMESTAMP,
       UPDATED_AT TIMESTAMP,
       TS TIMESTAMP
    ) TIMESTAMP(TS)
      PARTITION BY DAY WAL;
  `);

  // 2. Copy matching data from old table (others will be NULL)
  await questdbPgService.query(`
    INSERT INTO PAIR_DETAIL_NEW (
      PAIR_ADDRESS,
      MARKET_CAP_USD,
      LIQUIDITY_USD,
      BUY_VOLUME_USD,
      SELL_VOLUME_USD,
      PROCESS,
      PRICE_USD,
      POOLED_SOL,
      TS
    )
    SELECT
      PAIR_ADDRESS,
      MARKET_CAP_USD,
      LIQUIDITY_USD,
      BUY_VOLUME_USD,
      SELL_VOLUME_USD,
      PROCESS,
      PRICE_USD,
      POOLED_SOL,
      TS
    FROM PAIR_DETAIL;
  `);

  // 3. Drop old table
  await questdbPgService.query(`DROP TABLE PAIR_DETAIL;`);

  // 4. Rename new table
  await questdbPgService.query(`RENAME TABLE PAIR_DETAIL_NEW TO PAIR_DETAIL;`);
}

export async function down(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`DROP TABLE IF EXISTS PAIR_DETAIL;`);
}
