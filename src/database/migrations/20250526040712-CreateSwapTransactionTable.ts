import { QuestdbPgService } from '../../modules/questdb/questdb-pg.service';

export async function up(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`
    CREATE TABLE IF NOT EXISTS SWAP_TRANSACTION (
      PAIR_ADDRESS VARCHAR NOT NULL,
      TOKEN_ADDRESS VARCHAR NOT NULL,
      SOL_AMOUNT LONG,
      TOKEN_AMOUNT LONG,
      TRADE_USER VARCHAR NOT NULL,
      TRADE_TYPE SYMBOL CAPACITY 8 CACHE,
      SIGNATU<PERSON> VARCHAR NOT NULL,
      SOL_PRICE_USD DOUBLE,
      TS TIMESTAMP
    ) TIMESTAMP(TS)
    PARTITION BY DAY WAL;
  `);
}

export async function down(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`
    DROP TABLE IF EXISTS SWAP_TRANSACTION;
  `);
}
