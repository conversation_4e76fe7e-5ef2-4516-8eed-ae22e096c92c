import { QuestdbPgService } from 'src/modules/questdb/questdb-pg.service';

export async function up(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`
     CREATE TABLE IF NOT EXISTS PAIR_DETAIL_TIMEFRAMES (
       PAIR_ADDRESS VARCHAR NOT NULL,
       TIME_INTERVAL SYMBOL CAPACITY 8 CACHE,
       BUY_VOLUME_USD DOUBLE,
       SELL_VOLUME_USD DOUBLE,
       NUM_OF_SELLERS LONG,
       NUM_OF_BUYERS LONG,
       NUM_OF_SELL_TXS LONG,
       NUM_OF_BUY_TXS LONG,
       UPDATED_AT TIMESTAMP,
       TS TIMESTAMP
    ) TIMESTAMP(TS)
      PARTITION BY DAY
      WAL;
  `);
}

export async function down(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(`
    DROP TABLE IF EXISTS PAIR_DETAIL_TIMEFRAMES;
  `);
}
