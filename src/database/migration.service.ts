import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { QuestdbPgService } from '../modules/questdb/questdb-pg.service';
import { Umzug } from 'umzug';
import * as path from 'path';
import * as fs from 'fs';
import { ConfigService } from '@nestjs/config';
import 'ts-node/register';

@Injectable()
export class MigrationService implements OnModuleInit {
  private readonly logger = new Logger(MigrationService.name);
  private readonly umzug: Umzug;

  constructor(
    questdbPgService: QuestdbPgService,
    private readonly configService: ConfigService, // Inject ConfigService
  ) {
    // Initialize Umzug with QuestDB connection
    this.umzug = new Umzug({
      migrations: {
        glob: 'src/database/migrations/*.ts',
        resolve: ({ name, path, context }) => {
          // Load the migration file dynamically with import()
          return {
            name,
            up: async () => {
              const migration = await import(path);
              return await migration.up(context);
            },
            down: async () => {
              const migration = await import(path);
              return await migration.down(context);
            },
          };
        },
      },
      context: questdbPgService,
      storage: {
        // Simple storage using a table in QuestDB
        logMigration: async ({ name }) => {
          try {
            await questdbPgService.query(
              /* SQL */ 'CREATE TABLE IF NOT EXISTS migrations (name text, executed_at timestamp) timestamp(executed_at) PARTITION by day',
            );
            await questdbPgService.query(
              /* SQL */ 'INSERT INTO migrations VALUES ($1, now())',
              [name],
            );
          } catch (error) {
            this.logger.error(`Failed to log migration ${name}`, error);
            throw error;
          }
        },
        unlogMigration: async ({ name }) => {
          try {
            await questdbPgService.query(
              `Create table migrations_cp AS (SELECT * FROM migrations where name != '${name}') timestamp(executed_at) PARTITION by day`,
            );
            await questdbPgService.query('DROP TABLE migrations');
            await questdbPgService.query(
              'RENAME table migrations_cp TO migrations;',
            );
          } catch (error) {
            this.logger.error(`Failed to unlog migration ${name}`, error);
            throw error;
          }
        },
        executed: async () => {
          try {
            // Create the migrations table if it doesn't exist
            await questdbPgService.query(
              /* SQL */ 'CREATE TABLE IF NOT EXISTS migrations (name text, executed_at timestamp)',
            );
            const rows = await questdbPgService.query<{ name: string }[]>(
              /* SQL */ 'SELECT name FROM migrations',
            );
            return rows.map((r) => r.name);
          } catch (error) {
            this.logger.error('Failed to get executed migrations', error);
            return [];
          }
        },
      },
      logger: console,
    });
  }

  async onModuleInit() {
    // Use config to determine if migrations should run (from questdbPg config)
    const runMigrations = this.configService.get<boolean>(
      'questdbPg.runMigrations',
    );
    if (!runMigrations) {
      this.logger.log(
        'questdbPg.runMigrations is not enabled. Skipping migrations.',
      );
      return;
    }
    // Run migrations automatically on startup
    try {
      this.logger.log('Running database migrations...');
      const migrations = await this.umzug.up();
      if (migrations.length > 0) {
        this.logger.log(`Applied ${migrations.length} migrations`);
      } else {
        this.logger.log('No pending migrations');
      }
    } catch (error) {
      this.logger.error('Failed to run migrations', error);
      throw error;
    }
  }

  // Method to run migrations programmatically
  async runMigrations() {
    try {
      this.logger.log('Running migrations programmatically...');
      const migrations = await this.umzug.up();
      if (migrations.length > 0) {
        this.logger.log(`Successfully applied ${migrations.length} migrations`);
      } else {
        this.logger.log('No pending migrations to run');
      }
      return migrations;
    } catch (error) {
      this.logger.error('Failed to run migrations programmatically', error);
      throw error;
    }
  }

  // Method to revert the last migration
  async revertLastMigration() {
    try {
      this.logger.log('Reverting last migration...');
      const migrations = await this.umzug.down();
      if (migrations.length > 0) {
        this.logger.log(
          `Successfully reverted migration: ${migrations[0]?.name}`,
        );
      } else {
        this.logger.log('No migrations to revert');
      }
      return migrations;
    } catch (error) {
      this.logger.error('Failed to revert migration', error);
      throw error;
    }
  }

  // Method to create a new migration file
  createMigration(name: string): string {
    const timestamp = new Date()
      .toISOString()
      .replace(/[-:.TZ]/g, '')
      .slice(0, 14);
    const filename = `${timestamp}-${name}.ts`;
    const migrationPath = path.join(
      process.cwd(),
      'src/database/migrations',
      filename,
    );

    const template = `
import { QuestdbPgService } from '../../questdb/questdb-pg.service';

export async function up(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(\`
    -- Your SQL code here
  \`);
}

export async function down(questdbPgService: QuestdbPgService): Promise<void> {
  await questdbPgService.query(\`
    -- Your rollback SQL code here
  \`);
}
`;

    fs.writeFileSync(migrationPath, template.trim());
    return migrationPath;
  }
}
