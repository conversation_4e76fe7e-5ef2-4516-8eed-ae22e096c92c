import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from './config/app.config';
import { loggerConfig } from './config/logger.config';
import { KafkaOptions, Transport } from '@nestjs/microservices';
import { HttpExceptionFilter } from './common/filter/http-exception.filter';
import { WsExceptionFilter } from './common/filter/ws-exception.filter';
import { GrpcConfig } from './config/grpc.config';
import { GrpcExceptionFilter } from './common/filter/grpc-exception.filter';
import { KafkaConfig } from './config/kafka.config';
import { ErrorService } from './common/errors/error.service';

async function bootstrap() {
  const logger = new Logger(bootstrap.name);
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const appConfig = configService.get<AppConfig>('app');
  const grpcConfig = configService.getOrThrow<GrpcConfig>('grpc');
  const kafkaConfig = configService.getOrThrow<KafkaConfig>('kafka', {
    infer: true,
  }) as KafkaConfig;

  app.connectMicroservice<KafkaOptions>({
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: kafkaConfig.clientId,
        brokers: kafkaConfig.brokers,
        sasl: kafkaConfig.sasl,
      },
      consumer: kafkaConfig.consumer,
    },
  });
  await app.startAllMicroservices();
  const errorService = app.get(ErrorService);

  app.useGlobalFilters(
    ...[
      new HttpExceptionFilter(errorService),
      new WsExceptionFilter(errorService),
      new GrpcExceptionFilter(errorService),
    ],
  );
  app.useLogger(loggerConfig(appConfig.logLevel));

  app.useGlobalPipes(new ValidationPipe({ transform: true }));

  await app.listen(appConfig.port);
  logger.log(
    `GRPC started at [${appConfig.nodeEnv}], listening on url [${grpcConfig.url}]`,
  );
  logger.log(
    `App started at [${appConfig.nodeEnv}], listening on port [${appConfig.port}]`,
  );
}

bootstrap().catch((err) => {
  console.error(err);

  const defaultExitCode = 1;
  process.exit(defaultExitCode);
});
