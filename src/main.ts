import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from './config/app.config';
import { loggerConfig } from './config/logger.config';
import {
  KafkaOptions,
  Transport,
  MicroserviceOptions,
} from '@nestjs/microservices';
import { HttpExceptionFilter } from './common/filter/http-exception.filter';
import { WsExceptionFilter } from './common/filter/ws-exception.filter';
import { resolve } from 'path';
import { GrpcConfig } from './config/grpc.config';
import { GrpcExceptionFilter } from './common/filter/grpc-exception.filter';
import { KafkaConfig } from './config/kafka.config';

async function bootstrap() {
  const logger = new Logger(bootstrap.name);
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const appConfig = configService.get<AppConfig>('app');
  const grpcConfig = configService.get<GrpcConfig>('grpc');
  const kafkaConfig = configService.getOrThrow<KafkaConfig>('kafka', {
    infer: true,
  }) as KafkaConfig;

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      url: grpcConfig.url,
      package: ['pair'],
      protoPath: resolve(__dirname, '../src/grpc/proto/pair.proto'),
    },
  });
  app.connectMicroservice<KafkaOptions>({
    transport: Transport.KAFKA,
    options: {
      client: {
        clientId: kafkaConfig.clientId,
        brokers: kafkaConfig.brokers,
        sasl: kafkaConfig.sasl,
      },
      consumer: kafkaConfig.consumer,
    },
  });
  await app.startAllMicroservices();
  app.useGlobalFilters(
    ...[
      new HttpExceptionFilter(),
      new WsExceptionFilter(),
      new GrpcExceptionFilter(),
    ],
  );
  app.useLogger(loggerConfig(appConfig.logLevel));
  await app.listen(appConfig.port);
  logger.log(
    `App started at [${appConfig.nodeEnv}], listening on port [${appConfig.port}]`,
  );
}

bootstrap().catch((err) => {
  console.error(err);

  const defaultExitCode = 1;
  process.exit(defaultExitCode);
});
