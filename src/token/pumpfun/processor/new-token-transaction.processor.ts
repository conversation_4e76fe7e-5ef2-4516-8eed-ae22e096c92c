import { On<PERSON>or<PERSON>E<PERSON>, Processor } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../../common/constant/queue-name';
import { Inject, Injectable } from '@nestjs/common';
import { WorkerHostProcessor } from '../../../queue/worker-host.processor';
import { Job } from 'bullmq';
import { TokenService } from '../token.service';
import { TokenDto } from '../dto/token.dto';
import { Token } from '../entities/token.schema';
import { QueueNewTokenException } from 'src/common/exceptions/queue-new-token-exception';
import { TokenError } from 'src/common/constant/errors';

@Processor(PumpFunQueue.NEW_TOKEN, {
  concurrency: 100_000,
})
@Injectable()
export class NewTokenTransactionProcessor extends WorkerHostProcessor {
  private begin: number;
  constructor(
    @Inject()
    private readonly tokenService: TokenService,
  ) {
    super();
  }

  async handleProcess(job: Job<TokenDto, Token, string>): Promise<void> {
    try {
      const begin = performance.now();
      const tokenDataDto = job.data;

      const savedToken =
        await this.tokenService.createPumpFunToken(tokenDataDto);
      this.logger.log(
        `[SUCCESS] Processed token ${tokenDataDto.tokenAddress}, elapsed: ${(performance.now() - begin).toFixed()}ms `,
      );
      return savedToken;
    } catch (error) {
      this.logger.error(
        `${PumpFunQueue.NEW_TOKEN} handleProcess error: ${error.message}`,
        error.stack,
      );
      throw new QueueNewTokenException(TokenError.ERROR_QUEUE_NEW_TOKEN);
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<TokenDto, Token, string>) {
    const { id, queueName, data } = job;
    this.logger.verbose(
      `Job id: ${id}, Completed queue ${queueName}, token [${data.tokenAddress}],elapsed time [${(performance.now() - this.begin).toFixed(0)}]ms.`,
    );
  }
}
