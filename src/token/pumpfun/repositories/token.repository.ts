import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Token, TokenDocument } from '../entities/token.schema';

@Injectable()
export class TokenRepository {
  private readonly logger = new Logger(TokenRepository.name);

  constructor(
    @InjectModel(Token.name)
    private readonly tokenModel: Model<TokenDocument>,
  ) {}

  async create(data: Token): Promise<Token> {
    const createdToken = new this.tokenModel(data);
    const tokenObject = await createdToken.save();
    return tokenObject;
  }

  async findByTokenAddress(tokenAddress: string): Promise<Token | null> {
    return this.tokenModel.findOne({ tokenAddress }).exec();
  }

  async findByTokenAddresses(tokenAddresses: string[]): Promise<Token[]> {
    return this.tokenModel
      .find({ tokenAddress: { $in: tokenAddresses } })
      .lean()
      .exec();
  }
}
