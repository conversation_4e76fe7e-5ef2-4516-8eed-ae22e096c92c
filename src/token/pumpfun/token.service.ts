import { Inject, Injectable, Logger } from '@nestjs/common';
import bs58 from 'bs58';
import { PUMP_FUN_TOKEN_DATA_LAYOUT } from './layout/create.pump.fun.token.data';
import { InjectQueue } from '@nestjs/bullmq';
import { PumpFunQueue } from '../../common/constant/queue-name';
import { Queue } from 'bullmq';
import { PUMP_FUN_NEW_TOKEN_TRANSACTION_JOB } from '../../common/constant/job-name';
import { TokenDto } from './dto/token.dto';
import { TokenRepository } from './repositories/token.repository';
import { Token } from './entities/token.schema';

@Injectable()
export class TokenService {
  private readonly logger: Logger = new Logger(TokenService.name);

  constructor(
    @Inject()
    private readonly tokenRepository: TokenRepository,
    @InjectQueue(PumpFunQueue.NEW_TOKEN)
    private readonly newTokenQueue: Queue,
  ) {}

  async handlePumpFunToken(
    tokenAddress: string,
    decimals: number,
    createdAt: Date,
    createPairInstruction?: any,
  ) {
    try {
      const bufferData = bs58.decode(createPairInstruction);
      // Remove first 8 bytes for event cpi
      const tokenInfo = PUMP_FUN_TOKEN_DATA_LAYOUT.decode(
        Buffer.from(bufferData),
      );

      await this.newTokenQueue.add(PUMP_FUN_NEW_TOKEN_TRANSACTION_JOB, {
        name: tokenInfo.name,
        symbol: tokenInfo.symbol,
        decimals: decimals,
        descriptionUri: tokenInfo.uri,
        createdAt: createdAt,
        tokenAddress: tokenAddress,
      } as TokenDto);
    } catch (err) {
      this.logger.error(
        `Failed to save token for address: ${tokenAddress} with reason: ${err.message}`,
      );
      this.logger.debug(err.stack);
    }
  }

  async createPumpFunToken(tokenDto: TokenDto): Promise<void> {
    let tokenDescription = '';
    let tokenImage = '';
    let twitter = '';
    let website = '';
    let telegram = '';

    try {
      const response = await fetch(tokenDto.descriptionUri);

      if (response.ok) {
        const responseObj = await response.json();

        tokenDescription = responseObj.description ?? '';
        tokenImage = responseObj.image ?? '';
        twitter = responseObj.twitter ?? '';
        website = responseObj.website ?? '';
        telegram = responseObj.telegram ?? '';
      } else {
        this.logger.warn(
          `[WARN] Description URI returned non-OK response: ${response.status} — ${tokenDto.descriptionUri}`,
        );
      }
    } catch (error) {
      this.logger.warn(
        `[WARN] Failed to fetch token description from ${tokenDto.descriptionUri}: ${error.message}`,
        error.stack,
      );
    }

    const tokenData: Token = {
      tokenAddress: tokenDto.tokenAddress,
      tokenName: tokenDto.name,
      symbol: tokenDto.symbol,
      description: tokenDescription,
      image: tokenImage,
      decimals: tokenDto.decimals,
      twitter,
      website,
      telegram,
      createdAt: tokenDto.createdAt,
    };

    try {
      await this.tokenRepository.create(tokenData);
      this.logger.log(`[SUCCESS] Stored token: ${tokenDto.tokenAddress}`);
    } catch (err) {
      this.logger.error(
        `[FAILURE] Failed to store token ${tokenDto.tokenAddress}: ${err.message}`,
        err.stack,
      );
      throw err;
    }
  }

  async getTokensByAddresses(tokenAddresses: string[]): Promise<Token[]> {
    return await this.tokenRepository.findByTokenAddresses(tokenAddresses);
  }
}
