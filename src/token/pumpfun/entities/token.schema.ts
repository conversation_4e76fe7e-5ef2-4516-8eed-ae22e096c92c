import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { now } from 'mongoose';

export type TokenDocument = Token & Document;

@Schema({
  collection: 'token',
})
export class Token {
  @Prop({ required: true })
  tokenAddress: string;

  @Prop()
  tokenName: string;

  @Prop()
  symbol: string;

  @Prop()
  description: string;

  @Prop()
  image: string;

  @Prop()
  decimals: number;

  @Prop()
  twitter?: string;

  @Prop()
  website?: string;

  @Prop()
  telegram?: string;

  @Prop({ default: now })
  createdAt?: Date;

  @Prop({ default: now })
  updatedAt?: Date;
}

export const TokenSchema = SchemaFactory.createForClass(Token);
