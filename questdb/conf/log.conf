# list of configured writers
writers=file,stdout,http.min
#,alert

# file writer
#w.file.class=io.questdb.log.LogFileWriter
#w.file.location=questdb-debug.log
#w.file.level=INFO,ERROR

# rolling file writer
#w.file.class=io.questdb.log.LogRollingFileWriter
#w.file.location=${log.dir}/questdb-rolling.log.${date:yyyyMMdd}
#w.file.level=INFO,ERROR
#rollEvery accepts: day, hour, minute, month
#w.file.rollEvery=day
#rollSize specifies size at which to roll a new log file: a number followed by k, m, g (KB, MB, GB respectively)
#w.file.rollSize=128m
#lifeDuration accepts: a number followed by s, m, h, d, w, M, y for seconds, minutes, hours, etc.
#w.file.lifeDuration=1d
#sizeLimit is the max fileSize of the log directory. Follows same format as rollSize
#w.file.sizeLimit=1g

# stdout
w.stdout.class=io.questdb.log.LogConsoleWriter
w.stdout.level=INFO

# alert
# w.alert.class=io.questdb.log.LogAlertSocketWriter
# w.alert.level=CRITICAL
# w.alert.location=/alert-manager-tpt.json
# w.alert.alertTargets=localhost:9093,**************:2468, ... ,localhost:9999
# w.alert.inBufferSize=2M
# w.alert.outBufferSize=4M
# w.alert.reconnectDelay=250 # 1/4th sec (milli precision)
# w.alert.defaultAlertHost=127.0.0.1
# w.alert.defaultAlertPort=9093


# disable logging out of min http server, which is supposed to be used
# for frequent monitoring
w.http.min.class=io.questdb.log.LogConsoleWriter
w.http.min.level=ERROR
w.http.min.scope=http-min-server
