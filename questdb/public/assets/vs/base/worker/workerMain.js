/*! For license information please see workerMain.js.LICENSE.txt */
(function(){var e=["require","exports","vs/editor/common/core/range","vs/editor/common/core/offsetRange","vs/editor/common/core/position","vs/base/common/errors","vs/base/common/strings","vs/base/common/arrays","vs/editor/common/diff/defaultLinesDiffComputer/algorithms/diffAlgorithm","vs/base/common/event","vs/editor/common/core/lineRange","vs/base/common/arraysFind","vs/base/common/assert","vs/base/common/lifecycle","vs/base/common/objects","vs/editor/common/diff/defaultLinesDiffComputer/utils","vs/editor/common/diff/rangeMapping","vs/base/common/platform","vs/base/common/uri","vs/nls","vs/base/common/functional","vs/base/common/iterator","vs/base/common/linkedList","vs/base/common/stopwatch","vs/base/common/diff/diff","vs/base/common/types","vs/base/common/uint","vs/editor/common/core/characterClassifier","vs/editor/common/core/wordHelper","vs/editor/common/diff/defaultLinesDiffComputer/algorithms/myersDiffAlgorithm","vs/editor/common/diff/defaultLinesDiffComputer/linesSliceCharSequence","vs/editor/common/diff/linesDiffComputer","vs/base/common/cache","vs/base/common/color","vs/base/common/diff/diffChange","vs/base/common/keyCodes","vs/base/common/lazy","vs/base/common/map","vs/base/common/cancellation","vs/base/common/hash","vs/base/common/codicons","vs/editor/common/core/selection","vs/editor/common/core/wordCharacterClassifier","vs/editor/common/diff/defaultLinesDiffComputer/heuristicSequenceOptimizations","vs/editor/common/diff/defaultLinesDiffComputer/lineSequence","vs/editor/common/diff/defaultLinesDiffComputer/algorithms/dynamicProgrammingDiffing","vs/editor/common/diff/defaultLinesDiffComputer/computeMovedLines","vs/editor/common/diff/defaultLinesDiffComputer/defaultLinesDiffComputer","vs/editor/common/diff/legacyLinesDiffComputer","vs/editor/common/diff/linesDiffComputers","vs/editor/common/languages/defaultDocumentColorsComputer","vs/editor/common/languages/linkComputer","vs/editor/common/languages/supports/inplaceReplaceSupport","vs/editor/common/model","vs/editor/common/model/prefixSumComputer","vs/editor/common/model/mirrorTextModel","vs/editor/common/model/textModelSearch","vs/editor/common/services/unicodeTextModelHighlighter","vs/editor/common/standalone/standaloneEnums","vs/editor/common/tokenizationRegistry","vs/nls!vs/base/common/platform","vs/nls!vs/base/common/worker/simpleWorker","vs/base/common/process","vs/base/common/path","vs/nls!vs/editor/common/languages","vs/editor/common/languages","vs/editor/common/services/editorBaseApi","vs/base/common/worker/simpleWorker","vs/editor/common/services/editorSimpleWorker"],t=function(t){for(var n=[],i=0,r=t.length;i<r;i++)n[i]=e[t[i]];return n};const n=this,i="object"==typeof global?global:{};var r,s;!function(e){e.global=n;class t{get isWindows(){return this._detect(),this._isWindows}get isNode(){return this._detect(),this._isNode}get isElectronRenderer(){return this._detect(),this._isElectronRenderer}get isWebWorker(){return this._detect(),this._isWebWorker}get isElectronNodeIntegrationWebWorker(){return this._detect(),this._isElectronNodeIntegrationWebWorker}constructor(){this._detected=!1,this._isWindows=!1,this._isNode=!1,this._isElectronRenderer=!1,this._isWebWorker=!1,this._isElectronNodeIntegrationWebWorker=!1}_detect(){this._detected||(this._detected=!0,this._isWindows=t._isWindows(),this._isNode=typeof module<"u"&&!!module.exports,this._isElectronRenderer=typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.electron<"u"&&"renderer"===process.type,this._isWebWorker="function"==typeof e.global.importScripts,this._isElectronNodeIntegrationWebWorker=this._isWebWorker&&typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.electron<"u"&&"worker"===process.type)}static _isWindows(){return!!(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.indexOf("Windows")>=0)||typeof process<"u"&&"win32"===process.platform}}e.Environment=t}(s||(s={})),function(e){class t{constructor(e,t,n){this.type=e,this.detail=t,this.timestamp=n}}e.LoaderEvent=t,e.LoaderEventRecorder=class{constructor(e){this._events=[new t(1,"",e)]}record(n,i){this._events.push(new t(n,i,e.Utilities.getHighPerformanceTimestamp()))}getEvents(){return this._events}};class n{record(e,t){}getEvents(){return[]}}n.INSTANCE=new n,e.NullLoaderEventRecorder=n}(s||(s={})),function(e){class t{static fileUriToFilePath(e,t){if(t=decodeURI(t).replace(/%23/g,"#"),e){if(/^file:\/\/\//.test(t))return t.substr(8);if(/^file:\/\//.test(t))return t.substr(5)}else if(/^file:\/\//.test(t))return t.substr(7);return t}static startsWith(e,t){return e.length>=t.length&&e.substr(0,t.length)===t}static endsWith(e,t){return e.length>=t.length&&e.substr(e.length-t.length)===t}static containsQueryString(e){return/^[^\#]*\?/gi.test(e)}static isAbsolutePath(e){return/^((http:\/\/)|(https:\/\/)|(file:\/\/)|(\/))/.test(e)}static forEachProperty(e,t){if(e){let n;for(n in e)e.hasOwnProperty(n)&&t(n,e[n])}}static isEmpty(e){let n=!0;return t.forEachProperty(e,(()=>{n=!1})),n}static recursiveClone(e){if(!e||"object"!=typeof e||e instanceof RegExp||!Array.isArray(e)&&Object.getPrototypeOf(e)!==Object.prototype)return e;let n=Array.isArray(e)?[]:{};return t.forEachProperty(e,((e,i)=>{n[e]=i&&"object"==typeof i?t.recursiveClone(i):i})),n}static generateAnonymousModule(){return"===anonymous"+t.NEXT_ANONYMOUS_ID+++"==="}static isAnonymousModule(e){return t.startsWith(e,"===anonymous")}static getHighPerformanceTimestamp(){return this.PERFORMANCE_NOW_PROBED||(this.PERFORMANCE_NOW_PROBED=!0,this.HAS_PERFORMANCE_NOW=e.global.performance&&"function"==typeof e.global.performance.now),this.HAS_PERFORMANCE_NOW?e.global.performance.now():Date.now()}}t.NEXT_ANONYMOUS_ID=1,t.PERFORMANCE_NOW_PROBED=!1,t.HAS_PERFORMANCE_NOW=!1,e.Utilities=t}(s||(s={})),function(e){function t(e){if(e instanceof Error)return e;const t=new Error(e.message||String(e)||"Unknown Error");return e.stack&&(t.stack=e.stack),t}e.ensureError=t;class n{static validateConfigurationOptions(n){if("string"!=typeof(n=n||{}).baseUrl&&(n.baseUrl=""),"boolean"!=typeof n.isBuild&&(n.isBuild=!1),"object"!=typeof n.paths&&(n.paths={}),"object"!=typeof n.config&&(n.config={}),typeof n.catchError>"u"&&(n.catchError=!1),typeof n.recordStats>"u"&&(n.recordStats=!1),"string"!=typeof n.urlArgs&&(n.urlArgs=""),"function"!=typeof n.onError&&(n.onError=function(e){return"loading"===e.phase?(console.error('Loading "'+e.moduleId+'" failed'),console.error(e),console.error("Here are the modules that depend on it:"),void console.error(e.neededBy)):"factory"===e.phase?(console.error('The factory function of "'+e.moduleId+'" has thrown an exception'),console.error(e),console.error("Here are the modules that depend on it:"),void console.error(e.neededBy)):void 0}),Array.isArray(n.ignoreDuplicateModules)||(n.ignoreDuplicateModules=[]),n.baseUrl.length>0&&(e.Utilities.endsWith(n.baseUrl,"/")||(n.baseUrl+="/")),"string"!=typeof n.cspNonce&&(n.cspNonce=""),typeof n.preferScriptTags>"u"&&(n.preferScriptTags=!1),n.nodeCachedData&&"object"==typeof n.nodeCachedData&&("string"!=typeof n.nodeCachedData.seed&&(n.nodeCachedData.seed="seed"),("number"!=typeof n.nodeCachedData.writeDelay||n.nodeCachedData.writeDelay<0)&&(n.nodeCachedData.writeDelay=7e3),!n.nodeCachedData.path||"string"!=typeof n.nodeCachedData.path)){const e=t(new Error("INVALID cached data configuration, 'path' MUST be set"));e.phase="configuration",n.onError(e),n.nodeCachedData=void 0}return n}static mergeConfigurationOptions(t=null,i=null){let r=e.Utilities.recursiveClone(i||{});return e.Utilities.forEachProperty(t,((t,n)=>{"ignoreDuplicateModules"===t&&typeof r.ignoreDuplicateModules<"u"?r.ignoreDuplicateModules=r.ignoreDuplicateModules.concat(n):"paths"===t&&typeof r.paths<"u"?e.Utilities.forEachProperty(n,((e,t)=>r.paths[e]=t)):"config"===t&&typeof r.config<"u"?e.Utilities.forEachProperty(n,((e,t)=>r.config[e]=t)):r[t]=e.Utilities.recursiveClone(n)})),n.validateConfigurationOptions(r)}}e.ConfigurationOptionsUtil=n;class i{constructor(e,t){if(this._env=e,this.options=n.mergeConfigurationOptions(t),this._createIgnoreDuplicateModulesMap(),this._createSortedPathsRules(),""===this.options.baseUrl&&this.options.nodeRequire&&this.options.nodeRequire.main&&this.options.nodeRequire.main.filename&&this._env.isNode){let e=this.options.nodeRequire.main.filename,t=Math.max(e.lastIndexOf("/"),e.lastIndexOf("\\"));this.options.baseUrl=e.substring(0,t+1)}}_createIgnoreDuplicateModulesMap(){this.ignoreDuplicateModulesMap={};for(let e=0;e<this.options.ignoreDuplicateModules.length;e++)this.ignoreDuplicateModulesMap[this.options.ignoreDuplicateModules[e]]=!0}_createSortedPathsRules(){this.sortedPathsRules=[],e.Utilities.forEachProperty(this.options.paths,((e,t)=>{Array.isArray(t)?this.sortedPathsRules.push({from:e,to:t}):this.sortedPathsRules.push({from:e,to:[t]})})),this.sortedPathsRules.sort(((e,t)=>t.from.length-e.from.length))}cloneAndMerge(e){return new i(this._env,n.mergeConfigurationOptions(e,this.options))}getOptionsLiteral(){return this.options}_applyPaths(t){let n;for(let i=0,r=this.sortedPathsRules.length;i<r;i++)if(n=this.sortedPathsRules[i],e.Utilities.startsWith(t,n.from)){let e=[];for(let i=0,r=n.to.length;i<r;i++)e.push(n.to[i]+t.substr(n.from.length));return e}return[t]}_addUrlArgsToUrl(t){return e.Utilities.containsQueryString(t)?t+"&"+this.options.urlArgs:t+"?"+this.options.urlArgs}_addUrlArgsIfNecessaryToUrl(e){return this.options.urlArgs?this._addUrlArgsToUrl(e):e}_addUrlArgsIfNecessaryToUrls(e){if(this.options.urlArgs)for(let t=0,n=e.length;t<n;t++)e[t]=this._addUrlArgsToUrl(e[t]);return e}moduleIdToPaths(t){if(this._env.isNode&&this.options.amdModulesPattern instanceof RegExp&&!this.options.amdModulesPattern.test(t))return this.isBuild()?["empty:"]:["node|"+t];let n,i=t;if(e.Utilities.endsWith(i,".js")||e.Utilities.isAbsolutePath(i))!e.Utilities.endsWith(i,".js")&&!e.Utilities.containsQueryString(i)&&(i+=".js"),n=[i];else{n=this._applyPaths(i);for(let t=0,i=n.length;t<i;t++)this.isBuild()&&"empty:"===n[t]||(e.Utilities.isAbsolutePath(n[t])||(n[t]=this.options.baseUrl+n[t]),!e.Utilities.endsWith(n[t],".js")&&!e.Utilities.containsQueryString(n[t])&&(n[t]=n[t]+".js"))}return this._addUrlArgsIfNecessaryToUrls(n)}requireToUrl(t){let n=t;return e.Utilities.isAbsolutePath(n)||(n=this._applyPaths(n)[0],e.Utilities.isAbsolutePath(n)||(n=this.options.baseUrl+n)),this._addUrlArgsIfNecessaryToUrl(n)}isBuild(){return this.options.isBuild}shouldInvokeFactory(t){return!!(!this.options.isBuild||e.Utilities.isAnonymousModule(t)||this.options.buildForceInvokeFactory&&this.options.buildForceInvokeFactory[t])}isDuplicateMessageIgnoredFor(e){return this.ignoreDuplicateModulesMap.hasOwnProperty(e)}getConfigForModule(e){if(this.options.config)return this.options.config[e]}shouldCatchError(){return this.options.catchError}shouldRecordStats(){return this.options.recordStats}onError(e){this.options.onError(e)}}e.Configuration=i}(s||(s={})),function(e){class t{constructor(e){this._env=e,this._scriptLoader=null,this._callbackMap={}}load(e,t,i,o){if(!this._scriptLoader)if(this._env.isWebWorker)this._scriptLoader=new r;else if(this._env.isElectronRenderer){const{preferScriptTags:t}=e.getConfig().getOptionsLiteral();this._scriptLoader=t?new n:new s(this._env)}else this._env.isNode?this._scriptLoader=new s(this._env):this._scriptLoader=new n;let a={callback:i,errorback:o};this._callbackMap.hasOwnProperty(t)?this._callbackMap[t].push(a):(this._callbackMap[t]=[a],this._scriptLoader.load(e,t,(()=>this.triggerCallback(t)),(e=>this.triggerErrorback(t,e))))}triggerCallback(e){let t=this._callbackMap[e];delete this._callbackMap[e];for(let e=0;e<t.length;e++)t[e].callback()}triggerErrorback(e,t){let n=this._callbackMap[e];delete this._callbackMap[e];for(let e=0;e<n.length;e++)n[e].errorback(t)}}class n{attachListeners(e,t,n){let i=()=>{e.removeEventListener("load",r),e.removeEventListener("error",s)},r=e=>{i(),t()},s=e=>{i(),n(e)};e.addEventListener("load",r),e.addEventListener("error",s)}load(t,n,i,r){if(/^node\|/.test(n)){let s=t.getConfig().getOptionsLiteral(),a=o(t.getRecorder(),s.nodeRequire||e.global.nodeRequire),l=n.split("|"),u=null;try{u=a(l[1])}catch(e){return void r(e)}t.enqueueDefineAnonymousModule([],(()=>u)),i()}else{let e=document.createElement("script");e.setAttribute("async","async"),e.setAttribute("type","text/javascript"),this.attachListeners(e,i,r);const{trustedTypesPolicy:s}=t.getConfig().getOptionsLiteral();s&&(n=s.createScriptURL(n)),e.setAttribute("src",n);const{cspNonce:o}=t.getConfig().getOptionsLiteral();o&&e.setAttribute("nonce",o),document.getElementsByTagName("head")[0].appendChild(e)}}}class r{constructor(){this._cachedCanUseEval=null}_canUseEval(e){return null===this._cachedCanUseEval&&(this._cachedCanUseEval=function(e){const{trustedTypesPolicy:t}=e.getConfig().getOptionsLiteral();try{return(t?self.eval(t.createScript("","true")):new Function("true")).call(self),!0}catch{return!1}}(e)),this._cachedCanUseEval}load(t,n,i,r){if(/^node\|/.test(n)){const s=t.getConfig().getOptionsLiteral(),a=o(t.getRecorder(),s.nodeRequire||e.global.nodeRequire),l=n.split("|");let u=null;try{u=a(l[1])}catch(e){return void r(e)}t.enqueueDefineAnonymousModule([],(function(){return u})),i()}else{const{trustedTypesPolicy:e}=t.getConfig().getOptionsLiteral();if((!/^((http:)|(https:)|(file:))/.test(n)||n.substring(0,self.origin.length)===self.origin)&&this._canUseEval(t))return void fetch(n).then((e=>{if(200!==e.status)throw new Error(e.statusText);return e.text()})).then((t=>{t=`${t}\n//# sourceURL=${n}`,(e?self.eval(e.createScript("",t)):new Function(t)).call(self),i()})).then(void 0,r);try{e&&(n=e.createScriptURL(n)),importScripts(n),i()}catch(e){r(e)}}}}class s{constructor(e){this._env=e,this._didInitialize=!1,this._didPatchNodeRequire=!1}_init(e){this._didInitialize||(this._didInitialize=!0,this._fs=e("fs"),this._vm=e("vm"),this._path=e("path"),this._crypto=e("crypto"))}_initNodeRequire(e,t){const{nodeCachedData:n}=t.getConfig().getOptionsLiteral();if(!n||this._didPatchNodeRequire)return;this._didPatchNodeRequire=!0;const r=this,s=e("module");s.prototype._compile=function(e,o){const a=s.wrap(e.replace(/^#!.*/,"")),l=t.getRecorder(),u=r._getCachedDataPath(n,o),c={filename:o};let h;try{const e=r._fs.readFileSync(u);h=e.slice(0,16),c.cachedData=e.slice(16),l.record(60,u)}catch{l.record(61,u)}const d=new r._vm.Script(a,c),f=d.runInThisContext(c),g=r._path.dirname(o),m=function(e){const t=e.constructor;let n=function(t){try{return e.require(t)}finally{}};return(n.resolve=function(n,i){return t._resolveFilename(n,e,!1,i)}).paths=function(n){return t._resolveLookupPaths(n,e)},n.main=process.mainModule,n.extensions=t._extensions,n.cache=t._cache,n}(this),p=[this.exports,m,this,o,g,process,i,Buffer],_=f.apply(this.exports,p);return r._handleCachedData(d,a,u,!c.cachedData,t),r._verifyCachedData(d,a,u,h,t),_}}load(t,n,i,r){const a=t.getConfig().getOptionsLiteral(),l=o(t.getRecorder(),a.nodeRequire||e.global.nodeRequire),u=a.nodeInstrumenter||function(e){return e};this._init(l),this._initNodeRequire(l,t);let c=t.getRecorder();if(/^node\|/.test(n)){let e=n.split("|"),s=null;try{s=l(e[1])}catch(e){return void r(e)}t.enqueueDefineAnonymousModule([],(()=>s)),i()}else{n=e.Utilities.fileUriToFilePath(this._env.isWindows,n);const o=this._path.normalize(n),l=this._getElectronRendererScriptPathOrUri(o),h=!!a.nodeCachedData,d=h?this._getCachedDataPath(a.nodeCachedData,n):void 0;this._readSourceAndCachedData(o,d,c,((e,n,a,c)=>{if(e)return void r(e);let f;f=n.charCodeAt(0)===s._BOM?s._PREFIX+n.substring(1)+s._SUFFIX:s._PREFIX+n+s._SUFFIX,f=u(f,o);const g={filename:l,cachedData:a},m=this._createAndEvalScript(t,f,g,i,r);this._handleCachedData(m,f,d,h&&!a,t),this._verifyCachedData(m,f,d,c,t)}))}}_createAndEvalScript(t,n,i,r,s){const o=t.getRecorder();o.record(31,i.filename);const a=new this._vm.Script(n,i),l=a.runInThisContext(i),u=t.getGlobalAMDDefineFunc();let c=!1;const h=function(){return c=!0,u.apply(null,arguments)};return h.amd=u.amd,l.call(e.global,t.getGlobalAMDRequireFunc(),h,i.filename,this._path.dirname(i.filename)),o.record(32,i.filename),c?r():s(new Error(`Didn't receive define call in ${i.filename}!`)),a}_getElectronRendererScriptPathOrUri(e){if(!this._env.isElectronRenderer)return e;let t=e.match(/^([a-z])\:(.*)/i);return t?`file:///${(t[1].toUpperCase()+":"+t[2]).replace(/\\/g,"/")}`:`file://${e}`}_getCachedDataPath(e,t){const n=this._crypto.createHash("md5").update(t,"utf8").update(e.seed,"utf8").update(process.arch,"").digest("hex"),i=this._path.basename(t).replace(/\.js$/,"");return this._path.join(e.path,`${i}-${n}.code`)}_handleCachedData(e,t,n,i,r){e.cachedDataRejected?this._fs.unlink(n,(i=>{r.getRecorder().record(62,n),this._createAndWriteCachedData(e,t,n,r),i&&r.getConfig().onError(i)})):i&&this._createAndWriteCachedData(e,t,n,r)}_createAndWriteCachedData(e,t,n,i){let r,s=Math.ceil(i.getConfig().getOptionsLiteral().nodeCachedData.writeDelay*(1+Math.random())),o=-1,a=0;const l=()=>{setTimeout((()=>{r||(r=this._crypto.createHash("md5").update(t,"utf8").digest());const s=e.createCachedData();if(!(0===s.length||s.length===o||a>=5)){if(s.length<o)return void l();o=s.length,this._fs.writeFile(n,Buffer.concat([r,s]),(e=>{e&&i.getConfig().onError(e),i.getRecorder().record(63,n),l()}))}}),s*Math.pow(4,a++))};l()}_readSourceAndCachedData(e,t,n,i){if(t){let r,s,o,a=2;const l=e=>{e?i(e):0==--a&&i(void 0,r,s,o)};this._fs.readFile(e,{encoding:"utf8"},((e,t)=>{r=t,l(e)})),this._fs.readFile(t,((e,i)=>{!e&&i&&i.length>0?(o=i.slice(0,16),s=i.slice(16),n.record(60,t)):n.record(61,t),l()}))}else this._fs.readFile(e,{encoding:"utf8"},i)}_verifyCachedData(e,t,n,i,r){i&&(e.cachedDataRejected||setTimeout((()=>{const e=this._crypto.createHash("md5").update(t,"utf8").digest();i.equals(e)||(r.getConfig().onError(new Error(`FAILED TO VERIFY CACHED DATA, deleting stale '${n}' now, but a RESTART IS REQUIRED`)),this._fs.unlink(n,(e=>{e&&r.getConfig().onError(e)})))}),Math.ceil(5e3*(1+Math.random()))))}}function o(e,t){if(t.__$__isRecorded)return t;const n=function(n){e.record(33,n);try{return t(n)}finally{e.record(34,n)}};return n.__$__isRecorded=!0,n}s._BOM=65279,s._PREFIX="(function (require, define, __filename, __dirname) { ",s._SUFFIX="\n});",e.ensureRecordedNodeRequire=o,e.createScriptLoader=function(e){return new t(e)}}(s||(s={})),function(e){class t{constructor(e){let t=e.lastIndexOf("/");this.fromModulePath=-1!==t?e.substr(0,t+1):""}static _normalizeModuleId(e){let t,n=e;for(t=/\/\.\//;t.test(n);)n=n.replace(t,"/");for(n=n.replace(/^\.\//g,""),t=/\/(([^\/])|([^\/][^\/\.])|([^\/\.][^\/])|([^\/][^\/][^\/]+))\/\.\.\//;t.test(n);)n=n.replace(t,"/");return n=n.replace(/^(([^\/])|([^\/][^\/\.])|([^\/\.][^\/])|([^\/][^\/][^\/]+))\/\.\.\//,""),n}resolveModule(n){let i=n;return e.Utilities.isAbsolutePath(i)||(e.Utilities.startsWith(i,"./")||e.Utilities.startsWith(i,"../"))&&(i=t._normalizeModuleId(this.fromModulePath+i)),i}}t.ROOT=new t(""),e.ModuleIdResolver=t;class n{constructor(e,t,n,i,r,s){this.id=e,this.strId=t,this.dependencies=n,this._callback=i,this._errorback=r,this.moduleIdResolver=s,this.exports={},this.error=null,this.exportsPassedIn=!1,this.unresolvedDependenciesCount=this.dependencies.length,this._isComplete=!1}static _safeInvokeFunction(t,n){try{return{returnedValue:t.apply(e.global,n),producedError:null}}catch(e){return{returnedValue:null,producedError:e}}}static _invokeFactory(t,n,i,r){return t.shouldInvokeFactory(n)?t.shouldCatchError()?this._safeInvokeFunction(i,r):{returnedValue:i.apply(e.global,r),producedError:null}:{returnedValue:null,producedError:null}}complete(t,i,r,s){this._isComplete=!0;let o=null;if(this._callback)if("function"==typeof this._callback){t.record(21,this.strId);let s=n._invokeFactory(i,this.strId,this._callback,r);o=s.producedError,t.record(22,this.strId),!o&&typeof s.returnedValue<"u"&&(!this.exportsPassedIn||e.Utilities.isEmpty(this.exports))&&(this.exports=s.returnedValue)}else this.exports=this._callback;if(o){let t=e.ensureError(o);t.phase="factory",t.moduleId=this.strId,t.neededBy=s(this.id),this.error=t,i.onError(t)}this.dependencies=null,this._callback=null,this._errorback=null,this.moduleIdResolver=null}onDependencyError(e){return this._isComplete=!0,this.error=e,!!this._errorback&&(this._errorback(e),!0)}isComplete(){return this._isComplete}}e.Module=n;class i{constructor(){this._nextId=0,this._strModuleIdToIntModuleId=new Map,this._intModuleIdToStrModuleId=[],this.getModuleId("exports"),this.getModuleId("module"),this.getModuleId("require")}getMaxModuleId(){return this._nextId}getModuleId(e){let t=this._strModuleIdToIntModuleId.get(e);return typeof t>"u"&&(t=this._nextId++,this._strModuleIdToIntModuleId.set(e,t),this._intModuleIdToStrModuleId[t]=e),t}getStrModuleId(e){return this._intModuleIdToStrModuleId[e]}}class r{constructor(e){this.id=e}}r.EXPORTS=new r(0),r.MODULE=new r(1),r.REQUIRE=new r(2),e.RegularDependency=r;class s{constructor(e,t,n){this.id=e,this.pluginId=t,this.pluginParam=n}}e.PluginDependency=s;class o{constructor(t,n,r,s,o=0){this._env=t,this._scriptLoader=n,this._loaderAvailableTimestamp=o,this._defineFunc=r,this._requireFunc=s,this._moduleIdProvider=new i,this._config=new e.Configuration(this._env),this._hasDependencyCycle=!1,this._modules2=[],this._knownModules2=[],this._inverseDependencies2=[],this._inversePluginDependencies2=new Map,this._currentAnonymousDefineCall=null,this._recorder=null,this._buildInfoPath=[],this._buildInfoDefineStack=[],this._buildInfoDependencies=[],this._requireFunc.moduleManager=this}reset(){return new o(this._env,this._scriptLoader,this._defineFunc,this._requireFunc,this._loaderAvailableTimestamp)}getGlobalAMDDefineFunc(){return this._defineFunc}getGlobalAMDRequireFunc(){return this._requireFunc}static _findRelevantLocationInStack(e,t){let n=e=>e.replace(/\\/g,"/"),i=n(e),r=t.split(/\n/);for(let e=0;e<r.length;e++){let t=r[e].match(/(.*):(\d+):(\d+)\)?$/);if(t){let e=t[1],r=t[2],s=t[3],o=Math.max(e.lastIndexOf(" ")+1,e.lastIndexOf("(")+1);if(e=e.substr(o),e=n(e),e===i){let e={line:parseInt(r,10),col:parseInt(s,10)};return 1===e.line&&(e.col-=53),e}}}throw new Error("Could not correlate define call site for needle "+e)}getBuildInfo(){if(!this._config.isBuild())return null;let e=[],t=0;for(let n=0,i=this._modules2.length;n<i;n++){let i=this._modules2[n];if(!i)continue;let r=this._buildInfoPath[i.id]||null,s=this._buildInfoDefineStack[i.id]||null,a=this._buildInfoDependencies[i.id];e[t++]={id:i.strId,path:r,defineLocation:r&&s?o._findRelevantLocationInStack(r,s):null,dependencies:a,shim:null,exports:i.exports}}return e}getRecorder(){return this._recorder||(this._config.shouldRecordStats()?this._recorder=new e.LoaderEventRecorder(this._loaderAvailableTimestamp):this._recorder=e.NullLoaderEventRecorder.INSTANCE),this._recorder}getLoaderEvents(){return this.getRecorder().getEvents()}enqueueDefineAnonymousModule(e,t){if(null!==this._currentAnonymousDefineCall)throw new Error("Can only have one anonymous define call per script file");let n=null;this._config.isBuild()&&(n=new Error("StackLocation").stack||null),this._currentAnonymousDefineCall={stack:n,dependencies:e,callback:t}}defineModule(e,i,r,s,o,a=new t(e)){let l=this._moduleIdProvider.getModuleId(e);if(this._modules2[l])return void(this._config.isDuplicateMessageIgnoredFor(e)||console.warn("Duplicate definition of module '"+e+"'"));let u=new n(l,e,this._normalizeDependencies(i,a),r,s,a);this._modules2[l]=u,this._config.isBuild()&&(this._buildInfoDefineStack[l]=o,this._buildInfoDependencies[l]=(u.dependencies||[]).map((e=>this._moduleIdProvider.getStrModuleId(e.id)))),this._resolve(u)}_normalizeDependency(e,t){if("exports"===e)return r.EXPORTS;if("module"===e)return r.MODULE;if("require"===e)return r.REQUIRE;let n=e.indexOf("!");if(n>=0){let i=t.resolveModule(e.substr(0,n)),r=t.resolveModule(e.substr(n+1)),o=this._moduleIdProvider.getModuleId(i+"!"+r),a=this._moduleIdProvider.getModuleId(i);return new s(o,a,r)}return new r(this._moduleIdProvider.getModuleId(t.resolveModule(e)))}_normalizeDependencies(e,t){let n=[],i=0;for(let r=0,s=e.length;r<s;r++)n[i++]=this._normalizeDependency(e[r],t);return n}_relativeRequire(t,n,i,r){if("string"==typeof n)return this.synchronousRequire(n,t);this.defineModule(e.Utilities.generateAnonymousModule(),n,i,r,null,t)}synchronousRequire(e,n=new t(e)){let i=this._normalizeDependency(e,n),r=this._modules2[i.id];if(!r)throw new Error("Check dependency list! Synchronous require cannot resolve module '"+e+"'. This is the first mention of this module!");if(!r.isComplete())throw new Error("Check dependency list! Synchronous require cannot resolve module '"+e+"'. This module has not been resolved completely yet.");if(r.error)throw r.error;return r.exports}configure(t,n){let i=this._config.shouldRecordStats();this._config=n?new e.Configuration(this._env,t):this._config.cloneAndMerge(t),this._config.shouldRecordStats()&&!i&&(this._recorder=null)}getConfig(){return this._config}_onLoad(e){if(null!==this._currentAnonymousDefineCall){let t=this._currentAnonymousDefineCall;this._currentAnonymousDefineCall=null,this.defineModule(this._moduleIdProvider.getStrModuleId(e),t.dependencies,t.callback,null,t.stack)}}_createLoadError(t,n){let i=this._moduleIdProvider.getStrModuleId(t),r=(this._inverseDependencies2[t]||[]).map((e=>this._moduleIdProvider.getStrModuleId(e)));const s=e.ensureError(n);return s.phase="loading",s.moduleId=i,s.neededBy=r,s}_onLoadError(e,t){const i=this._createLoadError(e,t);this._modules2[e]||(this._modules2[e]=new n(e,this._moduleIdProvider.getStrModuleId(e),[],(()=>{}),null,null));let r=[];for(let e=0,t=this._moduleIdProvider.getMaxModuleId();e<t;e++)r[e]=!1;let s=!1,o=[];for(o.push(e),r[e]=!0;o.length>0;){let e=o.shift(),t=this._modules2[e];t&&(s=t.onDependencyError(i)||s);let n=this._inverseDependencies2[e];if(n)for(let e=0,t=n.length;e<t;e++){let t=n[e];r[t]||(o.push(t),r[t]=!0)}}s||this._config.onError(i)}_hasDependencyPath(e,t){let n=this._modules2[e];if(!n)return!1;let i=[];for(let e=0,t=this._moduleIdProvider.getMaxModuleId();e<t;e++)i[e]=!1;let r=[];for(r.push(n),i[e]=!0;r.length>0;){let e=r.shift().dependencies;if(e)for(let n=0,s=e.length;n<s;n++){let s=e[n];if(s.id===t)return!0;let o=this._modules2[s.id];o&&!i[s.id]&&(i[s.id]=!0,r.push(o))}}return!1}_findCyclePath(e,t,n){if(e===t||50===n)return[e];let i=this._modules2[e];if(!i)return null;let r=i.dependencies;if(r)for(let i=0,s=r.length;i<s;i++){let s=this._findCyclePath(r[i].id,t,n+1);if(null!==s)return s.push(e),s}return null}_createRequire(t){let n=(e,n,i)=>this._relativeRequire(t,e,n,i);return n.toUrl=e=>this._config.requireToUrl(t.resolveModule(e)),n.getStats=()=>this.getLoaderEvents(),n.hasDependencyCycle=()=>this._hasDependencyCycle,n.config=(e,t=!1)=>{this.configure(e,t)},n.__$__nodeRequire=e.global.nodeRequire,n}_loadModule(e){if(this._modules2[e]||this._knownModules2[e])return;this._knownModules2[e]=!0;let t=this._moduleIdProvider.getStrModuleId(e),n=this._config.moduleIdToPaths(t);this._env.isNode&&(-1===t.indexOf("/")||/^@[^\/]+\/[^\/]+$/.test(t))&&n.push("node|"+t);let i=-1,r=t=>{if(i++,i>=n.length)this._onLoadError(e,t);else{let t=n[i],s=this.getRecorder();if(this._config.isBuild()&&"empty:"===t)return this._buildInfoPath[e]=t,this.defineModule(this._moduleIdProvider.getStrModuleId(e),[],null,null,null),void this._onLoad(e);s.record(10,t),this._scriptLoader.load(this,t,(()=>{this._config.isBuild()&&(this._buildInfoPath[e]=t),s.record(11,t),this._onLoad(e)}),(e=>{s.record(12,t),r(e)}))}};r(null)}_loadPluginDependency(e,n){if(this._modules2[n.id]||this._knownModules2[n.id])return;this._knownModules2[n.id]=!0;let i=e=>{this.defineModule(this._moduleIdProvider.getStrModuleId(n.id),[],e,null,null)};i.error=e=>{this._config.onError(this._createLoadError(n.id,e))},e.load(n.pluginParam,this._createRequire(t.ROOT),i,this._config.getOptionsLiteral())}_resolve(e){let t=e.dependencies;if(t)for(let n=0,i=t.length;n<i;n++){let i=t[n];if(i===r.EXPORTS){e.exportsPassedIn=!0,e.unresolvedDependenciesCount--;continue}if(i===r.MODULE){e.unresolvedDependenciesCount--;continue}if(i===r.REQUIRE){e.unresolvedDependenciesCount--;continue}let o=this._modules2[i.id];if(o&&o.isComplete()){if(o.error)return void e.onDependencyError(o.error);e.unresolvedDependenciesCount--}else if(this._hasDependencyPath(i.id,e.id)){this._hasDependencyCycle=!0,console.warn("There is a dependency cycle between '"+this._moduleIdProvider.getStrModuleId(i.id)+"' and '"+this._moduleIdProvider.getStrModuleId(e.id)+"'. The cyclic path follows:");let t=this._findCyclePath(i.id,e.id,0)||[];t.reverse(),t.push(i.id),console.warn(t.map((e=>this._moduleIdProvider.getStrModuleId(e))).join(" => \n")),e.unresolvedDependenciesCount--}else if(this._inverseDependencies2[i.id]=this._inverseDependencies2[i.id]||[],this._inverseDependencies2[i.id].push(e.id),i instanceof s){let e=this._modules2[i.pluginId];if(e&&e.isComplete()){this._loadPluginDependency(e.exports,i);continue}let t=this._inversePluginDependencies2.get(i.pluginId);t||(t=[],this._inversePluginDependencies2.set(i.pluginId,t)),t.push(i),this._loadModule(i.pluginId)}else this._loadModule(i.id)}0===e.unresolvedDependenciesCount&&this._onModuleComplete(e)}_onModuleComplete(e){let t=this.getRecorder();if(e.isComplete())return;let n=e.dependencies,i=[];if(n)for(let t=0,s=n.length;t<s;t++){let s=n[t];if(s===r.EXPORTS){i[t]=e.exports;continue}if(s===r.MODULE){i[t]={id:e.strId,config:()=>this._config.getConfigForModule(e.strId)};continue}if(s===r.REQUIRE){i[t]=this._createRequire(e.moduleIdResolver);continue}let o=this._modules2[s.id];i[t]=o?o.exports:null}e.complete(t,this._config,i,(e=>(this._inverseDependencies2[e]||[]).map((e=>this._moduleIdProvider.getStrModuleId(e)))));let s=this._inverseDependencies2[e.id];if(this._inverseDependencies2[e.id]=null,s)for(let e=0,t=s.length;e<t;e++){let t=s[e],n=this._modules2[t];n.unresolvedDependenciesCount--,0===n.unresolvedDependenciesCount&&this._onModuleComplete(n)}let o=this._inversePluginDependencies2.get(e.id);if(o){this._inversePluginDependencies2.delete(e.id);for(let t=0,n=o.length;t<n;t++)this._loadPluginDependency(e.exports,o[t])}}}e.ModuleManager=o}(s||(s={})),function(e){const t=new e.Environment;let n=null;const i=function(e,t,i){"string"!=typeof e&&(i=t,t=e,e=null),("object"!=typeof t||!Array.isArray(t))&&(i=t,t=null),t||(t=["require","exports","module"]),e?n.defineModule(e,t,i,null,null):n.enqueueDefineAnonymousModule(t,i)};i.amd={jQuery:!0};const s=function(e,t=!1){n.configure(e,t)},o=function(){if(1===arguments.length){if(arguments[0]instanceof Object&&!Array.isArray(arguments[0]))return void s(arguments[0]);if("string"==typeof arguments[0])return n.synchronousRequire(arguments[0])}if(2!==arguments.length&&3!==arguments.length||!Array.isArray(arguments[0]))throw new Error("Unrecognized require call");n.defineModule(e.Utilities.generateAnonymousModule(),arguments[0],arguments[1],arguments[2],null)};function a(){if(typeof e.global.require<"u"||typeof require<"u"){const t=e.global.require||require;if("function"==typeof t&&"function"==typeof t.resolve){const i=e.ensureRecordedNodeRequire(n.getRecorder(),t);e.global.nodeRequire=i,o.nodeRequire=i,o.__$__nodeRequire=i}}!t.isNode||t.isElectronRenderer||t.isElectronNodeIntegrationWebWorker?(t.isElectronRenderer||(e.global.define=i),e.global.require=o):module.exports=o}o.config=s,o.getConfig=function(){return n.getConfig().getOptionsLiteral()},o.reset=function(){n=n.reset()},o.getBuildInfo=function(){return n.getBuildInfo()},o.getStats=function(){return n.getLoaderEvents()},o.define=i,e.init=a,("function"!=typeof e.global.define||!e.global.define.amd)&&(n=new e.ModuleManager(t,e.createScriptLoader(t),i,o,e.Utilities.getHighPerformanceTimestamp()),typeof e.global.require<"u"&&"function"!=typeof e.global.require&&o.config(e.global.require),r=function(){return i.apply(null,arguments)},r.amd=i.amd,typeof doNotInitLoader>"u"&&a())}(s||(s={}));var o=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))((function(r,s){function o(e){try{l(i.next(e))}catch(e){s(e)}}function a(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){e.done?r(e.value):function(e){return e instanceof n?e:new n((function(t){t(e)}))}(e.value).then(o,a)}l((i=i.apply(e,t||[])).next())}))};r(e[19],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.load=t.create=t.setPseudoTranslation=t.getConfiguredDefaultLocale=t.localize=void 0;let n=typeof document<"u"&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;function i(e,t){let i;return i=0===t.length?e:e.replace(/\{(\d+)\}/g,((e,n)=>{const i=n[0],r=t[i];let s=e;return"string"==typeof r?s=r:("number"==typeof r||"boolean"==typeof r||null==r)&&(s=String(r)),s})),n&&(i="［"+i.replace(/[aouei]/g,"$&$&")+"］"),i}function r(e){return"/"===e.charAt(e.length-1)?e:e+"/"}function s(e,t,n){return o(this,void 0,void 0,(function*(){const i=r(e)+r(t)+"vscode/"+r(n),s=yield fetch(i);if(s.ok)return yield s.json();throw new Error(`${s.status} - ${s.statusText}`)}))}function a(e){return function(t,n){const r=Array.prototype.slice.call(arguments,2);return i(e[t],r)}}function l(e,t,...n){return i(t,n)}t.localize=l,t.getConfiguredDefaultLocale=function(e){},t.setPseudoTranslation=function(e){n=e},t.create=function(e,t){var n;return{localize:a(t[e]),getConfiguredDefaultLocale:null!==(n=t.getConfiguredDefaultLocale)&&void 0!==n?n:e=>{}}},t.load=function(e,t,n,i){var r;const u=null!==(r=i["vs/nls"])&&void 0!==r?r:{};if(!e||0===e.length)return n({localize:l,getConfiguredDefaultLocale:()=>{var e;return null===(e=u.availableLanguages)||void 0===e?void 0:e["*"]}});const c=u.availableLanguages?function(e,t){let n=e[t];return n||(n=e["*"],n)?n:null}(u.availableLanguages,e):null,h=null===c||"i-default"===c;let d=".nls";h||(d=d+"."+c);const f=t=>{Array.isArray(t)?t.localize=a(t):t.localize=a(t[e]),t.getConfiguredDefaultLocale=()=>{var e;return null===(e=u.availableLanguages)||void 0===e?void 0:e["*"]},n(t)};"function"==typeof u.loadBundle?u.loadBundle(e,c,((n,i)=>{n?t([e+".nls"],f):f(i)})):u.translationServiceUrl&&!h?o(this,void 0,void 0,(function*(){var n;try{const t=yield s(u.translationServiceUrl,c,e);return f(t)}catch(i){if(!c.includes("-"))return console.error(i),t([e+".nls"],f);try{const t=c.split("-")[0],i=yield s(u.translationServiceUrl,t,e);return null!==(n=u.availableLanguages)&&void 0!==n||(u.availableLanguages={}),u.availableLanguages["*"]=t,f(i)}catch(n){return console.error(n),t([e+".nls"],f)}}})):t([e+d],f,(n=>{".nls"!==d?(console.error(`Failed to load message bundle for language ${c}. Falling back to the default language:`,n),t([e+".nls"],f)):console.error("Failed trying to load default language strings",n)}))}})),function(){const e=globalThis.MonacoEnvironment,t=e&&e.baseUrl?e.baseUrl:"../../../",n=function(t,n){var i;if(e?.createTrustedTypesPolicy)try{return e.createTrustedTypesPolicy(t,n)}catch(e){return void console.warn(e)}try{return null===(i=self.trustedTypes)||void 0===i?void 0:i.createPolicy(t,n)}catch(e){return void console.warn(e)}}("amdLoader",{createScriptURL:e=>e,createScript:(e,...t)=>`(function anonymous(${t.slice(0,-1).join(",")}) { ${t.pop().toString()}\n})`});function i(){require.config({baseUrl:t,catchError:!0,trustedTypesPolicy:n,amdModulesPattern:/^vs\//})}"function"==typeof globalThis.define&&globalThis.define.amd&&i();let r=!0;const s=[];globalThis.onmessage=e=>{r?(r=!1,function(e){new Promise(((e,i)=>{if("function"==typeof globalThis.define&&globalThis.define.amd)return e();const r=t+"vs/loader.js";/^((http:)|(https:)|(file:))/.test(r)&&r.substring(0,globalThis.origin.length)!==globalThis.origin||!function(){try{return(n?globalThis.eval(n.createScript("","true")):new Function("true")).call(globalThis),!0}catch{return!1}}()?(n?importScripts(n.createScriptURL(r)):importScripts(r),e()):fetch(r).then((e=>{if(200!==e.status)throw new Error(e.statusText);return e.text()})).then((t=>{t=`${t}\n//# sourceURL=${r}`,(n?globalThis.eval(n.createScript("",t)):new Function(t)).call(globalThis),e()})).then(void 0,i)})).then((()=>{i(),require([e],(function(e){setTimeout((function(){const t=e.create(((e,t)=>{globalThis.postMessage(e,t)}),null);for(globalThis.onmessage=e=>t.onmessage(e.data,e.ports);s.length>0;){const e=s.shift();t.onmessage(e.data,e.ports)}}),0)}))}))}(e.data)):s.push(e)}}(),r(e[7],t([0,1]),(function(e,t){"use strict";function n(e,t){let n=0,i=e-1;for(;n<=i;){const e=(n+i)/2|0,r=t(e);if(r<0)n=e+1;else{if(!(r>0))return e;i=e-1}}return-(n+1)}function i(e,t,n){const i=r(e,t),s=e.length,o=n.length;e.length=s+o;for(let t=s-1;t>=i;t--)e[t+o]=e[t];for(let t=0;t<o;t++)e[t+i]=n[t]}function r(e,t){return t<0?Math.max(t+e.length,0):Math.min(t,e.length)}var s,o;Object.defineProperty(t,"__esModule",{value:!0}),t.CallbackIterable=t.ArrayQueue=t.reverseOrder=t.booleanComparator=t.numberComparator=t.tieBreakComparators=t.compareBy=t.CompareResult=t.splice=t.insertInto=t.asArray=t.pushMany=t.pushToEnd=t.pushToStart=t.arrayInsert=t.range=t.firstOrDefault=t.distinct=t.isNonEmptyArray=t.isFalsyOrEmpty=t.coalesceInPlace=t.coalesce=t.forEachWithNeighbors=t.forEachAdjacent=t.groupAdjacentBy=t.groupBy=t.quickSelect=t.binarySearch2=t.binarySearch=t.removeFastWithoutKeepingOrder=t.equals=t.tail2=t.tail=void 0,t.tail=function(e,t=0){return e[e.length-(1+t)]},t.tail2=function(e){if(0===e.length)throw new Error("Invalid tail call");return[e.slice(0,e.length-1),e[e.length-1]]},t.equals=function(e,t,n=((e,t)=>e===t)){if(e===t)return!0;if(!e||!t||e.length!==t.length)return!1;for(let i=0,r=e.length;i<r;i++)if(!n(e[i],t[i]))return!1;return!0},t.removeFastWithoutKeepingOrder=function(e,t){const n=e.length-1;t<n&&(e[t]=e[n]),e.pop()},t.binarySearch=function(e,t,i){return n(e.length,(n=>i(e[n],t)))},t.binarySearch2=n,t.quickSelect=function e(t,n,i){if((t|=0)>=n.length)throw new TypeError("invalid index");const r=n[Math.floor(n.length*Math.random())],s=[],o=[],a=[];for(const e of n){const t=i(e,r);t<0?s.push(e):t>0?o.push(e):a.push(e)}return t<s.length?e(t,s,i):t<s.length+a.length?a[0]:e(t-(s.length+a.length),o,i)},t.groupBy=function(e,t){const n=[];let i;for(const r of e.slice(0).sort(t))i&&0===t(i[0],r)?i.push(r):(i=[r],n.push(i));return n},t.groupAdjacentBy=function*(e,t){let n,i;for(const r of e)void 0!==i&&t(i,r)?n.push(r):(n&&(yield n),n=[r]),i=r;n&&(yield n)},t.forEachAdjacent=function(e,t){for(let n=0;n<=e.length;n++)t(0===n?void 0:e[n-1],n===e.length?void 0:e[n])},t.forEachWithNeighbors=function(e,t){for(let n=0;n<e.length;n++)t(0===n?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])},t.coalesce=function(e){return e.filter((e=>!!e))},t.coalesceInPlace=function(e){let t=0;for(let n=0;n<e.length;n++)e[n]&&(e[t]=e[n],t+=1);e.length=t},t.isFalsyOrEmpty=function(e){return!Array.isArray(e)||0===e.length},t.isNonEmptyArray=function(e){return Array.isArray(e)&&e.length>0},t.distinct=function(e,t=(e=>e)){const n=new Set;return e.filter((e=>{const i=t(e);return!n.has(i)&&(n.add(i),!0)}))},t.firstOrDefault=function(e,t){return e.length>0?e[0]:t},t.range=function(e,t){let n="number"==typeof t?e:0;"number"==typeof t?n=e:(n=0,t=e);const i=[];if(n<=t)for(let e=n;e<t;e++)i.push(e);else for(let e=n;e>t;e--)i.push(e);return i},t.arrayInsert=function(e,t,n){const i=e.slice(0,t),r=e.slice(t);return i.concat(n,r)},t.pushToStart=function(e,t){const n=e.indexOf(t);n>-1&&(e.splice(n,1),e.unshift(t))},t.pushToEnd=function(e,t){const n=e.indexOf(t);n>-1&&(e.splice(n,1),e.push(t))},t.pushMany=function(e,t){for(const n of t)e.push(n)},t.asArray=function(e){return Array.isArray(e)?e:[e]},t.insertInto=i,t.splice=function(e,t,n,s){const o=r(e,t);let a=e.splice(o,n);return void 0===a&&(a=[]),i(e,o,s),a},(o=s||(t.CompareResult=s={})).isLessThan=function(e){return e<0},o.isLessThanOrEqual=function(e){return e<=0},o.isGreaterThan=function(e){return e>0},o.isNeitherLessOrGreaterThan=function(e){return 0===e},o.greaterThan=1,o.lessThan=-1,o.neitherLessOrGreaterThan=0,t.compareBy=function(e,t){return(n,i)=>t(e(n),e(i))},t.tieBreakComparators=function(...e){return(t,n)=>{for(const i of e){const e=i(t,n);if(!s.isNeitherLessOrGreaterThan(e))return e}return s.neitherLessOrGreaterThan}},t.numberComparator=(e,t)=>e-t,t.booleanComparator=(e,n)=>(0,t.numberComparator)(e?1:0,n?1:0),t.reverseOrder=function(e){return(t,n)=>-e(t,n)},t.ArrayQueue=class{constructor(e){this.items=e,this.firstIdx=0,this.lastIdx=this.items.length-1}get length(){return this.lastIdx-this.firstIdx+1}takeWhile(e){let t=this.firstIdx;for(;t<this.items.length&&e(this.items[t]);)t++;const n=t===this.firstIdx?null:this.items.slice(this.firstIdx,t);return this.firstIdx=t,n}takeFromEndWhile(e){let t=this.lastIdx;for(;t>=0&&e(this.items[t]);)t--;const n=t===this.lastIdx?null:this.items.slice(t+1,this.lastIdx+1);return this.lastIdx=t,n}peek(){if(0!==this.length)return this.items[this.firstIdx]}dequeue(){const e=this.items[this.firstIdx];return this.firstIdx++,e}takeCount(e){const t=this.items.slice(this.firstIdx,this.firstIdx+e);return this.firstIdx+=e,t}};class a{constructor(e){this.iterate=e}toArray(){const e=[];return this.iterate((t=>(e.push(t),!0))),e}filter(e){return new a((t=>this.iterate((n=>!e(n)||t(n)))))}map(e){return new a((t=>this.iterate((n=>t(e(n))))))}findLast(e){let t;return this.iterate((n=>(e(n)&&(t=n),!0))),t}findLastMaxBy(e){let t,n=!0;return this.iterate((i=>((n||s.isGreaterThan(e(i,t)))&&(n=!1,t=i),!0))),t}}t.CallbackIterable=a,a.empty=new a((e=>{}))})),r(e[11],t([0,1]),(function(e,t){"use strict";function n(e,t,n=e.length-1){for(let i=n;i>=0;i--)if(t(e[i]))return i;return-1}function i(e,t,n=0,i=e.length){let r=n,s=i;for(;r<s;){const n=Math.floor((r+s)/2);t(e[n])?r=n+1:s=n}return r-1}function r(e,t,n=0,i=e.length){let r=n,s=i;for(;r<s;){const n=Math.floor((r+s)/2);t(e[n])?s=n:r=n+1}return r}Object.defineProperty(t,"__esModule",{value:!0}),t.mapFindFirst=t.findMaxIdxBy=t.findFirstMinBy=t.findLastMaxBy=t.findFirstMaxBy=t.MonotonousArray=t.findFirstIdxMonotonousOrArrLen=t.findFirstMonotonous=t.findLastIdxMonotonous=t.findLastMonotonous=t.findLastIdx=t.findLast=void 0,t.findLast=function(e,t,i){const r=n(e,t);if(-1!==r)return e[r]},t.findLastIdx=n,t.findLastMonotonous=function(e,t){const n=i(e,t);return-1===n?void 0:e[n]},t.findLastIdxMonotonous=i,t.findFirstMonotonous=function(e,t){const n=r(e,t);return n===e.length?void 0:e[n]},t.findFirstIdxMonotonousOrArrLen=r;class s{constructor(e){this._array=e,this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(s.assertInvariants){if(this._prevFindLastPredicate)for(const t of this._array)if(this._prevFindLastPredicate(t)&&!e(t))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.");this._prevFindLastPredicate=e}const t=i(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=t+1,-1===t?void 0:this._array[t]}}function o(e,t){if(0===e.length)return;let n=e[0];for(let i=1;i<e.length;i++){const r=e[i];t(r,n)>0&&(n=r)}return n}t.MonotonousArray=s,s.assertInvariants=!1,t.findFirstMaxBy=o,t.findLastMaxBy=function(e,t){if(0===e.length)return;let n=e[0];for(let i=1;i<e.length;i++){const r=e[i];t(r,n)>=0&&(n=r)}return n},t.findFirstMinBy=function(e,t){return o(e,((e,n)=>-t(e,n)))},t.findMaxIdxBy=function(e,t){if(0===e.length)return-1;let n=0;for(let i=1;i<e.length;i++)t(e[i],e[n])>0&&(n=i);return n},t.mapFindFirst=function(e,t){for(const n of e){const e=t(n);if(void 0!==e)return e}}})),r(e[32],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CachedFunction=t.LRUCachedFunction=void 0,t.LRUCachedFunction=class{constructor(e){this.fn=e,this.lastCache=void 0,this.lastArgKey=void 0}get(e){const t=JSON.stringify(e);return this.lastArgKey!==t&&(this.lastArgKey=t,this.lastCache=this.fn(e)),this.lastCache}},t.CachedFunction=class{get cachedValues(){return this._map}constructor(e){this.fn=e,this._map=new Map}get(e){if(this._map.has(e))return this._map.get(e);const t=this.fn(e);return this._map.set(e,t),t}}})),r(e[33],t([0,1]),(function(e,t){"use strict";function n(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}Object.defineProperty(t,"__esModule",{value:!0}),t.Color=t.HSVA=t.HSLA=t.RGBA=void 0;class i{constructor(e,t,i,r=1){this._rgbaBrand=void 0,this.r=0|Math.min(255,Math.max(0,e)),this.g=0|Math.min(255,Math.max(0,t)),this.b=0|Math.min(255,Math.max(0,i)),this.a=n(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}}t.RGBA=i;class r{constructor(e,t,i,r){this._hslaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=n(Math.max(Math.min(1,t),0),3),this.l=n(Math.max(Math.min(1,i),0),3),this.a=n(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.l===t.l&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,i=e.b/255,s=e.a,o=Math.max(t,n,i),a=Math.min(t,n,i);let l=0,u=0;const c=(a+o)/2,h=o-a;if(h>0){switch(u=Math.min(c<=.5?h/(2*c):h/(2-2*c),1),o){case t:l=(n-i)/h+(n<i?6:0);break;case n:l=(i-t)/h+2;break;case i:l=(t-n)/h+4}l*=60,l=Math.round(l)}return new r(l,u,c,s)}static _hue2rgb(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}static toRGBA(e){const t=e.h/360,{s:n,l:s,a:o}=e;let a,l,u;if(0===n)a=l=u=s;else{const e=s<.5?s*(1+n):s+n-s*n,i=2*s-e;a=r._hue2rgb(i,e,t+1/3),l=r._hue2rgb(i,e,t),u=r._hue2rgb(i,e,t-1/3)}return new i(Math.round(255*a),Math.round(255*l),Math.round(255*u),o)}}t.HSLA=r;class s{constructor(e,t,i,r){this._hsvaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=n(Math.max(Math.min(1,t),0),3),this.v=n(Math.max(Math.min(1,i),0),3),this.a=n(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.v===t.v&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,i=e.b/255,r=Math.max(t,n,i),o=r-Math.min(t,n,i),a=0===r?0:o/r;let l;return l=0===o?0:r===t?((n-i)/o%6+6)%6:r===n?(i-t)/o+2:(t-n)/o+4,new s(Math.round(60*l),a,r,e.a)}static toRGBA(e){const{h:t,s:n,v:r,a:s}=e,o=r*n,a=o*(1-Math.abs(t/60%2-1)),l=r-o;let[u,c,h]=[0,0,0];return t<60?(u=o,c=a):t<120?(u=a,c=o):t<180?(c=o,h=a):t<240?(c=a,h=o):t<300?(u=a,h=o):t<=360&&(u=o,h=a),u=Math.round(255*(u+l)),c=Math.round(255*(c+l)),h=Math.round(255*(h+l)),new i(u,c,h,s)}}t.HSVA=s;class o{static fromHex(e){return o.Format.CSS.parseHex(e)||o.red}static equals(e,t){return!e&&!t||!(!e||!t)&&e.equals(t)}get hsla(){return this._hsla?this._hsla:r.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:s.fromRGBA(this.rgba)}constructor(e){if(!e)throw new Error("Color needs a value");if(e instanceof i)this.rgba=e;else if(e instanceof r)this._hsla=e,this.rgba=r.toRGBA(e);else{if(!(e instanceof s))throw new Error("Invalid color ctor argument");this._hsva=e,this.rgba=s.toRGBA(e)}}equals(e){return!!e&&i.equals(this.rgba,e.rgba)&&r.equals(this.hsla,e.hsla)&&s.equals(this.hsva,e.hsva)}getRelativeLuminance(){return n(.2126*o._relativeLuminanceForComponent(this.rgba.r)+.7152*o._relativeLuminanceForComponent(this.rgba.g)+.0722*o._relativeLuminanceForComponent(this.rgba.b),4)}static _relativeLuminanceForComponent(e){const t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}isLighter(){return(299*this.rgba.r+587*this.rgba.g+114*this.rgba.b)/1e3>=128}isLighterThan(e){return this.getRelativeLuminance()>e.getRelativeLuminance()}isDarkerThan(e){return this.getRelativeLuminance()<e.getRelativeLuminance()}lighten(e){return new o(new r(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*e,this.hsla.a))}darken(e){return new o(new r(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*e,this.hsla.a))}transparent(e){const{r:t,g:n,b:r,a:s}=this.rgba;return new o(new i(t,n,r,s*e))}isTransparent(){return 0===this.rgba.a}isOpaque(){return 1===this.rgba.a}opposite(){return new o(new i(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(e){if(this.isOpaque()||1!==e.rgba.a)return this;const{r:t,g:n,b:r,a:s}=this.rgba;return new o(new i(e.rgba.r-s*(e.rgba.r-t),e.rgba.g-s*(e.rgba.g-n),e.rgba.b-s*(e.rgba.b-r),1))}toString(){return this._toString||(this._toString=o.Format.CSS.format(this)),this._toString}static getLighterColor(e,t,n){if(e.isLighterThan(t))return e;n=n||.5;const i=e.getRelativeLuminance(),r=t.getRelativeLuminance();return n=n*(r-i)/r,e.lighten(n)}static getDarkerColor(e,t,n){if(e.isDarkerThan(t))return e;n=n||.5;const i=e.getRelativeLuminance();return n=n*(i-t.getRelativeLuminance())/i,e.darken(n)}}t.Color=o,o.white=new o(new i(255,255,255,1)),o.black=new o(new i(0,0,0,1)),o.red=new o(new i(255,0,0,1)),o.blue=new o(new i(0,0,255,1)),o.green=new o(new i(0,255,0,1)),o.cyan=new o(new i(0,255,255,1)),o.lightgrey=new o(new i(211,211,211,1)),o.transparent=new o(new i(0,0,0,0)),function(e){let t;!function(t){let n;!function(t){function n(e){const t=e.toString(16);return 2!==t.length?"0"+t:t}function r(e){switch(e){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:case 65:return 10;case 98:case 66:return 11;case 99:case 67:return 12;case 100:case 68:return 13;case 101:case 69:return 14;case 102:case 70:return 15}return 0}t.formatRGB=function(t){return 1===t.rgba.a?`rgb(${t.rgba.r}, ${t.rgba.g}, ${t.rgba.b})`:e.Format.CSS.formatRGBA(t)},t.formatRGBA=function(e){return`rgba(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b}, ${+e.rgba.a.toFixed(2)})`},t.formatHSL=function(t){return 1===t.hsla.a?`hsl(${t.hsla.h}, ${(100*t.hsla.s).toFixed(2)}%, ${(100*t.hsla.l).toFixed(2)}%)`:e.Format.CSS.formatHSLA(t)},t.formatHSLA=function(e){return`hsla(${e.hsla.h}, ${(100*e.hsla.s).toFixed(2)}%, ${(100*e.hsla.l).toFixed(2)}%, ${e.hsla.a.toFixed(2)})`},t.formatHex=function(e){return`#${n(e.rgba.r)}${n(e.rgba.g)}${n(e.rgba.b)}`},t.formatHexA=function(t,i=!1){return i&&1===t.rgba.a?e.Format.CSS.formatHex(t):`#${n(t.rgba.r)}${n(t.rgba.g)}${n(t.rgba.b)}${n(Math.round(255*t.rgba.a))}`},t.format=function(t){return t.isOpaque()?e.Format.CSS.formatHex(t):e.Format.CSS.formatRGBA(t)},t.parseHex=function(t){const n=t.length;if(0===n||35!==t.charCodeAt(0))return null;if(7===n){const n=16*r(t.charCodeAt(1))+r(t.charCodeAt(2)),s=16*r(t.charCodeAt(3))+r(t.charCodeAt(4)),o=16*r(t.charCodeAt(5))+r(t.charCodeAt(6));return new e(new i(n,s,o,1))}if(9===n){const n=16*r(t.charCodeAt(1))+r(t.charCodeAt(2)),s=16*r(t.charCodeAt(3))+r(t.charCodeAt(4)),o=16*r(t.charCodeAt(5))+r(t.charCodeAt(6)),a=16*r(t.charCodeAt(7))+r(t.charCodeAt(8));return new e(new i(n,s,o,a/255))}if(4===n){const n=r(t.charCodeAt(1)),s=r(t.charCodeAt(2)),o=r(t.charCodeAt(3));return new e(new i(16*n+n,16*s+s,16*o+o))}if(5===n){const n=r(t.charCodeAt(1)),s=r(t.charCodeAt(2)),o=r(t.charCodeAt(3)),a=r(t.charCodeAt(4));return new e(new i(16*n+n,16*s+s,16*o+o,(16*a+a)/255))}return null}}(n=t.CSS||(t.CSS={}))}(t=e.Format||(e.Format={}))}(o||(t.Color=o={}))})),r(e[34],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DiffChange=void 0,t.DiffChange=class{constructor(e,t,n,i){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=i}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}})),r(e[5],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BugIndicatingError=t.ErrorNoTelemetry=t.NotSupportedError=t.illegalState=t.illegalArgument=t.canceled=t.CancellationError=t.isCancellationError=t.transformErrorForSerialization=t.onUnexpectedExternalError=t.onUnexpectedError=t.errorHandler=t.ErrorHandler=void 0;class n{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout((()=>{throw e.stack?a.isErrorNoTelemetry(e)?new a(e.message+"\n\n"+e.stack):new Error(e.message+"\n\n"+e.stack):e}),0)}}emit(e){this.listeners.forEach((t=>{t(e)}))}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}}t.ErrorHandler=n,t.errorHandler=new n,t.onUnexpectedError=function(e){r(e)||t.errorHandler.onUnexpectedError(e)},t.onUnexpectedExternalError=function(e){r(e)||t.errorHandler.onUnexpectedExternalError(e)},t.transformErrorForSerialization=function(e){if(e instanceof Error){const{name:t,message:n}=e;return{$isError:!0,name:t,message:n,stack:e.stacktrace||e.stack,noTelemetry:a.isErrorNoTelemetry(e)}}return e};const i="Canceled";function r(e){return e instanceof s||e instanceof Error&&e.name===i&&e.message===i}t.isCancellationError=r;class s extends Error{constructor(){super(i),this.name=this.message}}t.CancellationError=s,t.canceled=function(){const e=new Error(i);return e.name=e.message,e},t.illegalArgument=function(e){return e?new Error(`Illegal argument: ${e}`):new Error("Illegal argument")},t.illegalState=function(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")};class o extends Error{constructor(e){super("NotSupported"),e&&(this.message=e)}}t.NotSupportedError=o;class a extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof a)return e;const t=new a;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return"CodeExpectedError"===e.name}}t.ErrorNoTelemetry=a;class l extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,l.prototype)}}t.BugIndicatingError=l})),r(e[12],t([0,1,5]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkAdjacentItems=t.assertFn=t.assertNever=t.ok=void 0,t.ok=function(e,t){if(!e)throw new Error(t?`Assertion failed (${t})`:"Assertion Failed")},t.assertNever=function(e,t="Unreachable"){throw new Error(t)},t.assertFn=function(e){e()||(e(),(0,n.onUnexpectedError)(new n.BugIndicatingError("Assertion Failed")))},t.checkAdjacentItems=function(e,t){let n=0;for(;n<e.length-1;){if(!t(e[n],e[n+1]))return!1;n++}return!0}})),r(e[20],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createSingleCallFunction=void 0,t.createSingleCallFunction=function(e){const t=this;let n,i=!1;return function(){return i||(i=!0,n=e.apply(t,arguments)),n}}})),r(e[21],t([0,1]),(function(e,t){"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Iterable=void 0,function(e){function t(e){return e&&"object"==typeof e&&"function"==typeof e[Symbol.iterator]}e.is=t;const n=Object.freeze([]);function*i(e){yield e}e.empty=function(){return n},e.single=i,e.wrap=function(e){return t(e)?e:i(e)},e.from=function(e){return e||n},e.reverse=function*(e){for(let t=e.length-1;t>=0;t--)yield e[t]},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){for(const n of e)if(t(n))return!0;return!1},e.find=function(e,t){for(const n of e)if(t(n))return n},e.filter=function*(e,t){for(const n of e)t(n)&&(yield n)},e.map=function*(e,t){let n=0;for(const i of e)yield t(i,n++)},e.concat=function*(...e){for(const t of e)for(const e of t)yield e},e.reduce=function(e,t,n){let i=n;for(const n of e)i=t(i,n);return i},e.slice=function*(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]},e.consume=function(t,n=Number.POSITIVE_INFINITY){const i=[];if(0===n)return[i,t];const r=t[Symbol.iterator]();for(let t=0;t<n;t++){const t=r.next();if(t.done)return[i,e.empty()];i.push(t.value)}return[i,{[Symbol.iterator]:()=>r}]}}(n||(t.Iterable=n={}))})),r(e[35],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.KeyChord=t.KeyCodeUtils=t.IMMUTABLE_KEY_CODE_TO_CODE=t.IMMUTABLE_CODE_TO_KEY_CODE=t.NATIVE_WINDOWS_KEY_CODE_TO_KEY_CODE=t.EVENT_KEY_CODE_MAP=void 0;class n{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}}const i=new n,r=new n,s=new n;t.EVENT_KEY_CODE_MAP=new Array(230),t.NATIVE_WINDOWS_KEY_CODE_TO_KEY_CODE={};const o=[],a=Object.create(null),l=Object.create(null);t.IMMUTABLE_CODE_TO_KEY_CODE=[],t.IMMUTABLE_KEY_CODE_TO_CODE=[];for(let e=0;e<=193;e++)t.IMMUTABLE_CODE_TO_KEY_CODE[e]=-1;for(let e=0;e<=132;e++)t.IMMUTABLE_KEY_CODE_TO_CODE[e]=-1;var u,c;!function(){const e="",n=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],u=[],c=[];for(const e of n){const[n,h,d,f,g,m,p,_,b]=e;if(c[h]||(c[h]=!0,o[h]=d,a[d]=h,l[d.toLowerCase()]=h,n&&(t.IMMUTABLE_CODE_TO_KEY_CODE[h]=f,0!==f&&3!==f&&5!==f&&4!==f&&6!==f&&57!==f&&(t.IMMUTABLE_KEY_CODE_TO_CODE[f]=h))),!u[f]){if(u[f]=!0,!g)throw new Error(`String representation missing for key code ${f} around scan code ${d}`);i.define(f,g),r.define(f,_||g),s.define(f,b||_||g)}m&&(t.EVENT_KEY_CODE_MAP[m]=f),p&&(t.NATIVE_WINDOWS_KEY_CODE_TO_KEY_CODE[p]=f)}t.IMMUTABLE_KEY_CODE_TO_CODE[3]=46}(),(c=u||(t.KeyCodeUtils=u={})).toString=function(e){return i.keyCodeToStr(e)},c.fromString=function(e){return i.strToKeyCode(e)},c.toUserSettingsUS=function(e){return r.keyCodeToStr(e)},c.toUserSettingsGeneral=function(e){return s.keyCodeToStr(e)},c.fromUserSettings=function(e){return r.strToKeyCode(e)||s.strToKeyCode(e)},c.toElectronAccelerator=function(e){if(e>=98&&e<=113)return null;switch(e){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return i.keyCodeToStr(e)},t.KeyChord=function(e,t){return(e|(65535&t)<<16>>>0)>>>0}})),r(e[36],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Lazy=void 0,t.Lazy=class{constructor(e){this.executor=e,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(e){this._error=e}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}})),r(e[13],t([0,1,20,21]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DisposableMap=t.ImmortalReference=t.RefCountedDisposable=t.MutableDisposable=t.Disposable=t.DisposableStore=t.toDisposable=t.combinedDisposable=t.dispose=t.isDisposable=t.markAsSingleton=t.markAsDisposed=t.trackDisposable=t.setDisposableTracker=void 0;let r=null;function s(e){return r?.trackDisposable(e),e}function o(e){r?.markAsDisposed(e)}function a(e,t){r?.setParent(e,t)}function l(e){if(i.Iterable.is(e)){const t=[];for(const n of e)if(n)try{n.dispose()}catch(e){t.push(e)}if(1===t.length)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function u(e){const t=s({dispose:(0,n.createSingleCallFunction)((()=>{o(t),e()}))});return t}t.setDisposableTracker=function(e){r=e},t.trackDisposable=s,t.markAsDisposed=o,t.markAsSingleton=function(e){return r?.markAsSingleton(e),e},t.isDisposable=function(e){return"function"==typeof e.dispose&&0===e.dispose.length},t.dispose=l,t.combinedDisposable=function(...e){const t=u((()=>l(e)));return function(e,t){if(r)for(const n of e)r.setParent(n,t)}(e,t),t},t.toDisposable=u;class c{constructor(){this._toDispose=new Set,this._isDisposed=!1,s(this)}dispose(){this._isDisposed||(o(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(0!==this._toDispose.size)try{l(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return a(e,this),this._isDisposed?c.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(e),e}deleteAndLeak(e){e&&this._toDispose.has(e)&&(this._toDispose.delete(e),a(e,null))}}t.DisposableStore=c,c.DISABLE_DISPOSED_WARNING=!1;class h{constructor(){this._store=new c,s(this),a(this._store,this)}dispose(){o(this),this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}t.Disposable=h,h.None=Object.freeze({dispose(){}}),t.MutableDisposable=class{constructor(){this._isDisposed=!1,s(this)}get value(){return this._isDisposed?void 0:this._value}set value(e){var t;this._isDisposed||e===this._value||(null===(t=this._value)||void 0===t||t.dispose(),e&&a(e,this),this._value=e)}clear(){this.value=void 0}dispose(){var e;this._isDisposed=!0,o(this),null===(e=this._value)||void 0===e||e.dispose(),this._value=void 0}},t.RefCountedDisposable=class{constructor(e){this._disposable=e,this._counter=1}acquire(){return this._counter++,this}release(){return 0==--this._counter&&this._disposable.dispose(),this}},t.ImmortalReference=class{constructor(e){this.object=e}dispose(){}};class d{constructor(){this._store=new Map,this._isDisposed=!1,s(this)}dispose(){o(this),this._isDisposed=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this._store.size)try{l(this._store.values())}finally{this._store.clear()}}get(e){return this._store.get(e)}set(e,t,n=!1){var i;this._isDisposed&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),n||null===(i=this._store.get(e))||void 0===i||i.dispose(),this._store.set(e,t)}deleteAndDispose(e){var t;null===(t=this._store.get(e))||void 0===t||t.dispose(),this._store.delete(e)}[Symbol.iterator](){return this._store[Symbol.iterator]()}}t.DisposableMap=d})),r(e[22],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LinkedList=void 0;class n{constructor(e){this.element=e,this.next=n.Undefined,this.prev=n.Undefined}}n.Undefined=new n(void 0);class i{constructor(){this._first=n.Undefined,this._last=n.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===n.Undefined}clear(){let e=this._first;for(;e!==n.Undefined;){const t=e.next;e.prev=n.Undefined,e.next=n.Undefined,e=t}this._first=n.Undefined,this._last=n.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){const i=new n(e);if(this._first===n.Undefined)this._first=i,this._last=i;else if(t){const e=this._last;this._last=i,i.prev=e,e.next=i}else{const e=this._first;this._first=i,i.next=e,e.prev=i}this._size+=1;let r=!1;return()=>{r||(r=!0,this._remove(i))}}shift(){if(this._first!==n.Undefined){const e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==n.Undefined){const e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==n.Undefined&&e.next!==n.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===n.Undefined&&e.next===n.Undefined?(this._first=n.Undefined,this._last=n.Undefined):e.next===n.Undefined?(this._last=this._last.prev,this._last.next=n.Undefined):e.prev===n.Undefined&&(this._first=this._first.next,this._first.prev=n.Undefined);this._size-=1}*[Symbol.iterator](){let e=this._first;for(;e!==n.Undefined;)yield e.element,e=e.next}}t.LinkedList=i})),r(e[37],t([0,1]),(function(e,t){"use strict";var n,i;Object.defineProperty(t,"__esModule",{value:!0}),t.SetMap=t.BidirectionalMap=t.LRUCache=t.LinkedMap=t.ResourceMap=void 0;class r{constructor(e,t){this.uri=e,this.value=t}}class s{constructor(e,t){if(this[n]="ResourceMap",e instanceof s)this.map=new Map(e.map),this.toKey=t??s.defaultToKey;else if(i=e,Array.isArray(i)){this.map=new Map,this.toKey=t??s.defaultToKey;for(const[t,n]of e)this.set(t,n)}else this.map=new Map,this.toKey=e??s.defaultToKey;var i}set(e,t){return this.map.set(this.toKey(e),new r(e,t)),this}get(e){var t;return null===(t=this.map.get(this.toKey(e)))||void 0===t?void 0:t.value}has(e){return this.map.has(this.toKey(e))}get size(){return this.map.size}clear(){this.map.clear()}delete(e){return this.map.delete(this.toKey(e))}forEach(e,t){typeof t<"u"&&(e=e.bind(t));for(const[t,n]of this.map)e(n.value,n.uri,this)}*values(){for(const e of this.map.values())yield e.value}*keys(){for(const e of this.map.values())yield e.uri}*entries(){for(const e of this.map.values())yield[e.uri,e.value]}*[(n=Symbol.toStringTag,Symbol.iterator)](){for(const[,e]of this.map)yield[e.uri,e.value]}}t.ResourceMap=s,s.defaultToKey=e=>e.toString();class o{constructor(){this[i]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){var e;return null===(e=this._head)||void 0===e?void 0:e.value}get last(){var e;return null===(e=this._tail)||void 0===e?void 0:e.value}has(e){return this._map.has(e)}get(e,t=0){const n=this._map.get(e);if(n)return 0!==t&&this.touch(n,t),n.value}set(e,t,n=0){let i=this._map.get(e);if(i)i.value=t,0!==n&&this.touch(i,n);else{switch(i={key:e,value:t,next:void 0,previous:void 0},n){case 0:case 2:default:this.addItemLast(i);break;case 1:this.addItemFirst(i)}this._map.set(e,i),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this._map.get(e);if(t)return this._map.delete(e),this.removeItem(t),this._size--,t.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");const e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,t){const n=this._state;let i=this._head;for(;i;){if(t?e.bind(t)(i.value,i.key,this):e(i.value,i.key,this),this._state!==n)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){const e=this,t=this._state;let n=this._head;const i={[Symbol.iterator]:()=>i,next(){if(e._state!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const e={value:n.key,done:!1};return n=n.next,e}return{value:void 0,done:!0}}};return i}values(){const e=this,t=this._state;let n=this._head;const i={[Symbol.iterator]:()=>i,next(){if(e._state!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const e={value:n.value,done:!1};return n=n.next,e}return{value:void 0,done:!0}}};return i}entries(){const e=this,t=this._state;let n=this._head;const i={[Symbol.iterator]:()=>i,next(){if(e._state!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const e={value:[n.key,n.value],done:!1};return n=n.next,e}return{value:void 0,done:!0}}};return i}[(i=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(0===e)return void this.clear();let t=this._head,n=this.size;for(;t&&n>e;)this._map.delete(t.key),t=t.next,n--;this._head=t,this._size=n,t&&(t.previous=void 0),this._state++}addItemFirst(e){if(this._head||this._tail){if(!this._head)throw new Error("Invalid list");e.next=this._head,this._head.previous=e}else this._tail=e;this._head=e,this._state++}addItemLast(e){if(this._head||this._tail){if(!this._tail)throw new Error("Invalid list");e.previous=this._tail,this._tail.next=e}else this._head=e;this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this._state++}touch(e,t){if(!this._head||!this._tail)throw new Error("Invalid list");if(1===t||2===t)if(1===t){if(e===this._head)return;const t=e.next,n=e.previous;e===this._tail?(n.next=void 0,this._tail=n):(t.previous=n,n.next=t),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(2===t){if(e===this._tail)return;const t=e.next,n=e.previous;e===this._head?(t.previous=void 0,this._head=t):(t.previous=n,n.next=t),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}toJSON(){const e=[];return this.forEach(((t,n)=>{e.push([n,t])})),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}}t.LinkedMap=o,t.LRUCache=class extends o{constructor(e,t=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,t),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}},t.BidirectionalMap=class{constructor(e){if(this._m1=new Map,this._m2=new Map,e)for(const[t,n]of e)this.set(t,n)}clear(){this._m1.clear(),this._m2.clear()}set(e,t){this._m1.set(e,t),this._m2.set(t,e)}get(e){return this._m1.get(e)}getKey(e){return this._m2.get(e)}delete(e){const t=this._m1.get(e);return void 0!==t&&(this._m1.delete(e),this._m2.delete(t),!0)}keys(){return this._m1.keys()}values(){return this._m1.values()}},t.SetMap=class{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){const n=this.map.get(e);n&&(n.delete(t),0===n.size&&this.map.delete(e))}forEach(e,t){const n=this.map.get(e);n&&n.forEach(t)}get(e){return this.map.get(e)||new Set}}})),r(e[23],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StopWatch=void 0;const n=globalThis.performance&&"function"==typeof globalThis.performance.now;class i{static create(e){return new i(e)}constructor(e){this._now=n&&!1===e?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}elapsed(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime}}t.StopWatch=i})),r(e[9],t([0,1,5,20,13,22,23]),(function(e,t,n,i,r,s,o){"use strict";var a;Object.defineProperty(t,"__esModule",{value:!0}),t.Relay=t.EventBufferer=t.EventMultiplexer=t.MicrotaskEmitter=t.DebounceEmitter=t.PauseableEmitter=t.createEventDeliveryQueue=t.Emitter=t.EventProfiling=t.Event=void 0,function(e){function t(e){return(t,n=null,i)=>{let r,s=!1;return r=e((e=>{if(!s)return r?r.dispose():s=!0,t.call(n,e)}),null,i),s&&r.dispose(),r}}function n(e,t,n){return s(((n,i=null,r)=>e((e=>n.call(i,t(e))),null,r)),n)}function i(e,t,n){return s(((n,i=null,r)=>e((e=>t(e)&&n.call(i,e)),null,r)),n)}function s(e,t){let n;const i=new d({onWillAddFirstListener(){n=e(i.fire,i)},onDidRemoveLastListener(){n?.dispose()}});return t?.add(i),i.event}function o(e,t,n=100,i=!1,r=!1,s,o){let a,l,u,c,h=0;const f=new d({leakWarningThreshold:s,onWillAddFirstListener(){a=e((e=>{h++,l=t(l,e),i&&!u&&(f.fire(l),l=void 0),c=()=>{const e=l;l=void 0,u=void 0,(!i||h>1)&&f.fire(e),h=0},"number"==typeof n?(clearTimeout(u),u=setTimeout(c,n)):void 0===u&&(u=0,queueMicrotask(c))}))},onWillRemoveListener(){r&&h>0&&c?.()},onDidRemoveLastListener(){c=void 0,a.dispose()}});return o?.add(f),f.event}e.None=()=>r.Disposable.None,e.defer=function(e,t){return o(e,(()=>{}),0,void 0,!0,void 0,t)},e.once=t,e.map=n,e.forEach=function(e,t,n){return s(((n,i=null,r)=>e((e=>{t(e),n.call(i,e)}),null,r)),n)},e.filter=i,e.signal=function(e){return e},e.any=function(...e){return(t,n=null,i)=>function(e,t){return t instanceof Array?t.push(e):t&&t.add(e),e}((0,r.combinedDisposable)(...e.map((e=>e((e=>t.call(n,e)))))),i)},e.reduce=function(e,t,i,r){let s=i;return n(e,(e=>(s=t(s,e),s)),r)},e.debounce=o,e.accumulate=function(t,n=0,i){return e.debounce(t,((e,t)=>e?(e.push(t),e):[t]),n,void 0,!0,void 0,i)},e.latch=function(e,t=((e,t)=>e===t),n){let r,s=!0;return i(e,(e=>{const n=s||!t(e,r);return s=!1,r=e,n}),n)},e.split=function(t,n,i){return[e.filter(t,n,i),e.filter(t,(e=>!n(e)),i)]},e.buffer=function(e,t=!1,n=[],i){let r=n.slice(),s=e((e=>{r?r.push(e):a.fire(e)}));i&&i.add(s);const o=()=>{r?.forEach((e=>a.fire(e))),r=null},a=new d({onWillAddFirstListener(){s||(s=e((e=>a.fire(e))),i&&i.add(s))},onDidAddFirstListener(){r&&(t?setTimeout(o):o())},onDidRemoveLastListener(){s&&s.dispose(),s=null}});return i&&i.add(a),a.event},e.chain=function(e,t){return(n,i,r)=>{const s=t(new l);return e((function(e){const t=s.evaluate(e);t!==a&&n.call(i,t)}),void 0,r)}};const a=Symbol("HaltChainable");class l{constructor(){this.steps=[]}map(e){return this.steps.push(e),this}forEach(e){return this.steps.push((t=>(e(t),t))),this}filter(e){return this.steps.push((t=>e(t)?t:a)),this}reduce(e,t){let n=t;return this.steps.push((t=>(n=e(n,t),n))),this}latch(e=((e,t)=>e===t)){let t,n=!0;return this.steps.push((i=>{const r=n||!e(i,t);return n=!1,t=i,r?i:a})),this}evaluate(e){for(const t of this.steps)if((e=t(e))===a)break;return e}}e.fromNodeEventEmitter=function(e,t,n=(e=>e)){const i=(...e)=>r.fire(n(...e)),r=new d({onWillAddFirstListener:()=>e.on(t,i),onDidRemoveLastListener:()=>e.removeListener(t,i)});return r.event},e.fromDOMEventEmitter=function(e,t,n=(e=>e)){const i=(...e)=>r.fire(n(...e)),r=new d({onWillAddFirstListener:()=>e.addEventListener(t,i),onDidRemoveLastListener:()=>e.removeEventListener(t,i)});return r.event},e.toPromise=function(e){return new Promise((n=>t(e)(n)))},e.fromPromise=function(e){const t=new d;return e.then((e=>{t.fire(e)}),(()=>{t.fire(void 0)})).finally((()=>{t.dispose()})),t.event},e.runAndSubscribe=function(e,t){return t(void 0),e((e=>t(e)))},e.runAndSubscribeWithStore=function(e,t){let n=null;function i(e){n?.dispose(),n=new r.DisposableStore,t(e,n)}i(void 0);const s=e((e=>i(e)));return(0,r.toDisposable)((()=>{s.dispose(),n?.dispose()}))};class u{constructor(e,t){this._observable=e,this._counter=0,this._hasChanged=!1;const n={onWillAddFirstListener:()=>{e.addObserver(this)},onDidRemoveLastListener:()=>{e.removeObserver(this)}};this.emitter=new d(n),t&&t.add(this.emitter)}beginUpdate(e){this._counter++}handlePossibleChange(e){}handleChange(e,t){this._hasChanged=!0}endUpdate(e){this._counter--,0===this._counter&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}e.fromObservable=function(e,t){return new u(e,t).emitter.event},e.fromObservableLight=function(e){return t=>{let n=0,i=!1;const r={beginUpdate(){n++},endUpdate(){n--,0===n&&(e.reportChanges(),i&&(i=!1,t()))},handlePossibleChange(){},handleChange(){i=!0}};return e.addObserver(r),e.reportChanges(),{dispose(){e.removeObserver(r)}}}}}(a||(t.Event=a={}));class l{constructor(e){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${e}_${l._idPool++}`,l.all.add(this)}start(e){this._stopWatch=new o.StopWatch,this.listenerCount=e}stop(){if(this._stopWatch){const e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}}t.EventProfiling=l,l.all=new Set,l._idPool=0;class u{constructor(e,t=Math.random().toString(18).slice(2,5)){this.threshold=e,this.name=t,this._warnCountdown=0}dispose(){var e;null===(e=this._stacks)||void 0===e||e.clear()}check(e,t){const n=this.threshold;if(n<=0||t<n)return;this._stacks||(this._stacks=new Map);const i=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,i+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=.5*n;let e,i=0;for(const[t,n]of this._stacks)(!e||i<n)&&(e=t,i=n);console.warn(`[${this.name}] potential listener LEAK detected, having ${t} listeners already. MOST frequent listener (${i}):`),console.warn(e)}return()=>{const t=this._stacks.get(e.value)||0;this._stacks.set(e.value,t-1)}}}class c{static create(){var e;return new c(null!==(e=(new Error).stack)&&void 0!==e?e:"")}constructor(e){this.value=e}print(){console.warn(this.value.split("\n").slice(2).join("\n"))}}class h{constructor(e){this.value=e}}class d{constructor(e){var t,n,i,r,s;this._size=0,this._options=e,this._leakageMon=null!==(t=this._options)&&void 0!==t&&t.leakWarningThreshold?new u(null!==(i=null===(n=this._options)||void 0===n?void 0:n.leakWarningThreshold)&&void 0!==i?i:-1):void 0,this._perfMon=null!==(r=this._options)&&void 0!==r&&r._profName?new l(this._options._profName):void 0,this._deliveryQueue=null===(s=this._options)||void 0===s?void 0:s.deliveryQueue}dispose(){var e,t,n,i;this._disposed||(this._disposed=!0,(null===(e=this._deliveryQueue)||void 0===e?void 0:e.current)===this&&this._deliveryQueue.reset(),this._listeners&&(this._listeners=void 0,this._size=0),null===(n=null===(t=this._options)||void 0===t?void 0:t.onDidRemoveLastListener)||void 0===n||n.call(t),null===(i=this._leakageMon)||void 0===i||i.dispose())}get event(){var e;return null!==(e=this._event)&&void 0!==e||(this._event=(e,t,n)=>{var i,s,o,a,l;if(this._leakageMon&&this._size>3*this._leakageMon.threshold)return console.warn(`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far`),r.Disposable.None;if(this._disposed)return r.Disposable.None;t&&(e=e.bind(t));const u=new h(e);let d;this._leakageMon&&this._size>=Math.ceil(.2*this._leakageMon.threshold)&&(u.stack=c.create(),d=this._leakageMon.check(u.stack,this._size+1)),this._listeners?this._listeners instanceof h?(null!==(l=this._deliveryQueue)&&void 0!==l||(this._deliveryQueue=new f),this._listeners=[this._listeners,u]):this._listeners.push(u):(null===(s=null===(i=this._options)||void 0===i?void 0:i.onWillAddFirstListener)||void 0===s||s.call(i,this),this._listeners=u,null===(a=null===(o=this._options)||void 0===o?void 0:o.onDidAddFirstListener)||void 0===a||a.call(o,this)),this._size++;const g=(0,r.toDisposable)((()=>{d?.(),this._removeListener(u)}));return n instanceof r.DisposableStore?n.add(g):Array.isArray(n)&&n.push(g),g}),this._event}_removeListener(e){var t,n,i,r;if(null===(n=null===(t=this._options)||void 0===t?void 0:t.onWillRemoveListener)||void 0===n||n.call(t,this),!this._listeners)return;if(1===this._size)return this._listeners=void 0,null===(r=null===(i=this._options)||void 0===i?void 0:i.onDidRemoveLastListener)||void 0===r||r.call(i,this),void(this._size=0);const s=this._listeners,o=s.indexOf(e);if(-1===o)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,s[o]=void 0;const a=this._deliveryQueue.current===this;if(2*this._size<=s.length){let e=0;for(let t=0;t<s.length;t++)s[t]?s[e++]=s[t]:a&&(this._deliveryQueue.end--,e<this._deliveryQueue.i&&this._deliveryQueue.i--);s.length=e}}_deliver(e,t){var i;if(!e)return;const r=(null===(i=this._options)||void 0===i?void 0:i.onListenerError)||n.onUnexpectedError;if(r)try{e.value(t)}catch(e){r(e)}else e.value(t)}_deliverQueue(e){const t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){var t,n,i,r;if(!(null===(t=this._deliveryQueue)||void 0===t)&&t.current&&(this._deliverQueue(this._deliveryQueue),null===(n=this._perfMon)||void 0===n||n.stop()),null===(i=this._perfMon)||void 0===i||i.start(this._size),this._listeners)if(this._listeners instanceof h)this._deliver(this._listeners,e);else{const t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}null===(r=this._perfMon)||void 0===r||r.stop()}hasListeners(){return this._size>0}}t.Emitter=d,t.createEventDeliveryQueue=()=>new f;class f{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}}class g extends d{constructor(e){super(e),this._isPaused=0,this._eventQueue=new s.LinkedList,this._mergeFn=e?.merge}pause(){this._isPaused++}resume(){if(0!==this._isPaused&&0==--this._isPaused)if(this._mergeFn){if(this._eventQueue.size>0){const e=Array.from(this._eventQueue);this._eventQueue.clear(),super.fire(this._mergeFn(e))}}else for(;!this._isPaused&&0!==this._eventQueue.size;)super.fire(this._eventQueue.shift())}fire(e){this._size&&(0!==this._isPaused?this._eventQueue.push(e):super.fire(e))}}t.PauseableEmitter=g,t.DebounceEmitter=class extends g{constructor(e){var t;super(e),this._delay=null!==(t=e.delay)&&void 0!==t?t:100}fire(e){this._handle||(this.pause(),this._handle=setTimeout((()=>{this._handle=void 0,this.resume()}),this._delay)),super.fire(e)}},t.MicrotaskEmitter=class extends d{constructor(e){super(e),this._queuedEvents=[],this._mergeFn=e?.merge}fire(e){this.hasListeners()&&(this._queuedEvents.push(e),1===this._queuedEvents.length&&queueMicrotask((()=>{this._mergeFn?super.fire(this._mergeFn(this._queuedEvents)):this._queuedEvents.forEach((e=>super.fire(e))),this._queuedEvents=[]})))}},t.EventMultiplexer=class{constructor(){this.hasListeners=!1,this.events=[],this.emitter=new d({onWillAddFirstListener:()=>this.onFirstListenerAdd(),onDidRemoveLastListener:()=>this.onLastListenerRemove()})}get event(){return this.emitter.event}add(e){const t={event:e,listener:null};return this.events.push(t),this.hasListeners&&this.hook(t),(0,r.toDisposable)((0,i.createSingleCallFunction)((()=>{this.hasListeners&&this.unhook(t);const e=this.events.indexOf(t);this.events.splice(e,1)})))}onFirstListenerAdd(){this.hasListeners=!0,this.events.forEach((e=>this.hook(e)))}onLastListenerRemove(){this.hasListeners=!1,this.events.forEach((e=>this.unhook(e)))}hook(e){e.listener=e.event((e=>this.emitter.fire(e)))}unhook(e){e.listener&&e.listener.dispose(),e.listener=null}dispose(){this.emitter.dispose()}},t.EventBufferer=class{constructor(){this.buffers=[]}wrapEvent(e){return(t,n,i)=>e((e=>{const i=this.buffers[this.buffers.length-1];i?i.push((()=>t.call(n,e))):t.call(n,e)}),void 0,i)}bufferEvents(e){const t=[];this.buffers.push(t);const n=e();return this.buffers.pop(),t.forEach((e=>e())),n}},t.Relay=class{constructor(){this.listening=!1,this.inputEvent=a.None,this.inputEventListener=r.Disposable.None,this.emitter=new d({onDidAddFirstListener:()=>{this.listening=!0,this.inputEventListener=this.inputEvent(this.emitter.fire,this.emitter)},onDidRemoveLastListener:()=>{this.listening=!1,this.inputEventListener.dispose()}}),this.event=this.emitter.event}set input(e){this.inputEvent=e,this.listening&&(this.inputEventListener.dispose(),this.inputEventListener=e(this.emitter.fire,this.emitter))}dispose(){this.inputEventListener.dispose(),this.emitter.dispose()}}})),r(e[38],t([0,1,9]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CancellationTokenSource=t.CancellationToken=void 0;const i=Object.freeze((function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}));var r,s;(s=r||(t.CancellationToken=r={})).isCancellationToken=function(e){return e===s.None||e===s.Cancelled||e instanceof o||!(!e||"object"!=typeof e)&&"boolean"==typeof e.isCancellationRequested&&"function"==typeof e.onCancellationRequested},s.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:n.Event.None}),s.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:i});class o{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?i:(this._emitter||(this._emitter=new n.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}t.CancellationTokenSource=class{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new o),this._token}cancel(){this._token?this._token instanceof o&&this._token.cancel():this._token=r.Cancelled}dispose(e=!1){var t;e&&this.cancel(),null===(t=this._parentListener)||void 0===t||t.dispose(),this._token?this._token instanceof o&&this._token.dispose():this._token=r.None}}})),r(e[6],t([0,1,32,36]),(function(e,t,n,i){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.InvisibleCharacters=t.AmbiguousCharacters=t.noBreakWhitespace=t.getLeftDeleteOffset=t.singleLetterHash=t.containsUppercaseCharacter=t.startsWithUTF8BOM=t.UTF8_BOM_CHARACTER=t.isEmojiImprecise=t.isFullWidthCharacter=t.containsUnusualLineTerminators=t.UNUSUAL_LINE_TERMINATORS=t.isBasicASCII=t.containsRTL=t.getCharContainingOffset=t.prevCharLength=t.nextCharLength=t.GraphemeIterator=t.CodePointIterator=t.getNextCodePoint=t.computeCodePoint=t.isLowSurrogate=t.isHighSurrogate=t.commonSuffixLength=t.commonPrefixLength=t.startsWithIgnoreCase=t.equalsIgnoreCase=t.isUpperAsciiLetter=t.isLowerAsciiLetter=t.isAsciiDigit=t.compareSubstringIgnoreCase=t.compareIgnoreCase=t.compareSubstring=t.compare=t.lastNonWhitespaceIndex=t.getLeadingWhitespace=t.firstNonWhitespaceIndex=t.splitLines=t.regExpLeadsToEndlessLoop=t.createRegExp=t.stripWildcards=t.convertSimple2RegExpPattern=t.rtrim=t.ltrim=t.trim=t.escapeRegExpCharacters=t.escape=t.format=t.isFalsyOrWhitespace=void 0,t.isFalsyOrWhitespace=function(e){return!e||"string"!=typeof e||0===e.trim().length};const s=/{(\d+)}/g;function o(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function a(e,t){if(!e||!t)return e;const n=t.length;if(0===n||0===e.length)return e;let i=0;for(;e.indexOf(t,i)===i;)i+=n;return e.substring(i)}function l(e,t){if(!e||!t)return e;const n=t.length,i=e.length;if(0===n||0===i)return e;let r=i,s=-1;for(;s=e.lastIndexOf(t,r-1),-1!==s&&s+n===r;){if(0===s)return"";r=s}return e.substring(0,r)}function u(e,t,n=0,i=e.length,r=0,s=t.length){for(;n<i&&r<s;n++,r++){const i=e.charCodeAt(n),s=t.charCodeAt(r);if(i<s)return-1;if(i>s)return 1}const o=i-n,a=s-r;return o<a?-1:o>a?1:0}function c(e,t,n=0,i=e.length,r=0,s=t.length){for(;n<i&&r<s;n++,r++){let o=e.charCodeAt(n),a=t.charCodeAt(r);if(o===a)continue;if(o>=128||a>=128)return u(e.toLowerCase(),t.toLowerCase(),n,i,r,s);h(o)&&(o-=32),h(a)&&(a-=32);const l=o-a;if(0!==l)return l}const o=i-n,a=s-r;return o<a?-1:o>a?1:0}function h(e){return e>=97&&e<=122}function d(e){return 55296<=e&&e<=56319}function f(e){return 56320<=e&&e<=57343}function g(e,t){return t-56320+(e-55296<<10)+65536}function m(e,t,n){const i=e.charCodeAt(n);if(d(i)&&n+1<t){const t=e.charCodeAt(n+1);if(f(t))return g(i,t)}return i}t.format=function(e,...t){return 0===t.length?e:e.replace(s,(function(e,n){const i=parseInt(n,10);return isNaN(i)||i<0||i>=t.length?e:t[i]}))},t.escape=function(e){return e.replace(/[<>&]/g,(function(e){switch(e){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return e}}))},t.escapeRegExpCharacters=o,t.trim=function(e,t=" "){return l(a(e,t),t)},t.ltrim=a,t.rtrim=l,t.convertSimple2RegExpPattern=function(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")},t.stripWildcards=function(e){return e.replace(/\*/g,"")},t.createRegExp=function(e,t,n={}){if(!e)throw new Error("Cannot create regex from empty string");t||(e=o(e)),n.wholeWord&&(/\B/.test(e.charAt(0))||(e="\\b"+e),/\B/.test(e.charAt(e.length-1))||(e+="\\b"));let i="";return n.global&&(i+="g"),n.matchCase||(i+="i"),n.multiline&&(i+="m"),n.unicode&&(i+="u"),new RegExp(e,i)},t.regExpLeadsToEndlessLoop=function(e){return"^"!==e.source&&"^$"!==e.source&&"$"!==e.source&&"^\\s*$"!==e.source&&!(!e.exec("")||0!==e.lastIndex)},t.splitLines=function(e){return e.split(/\r\n|\r|\n/)},t.firstNonWhitespaceIndex=function(e){for(let t=0,n=e.length;t<n;t++){const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}return-1},t.getLeadingWhitespace=function(e,t=0,n=e.length){for(let i=t;i<n;i++){const n=e.charCodeAt(i);if(32!==n&&9!==n)return e.substring(t,i)}return e.substring(t,n)},t.lastNonWhitespaceIndex=function(e,t=e.length-1){for(let n=t;n>=0;n--){const t=e.charCodeAt(n);if(32!==t&&9!==t)return n}return-1},t.compare=function(e,t){return e<t?-1:e>t?1:0},t.compareSubstring=u,t.compareIgnoreCase=function(e,t){return c(e,t,0,e.length,0,t.length)},t.compareSubstringIgnoreCase=c,t.isAsciiDigit=function(e){return e>=48&&e<=57},t.isLowerAsciiLetter=h,t.isUpperAsciiLetter=function(e){return e>=65&&e<=90},t.equalsIgnoreCase=function(e,t){return e.length===t.length&&0===c(e,t)},t.startsWithIgnoreCase=function(e,t){const n=t.length;return!(t.length>e.length)&&0===c(e,t,0,n)},t.commonPrefixLength=function(e,t){const n=Math.min(e.length,t.length);let i;for(i=0;i<n;i++)if(e.charCodeAt(i)!==t.charCodeAt(i))return i;return n},t.commonSuffixLength=function(e,t){const n=Math.min(e.length,t.length);let i;const r=e.length-1,s=t.length-1;for(i=0;i<n;i++)if(e.charCodeAt(r-i)!==t.charCodeAt(s-i))return i;return n},t.isHighSurrogate=d,t.isLowSurrogate=f,t.computeCodePoint=g,t.getNextCodePoint=m;class p{get offset(){return this._offset}constructor(e,t=0){this._str=e,this._len=e.length,this._offset=t}setOffset(e){this._offset=e}prevCodePoint(){const e=function(e,t){const n=e.charCodeAt(t-1);if(f(n)&&t>1){const i=e.charCodeAt(t-2);if(d(i))return g(i,n)}return n}(this._str,this._offset);return this._offset-=e>=65536?2:1,e}nextCodePoint(){const e=m(this._str,this._len,this._offset);return this._offset+=e>=65536?2:1,e}eol(){return this._offset>=this._len}}t.CodePointIterator=p;class _{get offset(){return this._iterator.offset}constructor(e,t=0){this._iterator=new p(e,t)}nextGraphemeLength(){const e=S.getInstance(),t=this._iterator,n=t.offset;let i=e.getGraphemeBreakType(t.nextCodePoint());for(;!t.eol();){const n=t.offset,r=e.getGraphemeBreakType(t.nextCodePoint());if(E(i,r)){t.setOffset(n);break}i=r}return t.offset-n}prevGraphemeLength(){const e=S.getInstance(),t=this._iterator,n=t.offset;let i=e.getGraphemeBreakType(t.prevCodePoint());for(;t.offset>0;){const n=t.offset,r=e.getGraphemeBreakType(t.prevCodePoint());if(E(r,i)){t.setOffset(n);break}i=r}return n-t.offset}eol(){return this._iterator.eol()}}function b(e,t){return new _(e,t).nextGraphemeLength()}function v(e,t){return new _(e,t).prevGraphemeLength()}let y;t.GraphemeIterator=_,t.nextCharLength=b,t.prevCharLength=v,t.getCharContainingOffset=function(e,t){t>0&&f(e.charCodeAt(t))&&t--;const n=t+b(e,t);return[n-v(e,n),n]},t.containsRTL=function(e){return y||(y=/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDC7\uFDF0-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE35\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDD23\uDE80-\uDEA9\uDEAD-\uDF45\uDF51-\uDF81\uDF86-\uDFF6]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD4B-\uDFFF]|\uD83B[\uDC00-\uDEBB])/),y.test(e)};const C=/^[\t\n\r\x20-\x7E]*$/;function L(e){return e>=127462&&e<=127487||8986===e||8987===e||9200===e||9203===e||e>=9728&&e<=10175||11088===e||11093===e||e>=127744&&e<=128591||e>=128640&&e<=128764||e>=128992&&e<=129008||e>=129280&&e<=129535||e>=129648&&e<=129782}function E(e,t){return 0===e?5!==t&&7!==t:!(2===e&&3===t||4!==e&&2!==e&&3!==e&&4!==t&&2!==t&&3!==t&&(8===e&&(8===t||9===t||11===t||12===t)||(11===e||9===e)&&(9===t||10===t)||(12===e||10===e)&&10===t||5===t||13===t||7===t||1===e||13===e&&14===t||6===e&&6===t))}t.isBasicASCII=function(e){return C.test(e)},t.UNUSUAL_LINE_TERMINATORS=/[\u2028\u2029]/,t.containsUnusualLineTerminators=function(e){return t.UNUSUAL_LINE_TERMINATORS.test(e)},t.isFullWidthCharacter=function(e){return e>=11904&&e<=55215||e>=63744&&e<=64255||e>=65281&&e<=65374},t.isEmojiImprecise=L,t.UTF8_BOM_CHARACTER=String.fromCharCode(65279),t.startsWithUTF8BOM=function(e){return!!(e&&e.length>0&&65279===e.charCodeAt(0))},t.containsUppercaseCharacter=function(e,t=!1){return!!e&&(t&&(e=e.replace(/\\./g,"")),e.toLowerCase()!==e)},t.singleLetterHash=function(e){return(e%=52)<26?String.fromCharCode(97+e):String.fromCharCode(65+e-26)};class S{static getInstance(){return S._INSTANCE||(S._INSTANCE=new S),S._INSTANCE}constructor(){this._data=JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}getGraphemeBreakType(e){if(e<32)return 10===e?3:13===e?2:4;if(e<127)return 0;const t=this._data,n=t.length/3;let i=1;for(;i<=n;)if(e<t[3*i])i*=2;else{if(!(e>t[3*i+1]))return t[3*i+2];i=2*i+1}return 0}}function w(e){return 127995<=e&&e<=127999}S._INSTANCE=null,t.getLeftDeleteOffset=function(e,t){if(0===e)return 0;const n=function(e,t){const n=new p(t,e);let i=n.prevCodePoint();for(;w(i)||65039===i||8419===i;){if(0===n.offset)return;i=n.prevCodePoint()}if(!L(i))return;let r=n.offset;return r>0&&8205===n.prevCodePoint()&&(r=n.offset),r}(e,t);if(void 0!==n)return n;const i=new p(t,e);return i.prevCodePoint(),i.offset},t.noBreakWhitespace=" ";class R{static getInstance(e){return r.cache.get(Array.from(e))}static getLocales(){return r._locales.value}constructor(e){this.confusableDictionary=e}isAmbiguous(e){return this.confusableDictionary.has(e)}getPrimaryConfusable(e){return this.confusableDictionary.get(e)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}t.AmbiguousCharacters=R,r=R,R.ambiguousCharacterData=new i.Lazy((()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}'))),R.cache=new n.LRUCachedFunction((e=>{function t(e){const t=new Map;for(let n=0;n<e.length;n+=2)t.set(e[n],e[n+1]);return t}function n(e,t){if(!e)return t;const n=new Map;for(const[i,r]of e)t.has(i)&&n.set(i,r);return n}const i=r.ambiguousCharacterData.value;let s,o=e.filter((e=>!e.startsWith("_")&&e in i));0===o.length&&(o=["_default"]);for(const e of o)s=n(s,t(i[e]));const a=function(e,t){const n=new Map(e);for(const[e,i]of t)n.set(e,i);return n}(t(i._common),s);return new r(a)})),R._locales=new i.Lazy((()=>Object.keys(r.ambiguousCharacterData.value).filter((e=>!e.startsWith("_")))));class N{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(N.getRawData())),this._data}static isInvisibleCharacter(e){return N.getData().has(e)}static get codePoints(){return N.getData()}}t.InvisibleCharacters=N,N._data=void 0})),r(e[39],t([0,1,6]),(function(e,t,n){"use strict";function i(e,t){switch(typeof e){case"object":return null===e?r(349,t):Array.isArray(e)?function(e,t){return t=r(104579,t),e.reduce(((e,t)=>i(t,e)),t)}(e,t):function(e,t){return t=r(181387,t),Object.keys(e).sort().reduce(((t,n)=>(t=s(n,t),i(e[n],t))),t)}(e,t);case"string":return s(e,t);case"boolean":return function(e,t){return r(e?433:863,t)}(e,t);case"number":return r(e,t);case"undefined":return r(937,t);default:return r(617,t)}}function r(e,t){return(t<<5)-t+e|0}function s(e,t){t=r(149417,t);for(let n=0,i=e.length;n<i;n++)t=r(e.charCodeAt(n),t);return t}function o(e,t,n=32){const i=n-t;return(e<<t|(~((1<<i)-1)&e)>>>i)>>>0}function a(e,t=0,n=e.byteLength,i=0){for(let r=0;r<n;r++)e[t+r]=i}function l(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map((e=>e.toString(16).padStart(2,"0"))).join(""):function(e,t,n="0"){for(;e.length<t;)e=n+e;return e}((e>>>0).toString(16),t/4)}Object.defineProperty(t,"__esModule",{value:!0}),t.StringSHA1=t.toHexString=t.stringHash=t.numberHash=t.doHash=t.hash=void 0,t.hash=function(e){return i(e,0)},t.doHash=i,t.numberHash=r,t.stringHash=s,t.toHexString=l;class u{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(e){const t=e.length;if(0===t)return;const i=this._buff;let r,s,o=this._buffLen,a=this._leftoverHighSurrogate;for(0!==a?(r=a,s=-1,a=0):(r=e.charCodeAt(0),s=0);;){let l=r;if(n.isHighSurrogate(r)){if(!(s+1<t)){a=r;break}{const t=e.charCodeAt(s+1);n.isLowSurrogate(t)?(s++,l=n.computeCodePoint(r,t)):l=65533}}else n.isLowSurrogate(r)&&(l=65533);if(o=this._push(i,o,l),s++,!(s<t))break;r=e.charCodeAt(s)}this._buffLen=o,this._leftoverHighSurrogate=a}_push(e,t,n){return n<128?e[t++]=n:n<2048?(e[t++]=192|(1984&n)>>>6,e[t++]=128|(63&n)>>>0):n<65536?(e[t++]=224|(61440&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0):(e[t++]=240|(1835008&n)>>>18,e[t++]=128|(258048&n)>>>12,e[t++]=128|(4032&n)>>>6,e[t++]=128|(63&n)>>>0),t>=64&&(this._step(),t-=64,this._totalLen+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),t}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),l(this._h0)+l(this._h1)+l(this._h2)+l(this._h3)+l(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,a(this._buff,this._buffLen),this._buffLen>56&&(this._step(),a(this._buff));const e=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(e/4294967296),!1),this._buffDV.setUint32(60,e%4294967296,!1),this._step()}_step(){const e=u._bigBlock32,t=this._buffDV;for(let n=0;n<64;n+=4)e.setUint32(n,t.getUint32(n,!1),!1);for(let t=64;t<320;t+=4)e.setUint32(t,o(e.getUint32(t-12,!1)^e.getUint32(t-32,!1)^e.getUint32(t-56,!1)^e.getUint32(t-64,!1),1),!1);let n,i,r,s=this._h0,a=this._h1,l=this._h2,c=this._h3,h=this._h4;for(let t=0;t<80;t++)t<20?(n=a&l|~a&c,i=1518500249):t<40?(n=a^l^c,i=1859775393):t<60?(n=a&l|a&c|l&c,i=2400959708):(n=a^l^c,i=3395469782),r=o(s,5)+n+h+i+e.getUint32(4*t,!1)&4294967295,h=c,c=l,l=o(a,30),a=s,s=r;this._h0=this._h0+s&4294967295,this._h1=this._h1+a&4294967295,this._h2=this._h2+l&4294967295,this._h3=this._h3+c&4294967295,this._h4=this._h4+h&4294967295}}t.StringSHA1=u,u._bigBlock32=new DataView(new ArrayBuffer(320))})),r(e[24],t([0,1,34,39]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LcsDiff=t.stringDiff=t.StringDiffSequence=void 0;class r{constructor(e){this.source=e}getElements(){const e=this.source,t=new Int32Array(e.length);for(let n=0,i=e.length;n<i;n++)t[n]=e.charCodeAt(n);return t}}t.StringDiffSequence=r,t.stringDiff=function(e,t,n){return new l(new r(e),new r(t)).ComputeDiff(n).changes};class s{static Assert(e,t){if(!e)throw new Error(t)}}class o{static Copy(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}static Copy2(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}}class a{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new n.DiffChange(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class l{constructor(e,t,n=null){this.ContinueProcessingPredicate=n,this._originalSequence=e,this._modifiedSequence=t;const[i,r,s]=l._getElements(e),[o,a,u]=l._getElements(t);this._hasStrings=s&&u,this._originalStringElements=i,this._originalElementsOrHash=r,this._modifiedStringElements=o,this._modifiedElementsOrHash=a,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&"string"==typeof e[0]}static _getElements(e){const t=e.getElements();if(l._isStringArray(t)){const e=new Int32Array(t.length);for(let n=0,r=t.length;n<r;n++)e[n]=(0,i.stringHash)(t[n],0);return[t,e,!0]}return t instanceof Int32Array?[[],t,!1]:[[],new Int32Array(t),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._modifiedStringElements[t])}ElementsAreStrictEqual(e,t){return!!this.ElementsAreEqual(e,t)&&l._getStrictElement(this._originalSequence,e)===l._getStrictElement(this._modifiedSequence,t)}static _getStrictElement(e,t){return"function"==typeof e.getStrictElement?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._originalStringElements[t])}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._modifiedStringElements[e]===this._modifiedStringElements[t])}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,i,r){const s=[!1];let o=this.ComputeDiffRecursive(e,t,n,i,s);return r&&(o=this.PrettifyChanges(o)),{quitEarly:s[0],changes:o}}ComputeDiffRecursive(e,t,i,r,o){for(o[0]=!1;e<=t&&i<=r&&this.ElementsAreEqual(e,i);)e++,i++;for(;t>=e&&r>=i&&this.ElementsAreEqual(t,r);)t--,r--;if(e>t||i>r){let o;return i<=r?(s.Assert(e===t+1,"originalStart should only be one more than originalEnd"),o=[new n.DiffChange(e,0,i,r-i+1)]):e<=t?(s.Assert(i===r+1,"modifiedStart should only be one more than modifiedEnd"),o=[new n.DiffChange(e,t-e+1,i,0)]):(s.Assert(e===t+1,"originalStart should only be one more than originalEnd"),s.Assert(i===r+1,"modifiedStart should only be one more than modifiedEnd"),o=[]),o}const a=[0],l=[0],u=this.ComputeRecursionPoint(e,t,i,r,a,l,o),c=a[0],h=l[0];if(null!==u)return u;if(!o[0]){const s=this.ComputeDiffRecursive(e,c,i,h,o);let a=[];return a=o[0]?[new n.DiffChange(c+1,t-(c+1)+1,h+1,r-(h+1)+1)]:this.ComputeDiffRecursive(c+1,t,h+1,r,o),this.ConcatenateChanges(s,a)}return[new n.DiffChange(e,t-e+1,i,r-i+1)]}WALKTRACE(e,t,i,r,s,o,l,u,c,h,d,f,g,m,p,_,b,v){let y=null,C=null,L=new a,E=t,S=i,w=g[0]-_[0]-r,R=-1073741824,N=this.m_forwardHistory.length-1;do{const t=w+e;t===E||t<S&&c[t-1]<c[t+1]?(m=(d=c[t+1])-w-r,d<R&&L.MarkNextChange(),R=d,L.AddModifiedElement(d+1,m),w=t+1-e):(m=(d=c[t-1]+1)-w-r,d<R&&L.MarkNextChange(),R=d-1,L.AddOriginalElement(d,m+1),w=t-1-e),N>=0&&(e=(c=this.m_forwardHistory[N])[0],E=1,S=c.length-1)}while(--N>=-1);if(y=L.getReverseChanges(),v[0]){let e=g[0]+1,t=_[0]+1;if(null!==y&&y.length>0){const n=y[y.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}C=[new n.DiffChange(e,f-e+1,t,p-t+1)]}else{L=new a,E=o,S=l,w=g[0]-_[0]-u,R=1073741824,N=b?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const e=w+s;e===E||e<S&&h[e-1]>=h[e+1]?(m=(d=h[e+1]-1)-w-u,d>R&&L.MarkNextChange(),R=d+1,L.AddOriginalElement(d+1,m+1),w=e+1-s):(m=(d=h[e-1])-w-u,d>R&&L.MarkNextChange(),R=d,L.AddModifiedElement(d+1,m+1),w=e-1-s),N>=0&&(s=(h=this.m_reverseHistory[N])[0],E=1,S=h.length-1)}while(--N>=-1);C=L.getChanges()}return this.ConcatenateChanges(y,C)}ComputeRecursionPoint(e,t,i,r,s,a,l){let u=0,c=0,h=0,d=0,f=0,g=0;e--,i--,s[0]=0,a[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const m=t-e+(r-i),p=m+1,_=new Int32Array(p),b=new Int32Array(p),v=r-i,y=t-e,C=e-i,L=t-r,E=(y-v)%2==0;_[v]=e,b[y]=t,l[0]=!1;for(let S=1;S<=m/2+1;S++){let m=0,w=0;h=this.ClipDiagonalBound(v-S,S,v,p),d=this.ClipDiagonalBound(v+S,S,v,p);for(let e=h;e<=d;e+=2){u=e===h||e<d&&_[e-1]<_[e+1]?_[e+1]:_[e-1]+1,c=u-(e-v)-C;const n=u;for(;u<t&&c<r&&this.ElementsAreEqual(u+1,c+1);)u++,c++;if(_[e]=u,u+c>m+w&&(m=u,w=c),!E&&Math.abs(e-y)<=S-1&&u>=b[e])return s[0]=u,a[0]=c,n<=b[e]&&S<=1448?this.WALKTRACE(v,h,d,C,y,f,g,L,_,b,u,t,s,c,r,a,E,l):null}const R=(m-e+(w-i)-S)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(m,R))return l[0]=!0,s[0]=m,a[0]=w,R>0&&S<=1448?this.WALKTRACE(v,h,d,C,y,f,g,L,_,b,u,t,s,c,r,a,E,l):(e++,i++,[new n.DiffChange(e,t-e+1,i,r-i+1)]);f=this.ClipDiagonalBound(y-S,S,y,p),g=this.ClipDiagonalBound(y+S,S,y,p);for(let n=f;n<=g;n+=2){u=n===f||n<g&&b[n-1]>=b[n+1]?b[n+1]-1:b[n-1],c=u-(n-y)-L;const o=u;for(;u>e&&c>i&&this.ElementsAreEqual(u,c);)u--,c--;if(b[n]=u,E&&Math.abs(n-v)<=S&&u<=_[n])return s[0]=u,a[0]=c,o>=_[n]&&S<=1448?this.WALKTRACE(v,h,d,C,y,f,g,L,_,b,u,t,s,c,r,a,E,l):null}if(S<=1447){let e=new Int32Array(d-h+2);e[0]=v-h+1,o.Copy2(_,h,e,1,d-h+1),this.m_forwardHistory.push(e),e=new Int32Array(g-f+2),e[0]=y-f+1,o.Copy2(b,f,e,1,g-f+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(v,h,d,C,y,f,g,L,_,b,u,t,s,c,r,a,E,l)}PrettifyChanges(e){for(let t=0;t<e.length;t++){const n=e[t],i=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,r=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,s=n.originalLength>0,o=n.modifiedLength>0;for(;n.originalStart+n.originalLength<i&&n.modifiedStart+n.modifiedLength<r&&(!s||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!o||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){const e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}const a=[null];t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],a)&&(e[t]=a[0],e.splice(t+1,1),t--)}for(let t=e.length-1;t>=0;t--){const n=e[t];let i=0,r=0;if(t>0){const n=e[t-1];i=n.originalStart+n.originalLength,r=n.modifiedStart+n.modifiedLength}const s=n.originalLength>0,o=n.modifiedLength>0;let a=0,l=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){const t=n.originalStart-e,u=n.modifiedStart-e;if(t<i||u<r||s&&!this.OriginalElementsAreEqual(t,t+n.originalLength)||o&&!this.ModifiedElementsAreEqual(u,u+n.modifiedLength))break;const c=(t===i&&u===r?5:0)+this._boundaryScore(t,n.originalLength,u,n.modifiedLength);c>l&&(l=c,a=e)}n.originalStart-=a,n.modifiedStart-=a;const u=[null];t>0&&this.ChangesOverlap(e[t-1],e[t],u)&&(e[t-1]=u[0],e.splice(t,1),t++)}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){const n=e[t-1],i=e[t],r=i.originalStart-n.originalStart-n.originalLength,s=n.originalStart,o=i.originalStart+i.originalLength,a=o-s,l=n.modifiedStart,u=i.modifiedStart+i.modifiedLength,c=u-l;if(r<5&&a<20&&c<20){const e=this._findBetterContiguousSequence(s,a,l,c,r);if(e){const[t,s]=e;(t!==n.originalStart+n.originalLength||s!==n.modifiedStart+n.modifiedLength)&&(n.originalLength=t-n.originalStart,n.modifiedLength=s-n.modifiedStart,i.originalStart=t+r,i.modifiedStart=s+r,i.originalLength=o-i.originalStart,i.modifiedLength=u-i.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,i,r){if(t<r||i<r)return null;const s=e+t-r+1,o=n+i-r+1;let a=0,l=0,u=0;for(let t=e;t<s;t++)for(let e=n;e<o;e++){const n=this._contiguousSequenceScore(t,e,r);n>0&&n>a&&(a=n,l=t,u=e)}return a>0?[l,u]:null}_contiguousSequenceScore(e,t,n){let i=0;for(let r=0;r<n;r++){if(!this.ElementsAreEqual(e+r,t+r))return 0;i+=this._originalStringElements[e+r].length}return i}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,i){return(this._OriginalRegionIsBoundary(e,t)?1:0)+(this._ModifiedRegionIsBoundary(n,i)?1:0)}ConcatenateChanges(e,t){const n=[];if(0===e.length||0===t.length)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){const i=new Array(e.length+t.length-1);return o.Copy(e,0,i,0,e.length-1),i[e.length-1]=n[0],o.Copy(t,1,i,e.length,t.length-1),i}{const n=new Array(e.length+t.length);return o.Copy(e,0,n,0,e.length),o.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,i){if(s.Assert(e.originalStart<=t.originalStart,"Left change is not less than or equal to right change"),s.Assert(e.modifiedStart<=t.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){const r=e.originalStart;let s=e.originalLength;const o=e.modifiedStart;let a=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(s=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(a=t.modifiedStart+t.modifiedLength-e.modifiedStart),i[0]=new n.DiffChange(r,s,o,a),!0}return i[0]=null,!1}ClipDiagonalBound(e,t,n,i){if(e>=0&&e<i)return e;const r=t%2==0;return e<0?r===(n%2==0)?0:1:r===((i-n-1)%2==0)?i-1:i-2}}t.LcsDiff=l})),r(e[25],t([0,1]),(function(e,t){"use strict";function n(e){return"string"==typeof e}function i(e){return typeof e>"u"}function r(e){return i(e)||null===e}function s(e){return"function"==typeof e}function o(e,t){if(n(t)){if(typeof e!==t)throw new Error(`argument does not match constraint: typeof ${t}`)}else if(s(t)){try{if(e instanceof t)return}catch{}if(!r(e)&&e.constructor===t||1===t.length&&!0===t.call(void 0,e))return;throw new Error("argument does not match one of these constraints: arg instanceof constraint, arg.constructor === constraint, nor constraint(arg) === true")}}Object.defineProperty(t,"__esModule",{value:!0}),t.validateConstraint=t.validateConstraints=t.isFunction=t.assertIsDefined=t.assertType=t.isUndefinedOrNull=t.isDefined=t.isUndefined=t.isBoolean=t.isIterable=t.isNumber=t.isTypedArray=t.isObject=t.isString=void 0,t.isString=n,t.isObject=function(e){return!("object"!=typeof e||null===e||Array.isArray(e)||e instanceof RegExp||e instanceof Date)},t.isTypedArray=function(e){const t=Object.getPrototypeOf(Uint8Array);return"object"==typeof e&&e instanceof t},t.isNumber=function(e){return"number"==typeof e&&!isNaN(e)},t.isIterable=function(e){return!!e&&"function"==typeof e[Symbol.iterator]},t.isBoolean=function(e){return!0===e||!1===e},t.isUndefined=i,t.isDefined=function(e){return!r(e)},t.isUndefinedOrNull=r,t.assertType=function(e,t){if(!e)throw new Error(t?`Unexpected type, expected '${t}'`:"Unexpected type")},t.assertIsDefined=function(e){if(r(e))throw new Error("Assertion Failed: argument is undefined or null");return e},t.isFunction=s,t.validateConstraints=function(e,t){const n=Math.min(e.length,t.length);for(let i=0;i<n;i++)o(e[i],t[i])},t.validateConstraint=o})),r(e[40],t([0,1,25]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Codicon=t.getCodiconFontCharacters=void 0;const i=Object.create(null);function r(e,t){if((0,n.isString)(t)){const n=i[t];if(void 0===n)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return i[e]=t,{id:e}}t.getCodiconFontCharacters=function(){return i},t.Codicon={add:r("add",6e4),plus:r("plus",6e4),gistNew:r("gist-new",6e4),repoCreate:r("repo-create",6e4),lightbulb:r("lightbulb",60001),lightBulb:r("light-bulb",60001),repo:r("repo",60002),repoDelete:r("repo-delete",60002),gistFork:r("gist-fork",60003),repoForked:r("repo-forked",60003),gitPullRequest:r("git-pull-request",60004),gitPullRequestAbandoned:r("git-pull-request-abandoned",60004),recordKeys:r("record-keys",60005),keyboard:r("keyboard",60005),tag:r("tag",60006),tagAdd:r("tag-add",60006),tagRemove:r("tag-remove",60006),gitPullRequestLabel:r("git-pull-request-label",60006),person:r("person",60007),personFollow:r("person-follow",60007),personOutline:r("person-outline",60007),personFilled:r("person-filled",60007),gitBranch:r("git-branch",60008),gitBranchCreate:r("git-branch-create",60008),gitBranchDelete:r("git-branch-delete",60008),sourceControl:r("source-control",60008),mirror:r("mirror",60009),mirrorPublic:r("mirror-public",60009),star:r("star",60010),starAdd:r("star-add",60010),starDelete:r("star-delete",60010),starEmpty:r("star-empty",60010),comment:r("comment",60011),commentAdd:r("comment-add",60011),alert:r("alert",60012),warning:r("warning",60012),search:r("search",60013),searchSave:r("search-save",60013),logOut:r("log-out",60014),signOut:r("sign-out",60014),logIn:r("log-in",60015),signIn:r("sign-in",60015),eye:r("eye",60016),eyeUnwatch:r("eye-unwatch",60016),eyeWatch:r("eye-watch",60016),circleFilled:r("circle-filled",60017),primitiveDot:r("primitive-dot",60017),closeDirty:r("close-dirty",60017),debugBreakpoint:r("debug-breakpoint",60017),debugBreakpointDisabled:r("debug-breakpoint-disabled",60017),debugHint:r("debug-hint",60017),primitiveSquare:r("primitive-square",60018),edit:r("edit",60019),pencil:r("pencil",60019),info:r("info",60020),issueOpened:r("issue-opened",60020),gistPrivate:r("gist-private",60021),gitForkPrivate:r("git-fork-private",60021),lock:r("lock",60021),mirrorPrivate:r("mirror-private",60021),close:r("close",60022),removeClose:r("remove-close",60022),x:r("x",60022),repoSync:r("repo-sync",60023),sync:r("sync",60023),clone:r("clone",60024),desktopDownload:r("desktop-download",60024),beaker:r("beaker",60025),microscope:r("microscope",60025),vm:r("vm",60026),deviceDesktop:r("device-desktop",60026),file:r("file",60027),fileText:r("file-text",60027),more:r("more",60028),ellipsis:r("ellipsis",60028),kebabHorizontal:r("kebab-horizontal",60028),mailReply:r("mail-reply",60029),reply:r("reply",60029),organization:r("organization",60030),organizationFilled:r("organization-filled",60030),organizationOutline:r("organization-outline",60030),newFile:r("new-file",60031),fileAdd:r("file-add",60031),newFolder:r("new-folder",60032),fileDirectoryCreate:r("file-directory-create",60032),trash:r("trash",60033),trashcan:r("trashcan",60033),history:r("history",60034),clock:r("clock",60034),folder:r("folder",60035),fileDirectory:r("file-directory",60035),symbolFolder:r("symbol-folder",60035),logoGithub:r("logo-github",60036),markGithub:r("mark-github",60036),github:r("github",60036),terminal:r("terminal",60037),console:r("console",60037),repl:r("repl",60037),zap:r("zap",60038),symbolEvent:r("symbol-event",60038),error:r("error",60039),stop:r("stop",60039),variable:r("variable",60040),symbolVariable:r("symbol-variable",60040),array:r("array",60042),symbolArray:r("symbol-array",60042),symbolModule:r("symbol-module",60043),symbolPackage:r("symbol-package",60043),symbolNamespace:r("symbol-namespace",60043),symbolObject:r("symbol-object",60043),symbolMethod:r("symbol-method",60044),symbolFunction:r("symbol-function",60044),symbolConstructor:r("symbol-constructor",60044),symbolBoolean:r("symbol-boolean",60047),symbolNull:r("symbol-null",60047),symbolNumeric:r("symbol-numeric",60048),symbolNumber:r("symbol-number",60048),symbolStructure:r("symbol-structure",60049),symbolStruct:r("symbol-struct",60049),symbolParameter:r("symbol-parameter",60050),symbolTypeParameter:r("symbol-type-parameter",60050),symbolKey:r("symbol-key",60051),symbolText:r("symbol-text",60051),symbolReference:r("symbol-reference",60052),goToFile:r("go-to-file",60052),symbolEnum:r("symbol-enum",60053),symbolValue:r("symbol-value",60053),symbolRuler:r("symbol-ruler",60054),symbolUnit:r("symbol-unit",60054),activateBreakpoints:r("activate-breakpoints",60055),archive:r("archive",60056),arrowBoth:r("arrow-both",60057),arrowDown:r("arrow-down",60058),arrowLeft:r("arrow-left",60059),arrowRight:r("arrow-right",60060),arrowSmallDown:r("arrow-small-down",60061),arrowSmallLeft:r("arrow-small-left",60062),arrowSmallRight:r("arrow-small-right",60063),arrowSmallUp:r("arrow-small-up",60064),arrowUp:r("arrow-up",60065),bell:r("bell",60066),bold:r("bold",60067),book:r("book",60068),bookmark:r("bookmark",60069),debugBreakpointConditionalUnverified:r("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:r("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:r("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:r("debug-breakpoint-data-unverified",60072),debugBreakpointData:r("debug-breakpoint-data",60073),debugBreakpointDataDisabled:r("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:r("debug-breakpoint-log-unverified",60074),debugBreakpointLog:r("debug-breakpoint-log",60075),debugBreakpointLogDisabled:r("debug-breakpoint-log-disabled",60075),briefcase:r("briefcase",60076),broadcast:r("broadcast",60077),browser:r("browser",60078),bug:r("bug",60079),calendar:r("calendar",60080),caseSensitive:r("case-sensitive",60081),check:r("check",60082),checklist:r("checklist",60083),chevronDown:r("chevron-down",60084),dropDownButton:r("drop-down-button",60084),chevronLeft:r("chevron-left",60085),chevronRight:r("chevron-right",60086),chevronUp:r("chevron-up",60087),chromeClose:r("chrome-close",60088),chromeMaximize:r("chrome-maximize",60089),chromeMinimize:r("chrome-minimize",60090),chromeRestore:r("chrome-restore",60091),circle:r("circle",60092),circleOutline:r("circle-outline",60092),debugBreakpointUnverified:r("debug-breakpoint-unverified",60092),circleSlash:r("circle-slash",60093),circuitBoard:r("circuit-board",60094),clearAll:r("clear-all",60095),clippy:r("clippy",60096),closeAll:r("close-all",60097),cloudDownload:r("cloud-download",60098),cloudUpload:r("cloud-upload",60099),code:r("code",60100),collapseAll:r("collapse-all",60101),colorMode:r("color-mode",60102),commentDiscussion:r("comment-discussion",60103),compareChanges:r("compare-changes",60157),creditCard:r("credit-card",60105),dash:r("dash",60108),dashboard:r("dashboard",60109),database:r("database",60110),debugContinue:r("debug-continue",60111),debugDisconnect:r("debug-disconnect",60112),debugPause:r("debug-pause",60113),debugRestart:r("debug-restart",60114),debugStart:r("debug-start",60115),debugStepInto:r("debug-step-into",60116),debugStepOut:r("debug-step-out",60117),debugStepOver:r("debug-step-over",60118),debugStop:r("debug-stop",60119),debug:r("debug",60120),deviceCameraVideo:r("device-camera-video",60121),deviceCamera:r("device-camera",60122),deviceMobile:r("device-mobile",60123),diffAdded:r("diff-added",60124),diffIgnored:r("diff-ignored",60125),diffModified:r("diff-modified",60126),diffRemoved:r("diff-removed",60127),diffRenamed:r("diff-renamed",60128),diff:r("diff",60129),discard:r("discard",60130),editorLayout:r("editor-layout",60131),emptyWindow:r("empty-window",60132),exclude:r("exclude",60133),extensions:r("extensions",60134),eyeClosed:r("eye-closed",60135),fileBinary:r("file-binary",60136),fileCode:r("file-code",60137),fileMedia:r("file-media",60138),filePdf:r("file-pdf",60139),fileSubmodule:r("file-submodule",60140),fileSymlinkDirectory:r("file-symlink-directory",60141),fileSymlinkFile:r("file-symlink-file",60142),fileZip:r("file-zip",60143),files:r("files",60144),filter:r("filter",60145),flame:r("flame",60146),foldDown:r("fold-down",60147),foldUp:r("fold-up",60148),fold:r("fold",60149),folderActive:r("folder-active",60150),folderOpened:r("folder-opened",60151),gear:r("gear",60152),gift:r("gift",60153),gistSecret:r("gist-secret",60154),gist:r("gist",60155),gitCommit:r("git-commit",60156),gitCompare:r("git-compare",60157),gitMerge:r("git-merge",60158),githubAction:r("github-action",60159),githubAlt:r("github-alt",60160),globe:r("globe",60161),grabber:r("grabber",60162),graph:r("graph",60163),gripper:r("gripper",60164),heart:r("heart",60165),home:r("home",60166),horizontalRule:r("horizontal-rule",60167),hubot:r("hubot",60168),inbox:r("inbox",60169),issueClosed:r("issue-closed",60324),issueReopened:r("issue-reopened",60171),issues:r("issues",60172),italic:r("italic",60173),jersey:r("jersey",60174),json:r("json",60175),bracket:r("bracket",60175),kebabVertical:r("kebab-vertical",60176),key:r("key",60177),law:r("law",60178),lightbulbAutofix:r("lightbulb-autofix",60179),linkExternal:r("link-external",60180),link:r("link",60181),listOrdered:r("list-ordered",60182),listUnordered:r("list-unordered",60183),liveShare:r("live-share",60184),loading:r("loading",60185),location:r("location",60186),mailRead:r("mail-read",60187),mail:r("mail",60188),markdown:r("markdown",60189),megaphone:r("megaphone",60190),mention:r("mention",60191),milestone:r("milestone",60192),gitPullRequestMilestone:r("git-pull-request-milestone",60192),mortarBoard:r("mortar-board",60193),move:r("move",60194),multipleWindows:r("multiple-windows",60195),mute:r("mute",60196),noNewline:r("no-newline",60197),note:r("note",60198),octoface:r("octoface",60199),openPreview:r("open-preview",60200),package:r("package",60201),paintcan:r("paintcan",60202),pin:r("pin",60203),play:r("play",60204),run:r("run",60204),plug:r("plug",60205),preserveCase:r("preserve-case",60206),preview:r("preview",60207),project:r("project",60208),pulse:r("pulse",60209),question:r("question",60210),quote:r("quote",60211),radioTower:r("radio-tower",60212),reactions:r("reactions",60213),references:r("references",60214),refresh:r("refresh",60215),regex:r("regex",60216),remoteExplorer:r("remote-explorer",60217),remote:r("remote",60218),remove:r("remove",60219),replaceAll:r("replace-all",60220),replace:r("replace",60221),repoClone:r("repo-clone",60222),repoForcePush:r("repo-force-push",60223),repoPull:r("repo-pull",60224),repoPush:r("repo-push",60225),report:r("report",60226),requestChanges:r("request-changes",60227),rocket:r("rocket",60228),rootFolderOpened:r("root-folder-opened",60229),rootFolder:r("root-folder",60230),rss:r("rss",60231),ruby:r("ruby",60232),saveAll:r("save-all",60233),saveAs:r("save-as",60234),save:r("save",60235),screenFull:r("screen-full",60236),screenNormal:r("screen-normal",60237),searchStop:r("search-stop",60238),server:r("server",60240),settingsGear:r("settings-gear",60241),settings:r("settings",60242),shield:r("shield",60243),smiley:r("smiley",60244),sortPrecedence:r("sort-precedence",60245),splitHorizontal:r("split-horizontal",60246),splitVertical:r("split-vertical",60247),squirrel:r("squirrel",60248),starFull:r("star-full",60249),starHalf:r("star-half",60250),symbolClass:r("symbol-class",60251),symbolColor:r("symbol-color",60252),symbolCustomColor:r("symbol-customcolor",60252),symbolConstant:r("symbol-constant",60253),symbolEnumMember:r("symbol-enum-member",60254),symbolField:r("symbol-field",60255),symbolFile:r("symbol-file",60256),symbolInterface:r("symbol-interface",60257),symbolKeyword:r("symbol-keyword",60258),symbolMisc:r("symbol-misc",60259),symbolOperator:r("symbol-operator",60260),symbolProperty:r("symbol-property",60261),wrench:r("wrench",60261),wrenchSubaction:r("wrench-subaction",60261),symbolSnippet:r("symbol-snippet",60262),tasklist:r("tasklist",60263),telescope:r("telescope",60264),textSize:r("text-size",60265),threeBars:r("three-bars",60266),thumbsdown:r("thumbsdown",60267),thumbsup:r("thumbsup",60268),tools:r("tools",60269),triangleDown:r("triangle-down",60270),triangleLeft:r("triangle-left",60271),triangleRight:r("triangle-right",60272),triangleUp:r("triangle-up",60273),twitter:r("twitter",60274),unfold:r("unfold",60275),unlock:r("unlock",60276),unmute:r("unmute",60277),unverified:r("unverified",60278),verified:r("verified",60279),versions:r("versions",60280),vmActive:r("vm-active",60281),vmOutline:r("vm-outline",60282),vmRunning:r("vm-running",60283),watch:r("watch",60284),whitespace:r("whitespace",60285),wholeWord:r("whole-word",60286),window:r("window",60287),wordWrap:r("word-wrap",60288),zoomIn:r("zoom-in",60289),zoomOut:r("zoom-out",60290),listFilter:r("list-filter",60291),listFlat:r("list-flat",60292),listSelection:r("list-selection",60293),selection:r("selection",60293),listTree:r("list-tree",60294),debugBreakpointFunctionUnverified:r("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:r("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:r("debug-breakpoint-function-disabled",60296),debugStackframeActive:r("debug-stackframe-active",60297),circleSmallFilled:r("circle-small-filled",60298),debugStackframeDot:r("debug-stackframe-dot",60298),debugStackframe:r("debug-stackframe",60299),debugStackframeFocused:r("debug-stackframe-focused",60299),debugBreakpointUnsupported:r("debug-breakpoint-unsupported",60300),symbolString:r("symbol-string",60301),debugReverseContinue:r("debug-reverse-continue",60302),debugStepBack:r("debug-step-back",60303),debugRestartFrame:r("debug-restart-frame",60304),callIncoming:r("call-incoming",60306),callOutgoing:r("call-outgoing",60307),menu:r("menu",60308),expandAll:r("expand-all",60309),feedback:r("feedback",60310),gitPullRequestReviewer:r("git-pull-request-reviewer",60310),groupByRefType:r("group-by-ref-type",60311),ungroupByRefType:r("ungroup-by-ref-type",60312),account:r("account",60313),gitPullRequestAssignee:r("git-pull-request-assignee",60313),bellDot:r("bell-dot",60314),debugConsole:r("debug-console",60315),library:r("library",60316),output:r("output",60317),runAll:r("run-all",60318),syncIgnored:r("sync-ignored",60319),pinned:r("pinned",60320),githubInverted:r("github-inverted",60321),debugAlt:r("debug-alt",60305),serverProcess:r("server-process",60322),serverEnvironment:r("server-environment",60323),pass:r("pass",60324),stopCircle:r("stop-circle",60325),playCircle:r("play-circle",60326),record:r("record",60327),debugAltSmall:r("debug-alt-small",60328),vmConnect:r("vm-connect",60329),cloud:r("cloud",60330),merge:r("merge",60331),exportIcon:r("export",60332),graphLeft:r("graph-left",60333),magnet:r("magnet",60334),notebook:r("notebook",60335),redo:r("redo",60336),checkAll:r("check-all",60337),pinnedDirty:r("pinned-dirty",60338),passFilled:r("pass-filled",60339),circleLargeFilled:r("circle-large-filled",60340),circleLarge:r("circle-large",60341),circleLargeOutline:r("circle-large-outline",60341),combine:r("combine",60342),gather:r("gather",60342),table:r("table",60343),variableGroup:r("variable-group",60344),typeHierarchy:r("type-hierarchy",60345),typeHierarchySub:r("type-hierarchy-sub",60346),typeHierarchySuper:r("type-hierarchy-super",60347),gitPullRequestCreate:r("git-pull-request-create",60348),runAbove:r("run-above",60349),runBelow:r("run-below",60350),notebookTemplate:r("notebook-template",60351),debugRerun:r("debug-rerun",60352),workspaceTrusted:r("workspace-trusted",60353),workspaceUntrusted:r("workspace-untrusted",60354),workspaceUnspecified:r("workspace-unspecified",60355),terminalCmd:r("terminal-cmd",60356),terminalDebian:r("terminal-debian",60357),terminalLinux:r("terminal-linux",60358),terminalPowershell:r("terminal-powershell",60359),terminalTmux:r("terminal-tmux",60360),terminalUbuntu:r("terminal-ubuntu",60361),terminalBash:r("terminal-bash",60362),arrowSwap:r("arrow-swap",60363),copy:r("copy",60364),personAdd:r("person-add",60365),filterFilled:r("filter-filled",60366),wand:r("wand",60367),debugLineByLine:r("debug-line-by-line",60368),inspect:r("inspect",60369),layers:r("layers",60370),layersDot:r("layers-dot",60371),layersActive:r("layers-active",60372),compass:r("compass",60373),compassDot:r("compass-dot",60374),compassActive:r("compass-active",60375),azure:r("azure",60376),issueDraft:r("issue-draft",60377),gitPullRequestClosed:r("git-pull-request-closed",60378),gitPullRequestDraft:r("git-pull-request-draft",60379),debugAll:r("debug-all",60380),debugCoverage:r("debug-coverage",60381),runErrors:r("run-errors",60382),folderLibrary:r("folder-library",60383),debugContinueSmall:r("debug-continue-small",60384),beakerStop:r("beaker-stop",60385),graphLine:r("graph-line",60386),graphScatter:r("graph-scatter",60387),pieChart:r("pie-chart",60388),bracketDot:r("bracket-dot",60389),bracketError:r("bracket-error",60390),lockSmall:r("lock-small",60391),azureDevops:r("azure-devops",60392),verifiedFilled:r("verified-filled",60393),newLine:r("newline",60394),layout:r("layout",60395),layoutActivitybarLeft:r("layout-activitybar-left",60396),layoutActivitybarRight:r("layout-activitybar-right",60397),layoutPanelLeft:r("layout-panel-left",60398),layoutPanelCenter:r("layout-panel-center",60399),layoutPanelJustify:r("layout-panel-justify",60400),layoutPanelRight:r("layout-panel-right",60401),layoutPanel:r("layout-panel",60402),layoutSidebarLeft:r("layout-sidebar-left",60403),layoutSidebarRight:r("layout-sidebar-right",60404),layoutStatusbar:r("layout-statusbar",60405),layoutMenubar:r("layout-menubar",60406),layoutCentered:r("layout-centered",60407),layoutSidebarRightOff:r("layout-sidebar-right-off",60416),layoutPanelOff:r("layout-panel-off",60417),layoutSidebarLeftOff:r("layout-sidebar-left-off",60418),target:r("target",60408),indent:r("indent",60409),recordSmall:r("record-small",60410),errorSmall:r("error-small",60411),arrowCircleDown:r("arrow-circle-down",60412),arrowCircleLeft:r("arrow-circle-left",60413),arrowCircleRight:r("arrow-circle-right",60414),arrowCircleUp:r("arrow-circle-up",60415),heartFilled:r("heart-filled",60420),map:r("map",60421),mapFilled:r("map-filled",60422),circleSmall:r("circle-small",60423),bellSlash:r("bell-slash",60424),bellSlashDot:r("bell-slash-dot",60425),commentUnresolved:r("comment-unresolved",60426),gitPullRequestGoToChanges:r("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:r("git-pull-request-new-changes",60428),searchFuzzy:r("search-fuzzy",60429),commentDraft:r("comment-draft",60430),send:r("send",60431),sparkle:r("sparkle",60432),insert:r("insert",60433),mic:r("mic",60434),dialogError:r("dialog-error","error"),dialogWarning:r("dialog-warning","warning"),dialogInfo:r("dialog-info","info"),dialogClose:r("dialog-close","close"),treeItemExpanded:r("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:r("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:r("tree-filter-on-type-off","list-selection"),treeFilterClear:r("tree-filter-clear","close"),treeItemLoading:r("tree-item-loading","loading"),menuSelection:r("menu-selection","check"),menuSubmenu:r("menu-submenu","chevron-right"),menuBarMore:r("menubar-more","more"),scrollbarButtonLeft:r("scrollbar-button-left","triangle-left"),scrollbarButtonRight:r("scrollbar-button-right","triangle-right"),scrollbarButtonUp:r("scrollbar-button-up","triangle-up"),scrollbarButtonDown:r("scrollbar-button-down","triangle-down"),toolBarMore:r("toolbar-more","more"),quickInputBack:r("quick-input-back","arrow-left")}})),r(e[14],t([0,1,25]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createProxyObject=t.getAllMethodNames=t.getAllPropertyNames=t.equals=t.mixin=t.cloneAndChange=t.deepFreeze=t.deepClone=void 0,t.deepClone=function e(t){if(!t||"object"!=typeof t||t instanceof RegExp)return t;const n=Array.isArray(t)?[]:{};return Object.entries(t).forEach((([t,i])=>{n[t]=i&&"object"==typeof i?e(i):i})),n},t.deepFreeze=function(e){if(!e||"object"!=typeof e)return e;const t=[e];for(;t.length>0;){const e=t.shift();Object.freeze(e);for(const r in e)if(i.call(e,r)){const i=e[r];"object"==typeof i&&!Object.isFrozen(i)&&!(0,n.isTypedArray)(i)&&t.push(i)}}return e};const i=Object.prototype.hasOwnProperty;function r(e,t,s){if((0,n.isUndefinedOrNull)(e))return e;const o=t(e);if(typeof o<"u")return o;if(Array.isArray(e)){const n=[];for(const i of e)n.push(r(i,t,s));return n}if((0,n.isObject)(e)){if(s.has(e))throw new Error("Cannot clone recursive data-structure");s.add(e);const n={};for(const o in e)i.call(e,o)&&(n[o]=r(e[o],t,s));return s.delete(e),n}return e}function s(e){let t=[];for(;Object.prototype!==e;)t=t.concat(Object.getOwnPropertyNames(e)),e=Object.getPrototypeOf(e);return t}t.cloneAndChange=function(e,t){return r(e,t,new Set)},t.mixin=function e(t,i,r=!0){return(0,n.isObject)(t)?((0,n.isObject)(i)&&Object.keys(i).forEach((s=>{s in t?r&&((0,n.isObject)(t[s])&&(0,n.isObject)(i[s])?e(t[s],i[s],r):t[s]=i[s]):t[s]=i[s]})),t):i},t.equals=function e(t,n){if(t===n)return!0;if(null==t||null==n||typeof t!=typeof n||"object"!=typeof t||Array.isArray(t)!==Array.isArray(n))return!1;let i,r;if(Array.isArray(t)){if(t.length!==n.length)return!1;for(i=0;i<t.length;i++)if(!e(t[i],n[i]))return!1}else{const s=[];for(r in t)s.push(r);s.sort();const o=[];for(r in n)o.push(r);if(o.sort(),!e(s,o))return!1;for(i=0;i<s.length;i++)if(!e(t[s[i]],n[s[i]]))return!1}return!0},t.getAllPropertyNames=s,t.getAllMethodNames=function(e){const t=[];for(const n of s(e))"function"==typeof e[n]&&t.push(n);return t},t.createProxyObject=function(e,t){const n=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},i={};for(const t of e)i[t]=n(t);return i}})),r(e[26],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toUint32=t.toUint8=void 0,t.toUint8=function(e){return e<0?0:e>255?255:0|e},t.toUint32=function(e){return e<0?0:e>4294967295?4294967295:0|e}})),r(e[27],t([0,1,26]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CharacterSet=t.CharacterClassifier=void 0;class i{constructor(e){const t=(0,n.toUint8)(e);this._defaultValue=t,this._asciiMap=i._createAsciiMap(t),this._map=new Map}static _createAsciiMap(e){const t=new Uint8Array(256);return t.fill(e),t}set(e,t){const i=(0,n.toUint8)(t);e>=0&&e<256?this._asciiMap[e]=i:this._map.set(e,i)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}}t.CharacterClassifier=i,t.CharacterSet=class{constructor(){this._actual=new i(0)}add(e){this._actual.set(e,1)}has(e){return 1===this._actual.get(e)}clear(){return this._actual.clear()}}})),r(e[3],t([0,1,5]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OffsetRangeSet=t.OffsetRange=void 0;class i{static addRange(e,t){let n=0;for(;n<t.length&&t[n].endExclusive<e.start;)n++;let r=n;for(;r<t.length&&t[r].start<=e.endExclusive;)r++;if(n===r)t.splice(n,0,e);else{const s=Math.min(e.start,t[n].start),o=Math.max(e.endExclusive,t[r-1].endExclusive);t.splice(n,r-n,new i(s,o))}}static tryCreate(e,t){if(!(e>t))return new i(e,t)}static ofLength(e){return new i(0,e)}constructor(e,t){if(this.start=e,this.endExclusive=t,e>t)throw new n.BugIndicatingError(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(e){return new i(this.start+e,this.endExclusive+e)}deltaStart(e){return new i(this.start+e,this.endExclusive)}deltaEnd(e){return new i(this.start,this.endExclusive+e)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}equals(e){return this.start===e.start&&this.endExclusive===e.endExclusive}containsRange(e){return this.start<=e.start&&e.endExclusive<=this.endExclusive}contains(e){return this.start<=e&&e<this.endExclusive}join(e){return new i(Math.min(this.start,e.start),Math.max(this.endExclusive,e.endExclusive))}intersect(e){const t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);if(t<=n)return new i(t,n)}slice(e){return e.slice(this.start,this.endExclusive)}clip(e){if(this.isEmpty)throw new n.BugIndicatingError(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,e))}clipCyclic(e){if(this.isEmpty)throw new n.BugIndicatingError(`Invalid clipping range: ${this.toString()}`);return e<this.start?this.endExclusive-(this.start-e)%this.length:e>=this.endExclusive?this.start+(e-this.start)%this.length:e}forEach(e){for(let t=this.start;t<this.endExclusive;t++)e(t)}}t.OffsetRange=i;class r{constructor(){this._sortedRanges=[]}addRange(e){let t=0;for(;t<this._sortedRanges.length&&this._sortedRanges[t].endExclusive<e.start;)t++;let n=t;for(;n<this._sortedRanges.length&&this._sortedRanges[n].start<=e.endExclusive;)n++;if(t===n)this._sortedRanges.splice(t,0,e);else{const r=Math.min(e.start,this._sortedRanges[t].start),s=Math.max(e.endExclusive,this._sortedRanges[n-1].endExclusive);this._sortedRanges.splice(t,n-t,new i(r,s))}}toString(){return this._sortedRanges.map((e=>e.toString())).join(", ")}intersectsStrict(e){let t=0;for(;t<this._sortedRanges.length&&this._sortedRanges[t].endExclusive<=e.start;)t++;return t<this._sortedRanges.length&&this._sortedRanges[t].start<e.endExclusive}intersectWithRange(e){const t=new r;for(const n of this._sortedRanges){const i=n.intersect(e);i&&t.addRange(i)}return t}intersectWithRangeLength(e){return this.intersectWithRange(e).length}get length(){return this._sortedRanges.reduce(((e,t)=>e+t.length),0)}}t.OffsetRangeSet=r})),r(e[4],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Position=void 0;class n{constructor(e,t){this.lineNumber=e,this.column=t}with(e=this.lineNumber,t=this.column){return e===this.lineNumber&&t===this.column?this:new n(e,t)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(e){return n.equals(this,e)}static equals(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(e){return n.isBefore(this,e)}static isBefore(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column}isBeforeOrEqual(e){return n.isBeforeOrEqual(this,e)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column}static compare(e,t){const n=0|e.lineNumber,i=0|t.lineNumber;return n===i?(0|e.column)-(0|t.column):n-i}clone(){return new n(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(e){return new n(e.lineNumber,e.column)}static isIPosition(e){return e&&"number"==typeof e.lineNumber&&"number"==typeof e.column}}t.Position=n})),r(e[2],t([0,1,4]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Range=void 0;class i{constructor(e,t,n,i){e>n||e===n&&t>i?(this.startLineNumber=n,this.startColumn=i,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=i)}isEmpty(){return i.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(e){return i.containsPosition(this,e)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber||t.lineNumber===e.startLineNumber&&t.column<e.startColumn||t.lineNumber===e.endLineNumber&&t.column>e.endColumn)}static strictContainsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber||t.lineNumber===e.startLineNumber&&t.column<=e.startColumn||t.lineNumber===e.endLineNumber&&t.column>=e.endColumn)}containsRange(e){return i.containsRange(this,e)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber||t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber||t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn||t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)}strictContainsRange(e){return i.strictContainsRange(this,e)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber||t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber||t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn||t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)}plusRange(e){return i.plusRange(this,e)}static plusRange(e,t){let n,r,s,o;return t.startLineNumber<e.startLineNumber?(n=t.startLineNumber,r=t.startColumn):t.startLineNumber===e.startLineNumber?(n=t.startLineNumber,r=Math.min(t.startColumn,e.startColumn)):(n=e.startLineNumber,r=e.startColumn),t.endLineNumber>e.endLineNumber?(s=t.endLineNumber,o=t.endColumn):t.endLineNumber===e.endLineNumber?(s=t.endLineNumber,o=Math.max(t.endColumn,e.endColumn)):(s=e.endLineNumber,o=e.endColumn),new i(n,r,s,o)}intersectRanges(e){return i.intersectRanges(this,e)}static intersectRanges(e,t){let n=e.startLineNumber,r=e.startColumn,s=e.endLineNumber,o=e.endColumn;const a=t.startLineNumber,l=t.startColumn,u=t.endLineNumber,c=t.endColumn;return n<a?(n=a,r=l):n===a&&(r=Math.max(r,l)),s>u?(s=u,o=c):s===u&&(o=Math.min(o,c)),n>s||n===s&&r>o?null:new i(n,r,s,o)}equalsRange(e){return i.equalsRange(this,e)}static equalsRange(e,t){return!e&&!t||!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return i.getEndPosition(this)}static getEndPosition(e){return new n.Position(e.endLineNumber,e.endColumn)}getStartPosition(){return i.getStartPosition(this)}static getStartPosition(e){return new n.Position(e.startLineNumber,e.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(e,t){return new i(this.startLineNumber,this.startColumn,e,t)}setStartPosition(e,t){return new i(e,t,this.endLineNumber,this.endColumn)}collapseToStart(){return i.collapseToStart(this)}static collapseToStart(e){return new i(e.startLineNumber,e.startColumn,e.startLineNumber,e.startColumn)}collapseToEnd(){return i.collapseToEnd(this)}static collapseToEnd(e){return new i(e.endLineNumber,e.endColumn,e.endLineNumber,e.endColumn)}delta(e){return new i(this.startLineNumber+e,this.startColumn,this.endLineNumber+e,this.endColumn)}static fromPositions(e,t=e){return new i(e.lineNumber,e.column,t.lineNumber,t.column)}static lift(e){return e?new i(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):null}static isIRange(e){return e&&"number"==typeof e.startLineNumber&&"number"==typeof e.startColumn&&"number"==typeof e.endLineNumber&&"number"==typeof e.endColumn}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn||t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn||t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){const n=0|e.startLineNumber,i=0|t.startLineNumber;if(n===i){const n=0|e.startColumn,i=0|t.startColumn;if(n===i){const n=0|e.endLineNumber,i=0|t.endLineNumber;return n===i?(0|e.endColumn)-(0|t.endColumn):n-i}return n-i}return n-i}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}toJSON(){return this}}t.Range=i})),r(e[10],t([0,1,5,3,2,11]),(function(e,t,n,i,r,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LineRangeSet=t.LineRange=void 0;class o{static fromRange(e){return new o(e.startLineNumber,e.endLineNumber)}static joinMany(e){if(0===e.length)return[];let t=new a(e[0].slice());for(let n=1;n<e.length;n++)t=t.getUnion(new a(e[n].slice()));return t.ranges}static ofLength(e,t){return new o(e,e+t)}static deserialize(e){return new o(e[0],e[1])}constructor(e,t){if(e>t)throw new n.BugIndicatingError(`startLineNumber ${e} cannot be after endLineNumberExclusive ${t}`);this.startLineNumber=e,this.endLineNumberExclusive=t}contains(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(e){return new o(this.startLineNumber+e,this.endLineNumberExclusive+e)}deltaLength(e){return new o(this.startLineNumber,this.endLineNumberExclusive+e)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(e){return new o(Math.min(this.startLineNumber,e.startLineNumber),Math.max(this.endLineNumberExclusive,e.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(e){const t=Math.max(this.startLineNumber,e.startLineNumber),n=Math.min(this.endLineNumberExclusive,e.endLineNumberExclusive);if(t<=n)return new o(t,n)}intersectsStrict(e){return this.startLineNumber<e.endLineNumberExclusive&&e.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive}equals(e){return this.startLineNumber===e.startLineNumber&&this.endLineNumberExclusive===e.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new r.Range(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new r.Range(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(e){const t=[];for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t.push(e(n));return t}forEach(e){for(let t=this.startLineNumber;t<this.endLineNumberExclusive;t++)e(t)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}toOffsetRange(){return new i.OffsetRange(this.startLineNumber-1,this.endLineNumberExclusive-1)}}t.LineRange=o;class a{constructor(e=[]){this._normalizedRanges=e}get ranges(){return this._normalizedRanges}addRange(e){if(0===e.length)return;const t=(0,s.findFirstIdxMonotonousOrArrLen)(this._normalizedRanges,(t=>t.endLineNumberExclusive>=e.startLineNumber)),n=(0,s.findLastIdxMonotonous)(this._normalizedRanges,(t=>t.startLineNumber<=e.endLineNumberExclusive))+1;if(t===n)this._normalizedRanges.splice(t,0,e);else if(t===n-1){const n=this._normalizedRanges[t];this._normalizedRanges[t]=n.join(e)}else{const i=this._normalizedRanges[t].join(this._normalizedRanges[n-1]).join(e);this._normalizedRanges.splice(t,n-t,i)}}contains(e){const t=(0,s.findLastMonotonous)(this._normalizedRanges,(t=>t.startLineNumber<=e));return!!t&&t.endLineNumberExclusive>e}getUnion(e){if(0===this._normalizedRanges.length)return e;if(0===e._normalizedRanges.length)return this;const t=[];let n=0,i=0,r=null;for(;n<this._normalizedRanges.length||i<e._normalizedRanges.length;){let s=null;if(n<this._normalizedRanges.length&&i<e._normalizedRanges.length){const t=this._normalizedRanges[n],r=e._normalizedRanges[i];t.startLineNumber<r.startLineNumber?(s=t,n++):(s=r,i++)}else n<this._normalizedRanges.length?(s=this._normalizedRanges[n],n++):(s=e._normalizedRanges[i],i++);null===r?r=s:r.endLineNumberExclusive>=s.startLineNumber?r=new o(r.startLineNumber,Math.max(r.endLineNumberExclusive,s.endLineNumberExclusive)):(t.push(r),r=s)}return null!==r&&t.push(r),new a(t)}subtractFrom(e){const t=(0,s.findFirstIdxMonotonousOrArrLen)(this._normalizedRanges,(t=>t.endLineNumberExclusive>=e.startLineNumber)),n=(0,s.findLastIdxMonotonous)(this._normalizedRanges,(t=>t.startLineNumber<=e.endLineNumberExclusive))+1;if(t===n)return new a([e]);const i=[];let r=e.startLineNumber;for(let e=t;e<n;e++){const t=this._normalizedRanges[e];t.startLineNumber>r&&i.push(new o(r,t.startLineNumber)),r=t.endLineNumberExclusive}return r<e.endLineNumberExclusive&&i.push(new o(r,e.endLineNumberExclusive)),new a(i)}toString(){return this._normalizedRanges.map((e=>e.toString())).join(", ")}getIntersection(e){const t=[];let n=0,i=0;for(;n<this._normalizedRanges.length&&i<e._normalizedRanges.length;){const r=this._normalizedRanges[n],s=e._normalizedRanges[i],o=r.intersect(s);o&&!o.isEmpty&&t.push(o),r.endLineNumberExclusive<s.endLineNumberExclusive?n++:i++}return new a(t)}getWithDelta(e){return new a(this._normalizedRanges.map((t=>t.delta(e))))}}t.LineRangeSet=a})),r(e[41],t([0,1,4,2]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Selection=void 0;class r extends i.Range{constructor(e,t,n,i){super(e,t,n,i),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=i}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(e){return r.selectionsEqual(this,e)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(e,t){return 0===this.getDirection()?new r(this.startLineNumber,this.startColumn,e,t):new r(e,t,this.startLineNumber,this.startColumn)}getPosition(){return new n.Position(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new n.Position(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(e,t){return 0===this.getDirection()?new r(e,t,this.endLineNumber,this.endColumn):new r(this.endLineNumber,this.endColumn,e,t)}static fromPositions(e,t=e){return new r(e.lineNumber,e.column,t.lineNumber,t.column)}static fromRange(e,t){return 0===t?new r(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):new r(e.endLineNumber,e.endColumn,e.startLineNumber,e.startColumn)}static liftSelection(e){return new r(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,i=e.length;n<i;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&"number"==typeof e.selectionStartLineNumber&&"number"==typeof e.selectionStartColumn&&"number"==typeof e.positionLineNumber&&"number"==typeof e.positionColumn}static createWithDirection(e,t,n,i,s){return 0===s?new r(e,t,n,i):new r(n,i,e,t)}}t.Selection=r})),r(e[42],t([0,1,27]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getMapForWordSeparators=t.WordCharacterClassifier=void 0;class i extends n.CharacterClassifier{constructor(e){super(0);for(let t=0,n=e.length;t<n;t++)this.set(e.charCodeAt(t),2);this.set(32,1),this.set(9,1)}}t.WordCharacterClassifier=i,t.getMapForWordSeparators=function(e){const t={};return e=>(t.hasOwnProperty(e)||(t[e]=(e=>new i(e))(e)),t[e])}()})),r(e[28],t([0,1,21,22]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getWordAtText=t.ensureValidWordDefinition=t.DEFAULT_WORD_REGEXP=t.USUAL_WORD_SEPARATORS=void 0,t.USUAL_WORD_SEPARATORS="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?",t.DEFAULT_WORD_REGEXP=function(e=""){let n="(-?\\d*\\.\\d\\w*)|([^";for(const i of t.USUAL_WORD_SEPARATORS)e.indexOf(i)>=0||(n+="\\"+i);return n+="\\s]+)",new RegExp(n,"g")}(),t.ensureValidWordDefinition=function(e){let n=t.DEFAULT_WORD_REGEXP;if(e&&e instanceof RegExp)if(e.global)n=e;else{let t="g";e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),n=new RegExp(e.source,t)}return n.lastIndex=0,n};const r=new i.LinkedList;function s(e,t,n,i){let r;for(;r=e.exec(t);){const t=r.index||0;if(t<=n&&e.lastIndex>=n)return r;if(i>0&&t>i)return null}return null}r.unshift({maxLen:1e3,windowSize:15,timeBudget:150}),t.getWordAtText=function e(t,i,o,a,l){if(l||(l=n.Iterable.first(r)),o.length>l.maxLen){let n=t-l.maxLen/2;return n<0?n=0:a+=n,e(t,i,o=o.substring(n,t+l.maxLen/2),a,l)}const u=Date.now(),c=t-1-a;let h=-1,d=null;for(let e=1;!(Date.now()-u>=l.timeBudget);e++){const t=c-l.windowSize*e;i.lastIndex=Math.max(0,t);const n=s(i,o,c,h);if(!n&&d||(d=n,t<=0))break;h=t}if(d){const e={word:d[0],startColumn:a+1+d.index,endColumn:a+1+d.index+d[0].length};return i.lastIndex=0,e}return null}})),r(e[8],t([0,1,7,5,3]),(function(e,t,n,i,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DateTimeout=t.InfiniteTimeout=t.OffsetPair=t.SequenceDiff=t.DiffAlgorithmResult=void 0;class s{static trivial(e,t){return new s([new o(r.OffsetRange.ofLength(e.length),r.OffsetRange.ofLength(t.length))],!1)}static trivialTimedOut(e,t){return new s([new o(r.OffsetRange.ofLength(e.length),r.OffsetRange.ofLength(t.length))],!0)}constructor(e,t){this.diffs=e,this.hitTimeout=t}}t.DiffAlgorithmResult=s;class o{static invert(e,t){const i=[];return(0,n.forEachAdjacent)(e,((e,n)=>{i.push(o.fromOffsetPairs(e?e.getEndExclusives():a.zero,n?n.getStarts():new a(t,(e?e.seq2Range.endExclusive-e.seq1Range.endExclusive:0)+t)))})),i}static fromOffsetPairs(e,t){return new o(new r.OffsetRange(e.offset1,t.offset1),new r.OffsetRange(e.offset2,t.offset2))}constructor(e,t){this.seq1Range=e,this.seq2Range=t}swap(){return new o(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(e){return new o(this.seq1Range.join(e.seq1Range),this.seq2Range.join(e.seq2Range))}delta(e){return 0===e?this:new o(this.seq1Range.delta(e),this.seq2Range.delta(e))}deltaStart(e){return 0===e?this:new o(this.seq1Range.deltaStart(e),this.seq2Range.deltaStart(e))}deltaEnd(e){return 0===e?this:new o(this.seq1Range.deltaEnd(e),this.seq2Range.deltaEnd(e))}intersect(e){const t=this.seq1Range.intersect(e.seq1Range),n=this.seq2Range.intersect(e.seq2Range);if(t&&n)return new o(t,n)}getStarts(){return new a(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new a(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}}t.SequenceDiff=o;class a{constructor(e,t){this.offset1=e,this.offset2=t}toString(){return`${this.offset1} <-> ${this.offset2}`}}t.OffsetPair=a,a.zero=new a(0,0),a.max=new a(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER);class l{isValid(){return!0}}t.InfiniteTimeout=l,l.instance=new l,t.DateTimeout=class{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new i.BugIndicatingError("timeout must be positive")}isValid(){return Date.now()-this.startTime<this.timeout||!this.valid||(this.valid=!1),this.valid}}})),r(e[29],t([0,1,3,8]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MyersDiffAlgorithm=void 0,t.MyersDiffAlgorithm=class{compute(e,t,a=i.InfiniteTimeout.instance){if(0===e.length||0===t.length)return i.DiffAlgorithmResult.trivial(e,t);const l=e,u=t;function c(e,t){for(;e<l.length&&t<u.length&&l.getElement(e)===u.getElement(t);)e++,t++;return e}let h=0;const d=new s;d.set(0,c(0,0));const f=new o;f.set(0,0===d.get(0)?null:new r(null,0,0,d.get(0)));let g=0;e:for(;;){if(h++,!a.isValid())return i.DiffAlgorithmResult.trivialTimedOut(l,u);const e=-Math.min(h,u.length+h%2),t=Math.min(h,l.length+h%2);for(g=e;g<=t;g+=2){let n=0;const i=g===t?-1:d.get(g+1),s=g===e?-1:d.get(g-1)+1;n++;const o=Math.min(Math.max(i,s),l.length),a=o-g;if(n++,o>l.length||a>u.length)continue;const h=c(o,a);d.set(g,h);const m=o===i?f.get(g+1):f.get(g-1);if(f.set(g,h!==o?new r(m,o,a,h-o):m),d.get(g)===l.length&&d.get(g)-g===u.length)break e}}let m=f.get(g);const p=[];let _=l.length,b=u.length;for(;;){const e=m?m.x+m.length:0,t=m?m.y+m.length:0;if((e!==_||t!==b)&&p.push(new i.SequenceDiff(new n.OffsetRange(e,_),new n.OffsetRange(t,b))),!m)break;_=m.x,b=m.y,m=m.prev}return p.reverse(),new i.DiffAlgorithmResult(p,!1)}};class r{constructor(e,t,n,i){this.prev=e,this.x=t,this.y=n,this.length=i}}class s{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){if(e<0){if((e=-e-1)>=this.negativeArr.length){const e=this.negativeArr;this.negativeArr=new Int32Array(2*e.length),this.negativeArr.set(e)}this.negativeArr[e]=t}else{if(e>=this.positiveArr.length){const e=this.positiveArr;this.positiveArr=new Int32Array(2*e.length),this.positiveArr.set(e)}this.positiveArr[e]=t}}}class o{constructor(){this.positiveArr=[],this.negativeArr=[]}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){e<0?(e=-e-1,this.negativeArr[e]=t):this.positiveArr[e]=t}}})),r(e[43],t([0,1,7,3,8]),(function(e,t,n,i,r){"use strict";function s(e,t,n,i,r){let s=1;for(;e.seq1Range.start-s>=i.start&&e.seq2Range.start-s>=r.start&&n.isStronglyEqual(e.seq2Range.start-s,e.seq2Range.endExclusive-s)&&s<100;)s++;s--;let o=0;for(;e.seq1Range.start+o<i.endExclusive&&e.seq2Range.endExclusive+o<r.endExclusive&&n.isStronglyEqual(e.seq2Range.start+o,e.seq2Range.endExclusive+o)&&o<100;)o++;if(0===s&&0===o)return e;let a=0,l=-1;for(let i=-s;i<=o;i++){const r=e.seq2Range.start+i,s=e.seq2Range.endExclusive+i,o=e.seq1Range.start+i,u=t.getBoundaryScore(o)+n.getBoundaryScore(r)+n.getBoundaryScore(s);u>l&&(l=u,a=i)}return e.delta(a)}Object.defineProperty(t,"__esModule",{value:!0}),t.removeVeryShortMatchingTextBetweenLongDiffs=t.removeVeryShortMatchingLinesBetweenDiffs=t.extendDiffsToEntireWordIfAppropriate=t.removeShortMatches=t.optimizeSequenceDiffs=void 0,t.optimizeSequenceDiffs=function(e,t,n){let o=n;return o=function(e,t,n){if(0===n.length)return n;const s=[];s.push(n[0]);for(let o=1;o<n.length;o++){const a=s[s.length-1];let l=n[o];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){const n=l.seq1Range.start-a.seq1Range.endExclusive;let o;for(o=1;o<=n&&e.getElement(l.seq1Range.start-o)===e.getElement(l.seq1Range.endExclusive-o)&&t.getElement(l.seq2Range.start-o)===t.getElement(l.seq2Range.endExclusive-o);o++);if(o--,o===n){s[s.length-1]=new r.SequenceDiff(new i.OffsetRange(a.seq1Range.start,l.seq1Range.endExclusive-n),new i.OffsetRange(a.seq2Range.start,l.seq2Range.endExclusive-n));continue}l=l.delta(-o)}s.push(l)}const o=[];for(let n=0;n<s.length-1;n++){const a=s[n+1];let l=s[n];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){const o=a.seq1Range.start-l.seq1Range.endExclusive;let u;for(u=0;u<o&&e.isStronglyEqual(l.seq1Range.start+u,l.seq1Range.endExclusive+u)&&t.isStronglyEqual(l.seq2Range.start+u,l.seq2Range.endExclusive+u);u++);if(u===o){s[n+1]=new r.SequenceDiff(new i.OffsetRange(l.seq1Range.start+o,a.seq1Range.endExclusive),new i.OffsetRange(l.seq2Range.start+o,a.seq2Range.endExclusive));continue}u>0&&(l=l.delta(u))}o.push(l)}return s.length>0&&o.push(s[s.length-1]),o}(e,t,o),o=function(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){const o=r>0?n[r-1]:void 0,a=n[r],l=r+1<n.length?n[r+1]:void 0,u=new i.OffsetRange(o?o.seq1Range.start+1:0,l?l.seq1Range.endExclusive-1:e.length),c=new i.OffsetRange(o?o.seq2Range.start+1:0,l?l.seq2Range.endExclusive-1:t.length);a.seq1Range.isEmpty?n[r]=s(a,e,t,u,c):a.seq2Range.isEmpty&&(n[r]=s(a.swap(),t,e,c,u).swap())}return n}(e,t,o),o},t.removeShortMatches=function(e,t,n){const i=[];for(const e of n){const t=i[i.length-1];t&&(e.seq1Range.start-t.seq1Range.endExclusive<=2||e.seq2Range.start-t.seq2Range.endExclusive<=2)?i[i.length-1]=new r.SequenceDiff(t.seq1Range.join(e.seq1Range),t.seq2Range.join(e.seq2Range)):i.push(e)}return i},t.extendDiffsToEntireWordIfAppropriate=function(e,t,n){const s=[];let o;function a(){if(!o)return;const e=o.s1Range.length-o.deleted;o.s2Range.length,o.added,Math.max(o.deleted,o.added)+(o.count-1)>e&&s.push(new r.SequenceDiff(o.s1Range,o.s2Range)),o=void 0}for(const r of n){let n=function(e,t){var n,s,l,u;if(!o||!o.s1Range.containsRange(e)||!o.s2Range.containsRange(t))if(!o||o.s1Range.endExclusive<e.start&&o.s2Range.endExclusive<t.start)a(),o={added:0,deleted:0,count:0,s1Range:e,s2Range:t};else{const r=i.OffsetRange.tryCreate(o.s1Range.endExclusive,e.start),a=i.OffsetRange.tryCreate(o.s2Range.endExclusive,t.start);o.deleted+=null!==(n=r?.length)&&void 0!==n?n:0,o.added+=null!==(s=a?.length)&&void 0!==s?s:0,o.s1Range=o.s1Range.join(e),o.s2Range=o.s2Range.join(t)}const c=e.intersect(r.seq1Range),h=t.intersect(r.seq2Range);o.count++,o.deleted+=null!==(l=c?.length)&&void 0!==l?l:0,o.added+=null!==(u=h?.length)&&void 0!==u?u:0};const s=e.findWordContaining(r.seq1Range.start-1),l=t.findWordContaining(r.seq2Range.start-1),u=e.findWordContaining(r.seq1Range.endExclusive),c=t.findWordContaining(r.seq2Range.endExclusive);s&&u&&l&&c&&s.equals(u)&&l.equals(c)?n(s,l):(s&&l&&n(s,l),u&&c&&n(u,c))}return a(),function(e,t){const n=[];for(;e.length>0||t.length>0;){const i=e[0],r=t[0];let s;s=i&&(!r||i.seq1Range.start<r.seq1Range.start)?e.shift():t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=s.seq1Range.start?n[n.length-1]=n[n.length-1].join(s):n.push(s)}return n}(n,s)},t.removeVeryShortMatchingLinesBetweenDiffs=function(e,t,n){let r=n;if(0===r.length)return r;let s,o=0;do{s=!1;const t=[r[0]];for(let n=1;n<r.length;n++){let o=function(t,n){const r=new i.OffsetRange(l.seq1Range.endExclusive,a.seq1Range.start);return e.getText(r).replace(/\s/g,"").length<=4&&(t.seq1Range.length+t.seq2Range.length>5||n.seq1Range.length+n.seq2Range.length>5)};const a=r[n],l=t[t.length-1];o(l,a)?(s=!0,t[t.length-1]=t[t.length-1].join(a)):t.push(a)}r=t}while(o++<10&&s);return r},t.removeVeryShortMatchingTextBetweenLongDiffs=function(e,t,s){let o=s;if(0===o.length)return o;let a,l=0;do{a=!1;const n=[o[0]];for(let r=1;r<o.length;r++){let s=function(n,r){const s=new i.OffsetRange(u.seq1Range.endExclusive,l.seq1Range.start);if(e.countLinesIn(s)>5||s.length>500)return!1;const o=e.getText(s).trim();if(o.length>20||o.split(/\r\n|\r|\n/).length>1)return!1;const a=e.countLinesIn(n.seq1Range),c=n.seq1Range.length,h=t.countLinesIn(n.seq2Range),d=n.seq2Range.length,f=e.countLinesIn(r.seq1Range),g=r.seq1Range.length,m=t.countLinesIn(r.seq2Range),p=r.seq2Range.length;function _(e){return Math.min(e,130)}return Math.pow(Math.pow(_(40*a+c),1.5)+Math.pow(_(40*h+d),1.5),1.5)+Math.pow(Math.pow(_(40*f+g),1.5)+Math.pow(_(40*m+p),1.5),1.5)>1.3*Math.pow(Math.pow(130,1.5),1.5)};const l=o[r],u=n[n.length-1];s(u,l)?(a=!0,n[n.length-1]=n[n.length-1].join(l)):n.push(l)}o=n}while(l++<10&&a);const u=[];return(0,n.forEachWithNeighbors)(o,((t,n,s)=>{let o=n;function a(e){return e.length>0&&e.trim().length<=3&&n.seq1Range.length+n.seq2Range.length>100}const l=e.extendToFullLines(n.seq1Range),c=e.getText(new i.OffsetRange(l.start,n.seq1Range.start));a(c)&&(o=o.deltaStart(-c.length));const h=e.getText(new i.OffsetRange(n.seq1Range.endExclusive,l.endExclusive));a(h)&&(o=o.deltaEnd(h.length));const d=r.SequenceDiff.fromOffsetPairs(t?t.getEndExclusives():r.OffsetPair.zero,s?s.getStarts():r.OffsetPair.max),f=o.intersect(d);u.push(f)})),u}})),r(e[44],t([0,1]),(function(e,t){"use strict";function n(e){let t=0;for(;t<e.length&&(32===e.charCodeAt(t)||9===e.charCodeAt(t));)t++;return t}Object.defineProperty(t,"__esModule",{value:!0}),t.LineSequence=void 0,t.LineSequence=class{constructor(e,t){this.trimmedHash=e,this.lines=t}getElement(e){return this.trimmedHash[e]}get length(){return this.trimmedHash.length}getBoundaryScore(e){return 1e3-((0===e?0:n(this.lines[e-1]))+(e===this.lines.length?0:n(this.lines[e])))}getText(e){return this.lines.slice(e.start,e.endExclusive).join("\n")}isStronglyEqual(e,t){return this.lines[e]===this.lines[t]}}})),r(e[15],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LineRangeFragment=t.isSpace=t.Array2D=void 0,t.Array2D=class{constructor(e,t){this.width=e,this.height=t,this.array=[],this.array=new Array(e*t)}get(e,t){return this.array[e+t*this.width]}set(e,t,n){this.array[e+t*this.width]=n}},t.isSpace=function(e){return 32===e||9===e};class n{static getKey(e){let t=this.chrKeys.get(e);return void 0===t&&(t=this.chrKeys.size,this.chrKeys.set(e,t)),t}constructor(e,t,i){this.range=e,this.lines=t,this.source=i,this.histogram=[];let r=0;for(let i=e.startLineNumber-1;i<e.endLineNumberExclusive-1;i++){const e=t[i];for(let t=0;t<e.length;t++){r++;const i=e[t],s=n.getKey(i);this.histogram[s]=(this.histogram[s]||0)+1}r++;const s=n.getKey("\n");this.histogram[s]=(this.histogram[s]||0)+1}this.totalCount=r}computeSimilarity(e){var t,n;let i=0;const r=Math.max(this.histogram.length,e.histogram.length);for(let s=0;s<r;s++)i+=Math.abs((null!==(t=this.histogram[s])&&void 0!==t?t:0)-(null!==(n=e.histogram[s])&&void 0!==n?n:0));return 1-i/(this.totalCount+e.totalCount)}}t.LineRangeFragment=n,n.chrKeys=new Map})),r(e[45],t([0,1,3,8,15]),(function(e,t,n,i,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DynamicProgrammingDiffing=void 0,t.DynamicProgrammingDiffing=class{compute(e,t,s=i.InfiniteTimeout.instance,o){if(0===e.length||0===t.length)return i.DiffAlgorithmResult.trivial(e,t);const a=new r.Array2D(e.length,t.length),l=new r.Array2D(e.length,t.length),u=new r.Array2D(e.length,t.length);for(let n=0;n<e.length;n++)for(let r=0;r<t.length;r++){if(!s.isValid())return i.DiffAlgorithmResult.trivialTimedOut(e,t);const c=0===n?0:a.get(n-1,r),h=0===r?0:a.get(n,r-1);let d;e.getElement(n)===t.getElement(r)?(d=0===n||0===r?0:a.get(n-1,r-1),n>0&&r>0&&3===l.get(n-1,r-1)&&(d+=u.get(n-1,r-1)),d+=o?o(n,r):1):d=-1;const f=Math.max(c,h,d);if(f===d){const e=n>0&&r>0?u.get(n-1,r-1):0;u.set(n,r,e+1),l.set(n,r,3)}else f===c?(u.set(n,r,0),l.set(n,r,1)):f===h&&(u.set(n,r,0),l.set(n,r,2));a.set(n,r,f)}const c=[];let h=e.length,d=t.length;function f(e,t){(e+1!==h||t+1!==d)&&c.push(new i.SequenceDiff(new n.OffsetRange(e+1,h),new n.OffsetRange(t+1,d))),h=e,d=t}let g=e.length-1,m=t.length-1;for(;g>=0&&m>=0;)3===l.get(g,m)?(f(g,m),g--,m--):1===l.get(g,m)?g--:m--;return f(-1,-1),c.reverse(),new i.DiffAlgorithmResult(c,!1)}}})),r(e[30],t([0,1,11,3,4,2,15]),(function(e,t,n,i,r,s,o){"use strict";function a(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}Object.defineProperty(t,"__esModule",{value:!0}),t.LinesSliceCharSequence=void 0,t.LinesSliceCharSequence=class{constructor(e,t,n){this.lines=e,this.considerWhitespaceChanges=n,this.elements=[],this.firstCharOffsetByLine=[],this.additionalOffsetByLine=[];let r=!1;t.start>0&&t.endExclusive>=e.length&&(t=new i.OffsetRange(t.start-1,t.endExclusive),r=!0),this.lineRange=t,this.firstCharOffsetByLine[0]=0;for(let t=this.lineRange.start;t<this.lineRange.endExclusive;t++){let i=e[t],s=0;if(r)s=i.length,i="",r=!1;else if(!n){const e=i.trimStart();s=i.length-e.length,i=e.trimEnd()}this.additionalOffsetByLine.push(s);for(let e=0;e<i.length;e++)this.elements.push(i.charCodeAt(e));t<e.length-1&&(this.elements.push("\n".charCodeAt(0)),this.firstCharOffsetByLine[t-this.lineRange.start+1]=this.elements.length)}this.additionalOffsetByLine.push(0)}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new i.OffsetRange(0,this.length))}getText(e){return this.elements.slice(e.start,e.endExclusive).map((e=>String.fromCharCode(e))).join("")}getElement(e){return this.elements[e]}get length(){return this.elements.length}getBoundaryScore(e){const t=c(e>0?this.elements[e-1]:-1),n=c(e<this.elements.length?this.elements[e]:-1);if(6===t&&7===n)return 0;let i=0;return t!==n&&(i+=10,0===t&&1===n&&(i+=1)),i+=u(t),i+=u(n),i}translateOffset(e){if(this.lineRange.isEmpty)return new r.Position(this.lineRange.start+1,1);const t=(0,n.findLastIdxMonotonous)(this.firstCharOffsetByLine,(t=>t<=e));return new r.Position(this.lineRange.start+t+1,e-this.firstCharOffsetByLine[t]+this.additionalOffsetByLine[t]+1)}translateRange(e){return s.Range.fromPositions(this.translateOffset(e.start),this.translateOffset(e.endExclusive))}findWordContaining(e){if(e<0||e>=this.elements.length||!a(this.elements[e]))return;let t=e;for(;t>0&&a(this.elements[t-1]);)t--;let n=e;for(;n<this.elements.length&&a(this.elements[n]);)n++;return new i.OffsetRange(t,n)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,t){return this.elements[e]===this.elements[t]}extendToFullLines(e){var t,r;const s=null!==(t=(0,n.findLastMonotonous)(this.firstCharOffsetByLine,(t=>t<=e.start)))&&void 0!==t?t:0,o=null!==(r=(0,n.findFirstMonotonous)(this.firstCharOffsetByLine,(t=>e.endExclusive<=t)))&&void 0!==r?r:this.elements.length;return new i.OffsetRange(s,o)}};const l={0:0,1:0,2:0,3:10,4:2,5:3,6:10,7:10};function u(e){return l[e]}function c(e){return 10===e?7:13===e?6:(0,o.isSpace)(e)?5:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:-1===e?3:4}})),r(e[31],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MovedText=t.LinesDiff=void 0,t.LinesDiff=class{constructor(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}},t.MovedText=class{constructor(e,t){this.lineRangeMapping=e,this.changes=t}}})),r(e[16],t([0,1,10]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RangeMapping=t.DetailedLineRangeMapping=t.LineRangeMapping=void 0;class i{static inverse(e,t,i){const s=[];let o=1,a=1;for(const t of e){const e=new r(new n.LineRange(o,t.original.startLineNumber),new n.LineRange(a,t.modified.startLineNumber),void 0);e.modified.isEmpty||s.push(e),o=t.original.endLineNumberExclusive,a=t.modified.endLineNumberExclusive}const l=new r(new n.LineRange(o,t+1),new n.LineRange(a,i+1),void 0);return l.modified.isEmpty||s.push(l),s}constructor(e,t){this.original=e,this.modified=t}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new i(this.modified,this.original)}join(e){return new i(this.original.join(e.original),this.modified.join(e.modified))}}t.LineRangeMapping=i;class r extends i{constructor(e,t,n){super(e,t),this.innerChanges=n}flip(){var e;return new r(this.modified,this.original,null===(e=this.innerChanges)||void 0===e?void 0:e.map((e=>e.flip())))}}t.DetailedLineRangeMapping=r;class s{constructor(e,t){this.originalRange=e,this.modifiedRange=t}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new s(this.modifiedRange,this.originalRange)}}t.RangeMapping=s})),r(e[46],t([0,1,8,16,7,11,37,10,3,30,15,29]),(function(e,t,n,i,r,s,o,a,l,u,c,h){"use strict";function d(e,t,i){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;const r=(new h.MyersDiffAlgorithm).compute(new u.LinesSliceCharSequence([e],new l.OffsetRange(0,1),!1),new u.LinesSliceCharSequence([t],new l.OffsetRange(0,1),!1),i);let s=0;const o=n.SequenceDiff.invert(r.diffs,e.length);for(const t of o)t.seq1Range.forEach((t=>{(0,c.isSpace)(e.charCodeAt(t))||s++}));const a=function(t){let n=0;for(let i=0;i<e.length;i++)(0,c.isSpace)(t.charCodeAt(i))||n++;return n}(e.length>t.length?e:t);return s/a>.6&&a>10}Object.defineProperty(t,"__esModule",{value:!0}),t.computeMovedLines=void 0,t.computeMovedLines=function(e,t,n,l,u,h){let{moves:f,excludedChanges:g}=function(e,t,n,r){const s=[],o=e.filter((e=>e.modified.isEmpty&&e.original.length>=3)).map((e=>new c.LineRangeFragment(e.original,t,e))),a=new Set(e.filter((e=>e.original.isEmpty&&e.modified.length>=3)).map((e=>new c.LineRangeFragment(e.modified,n,e)))),l=new Set;for(const e of o){let t,n=-1;for(const i of a){const r=e.computeSimilarity(i);r>n&&(n=r,t=i)}if(n>.9&&t&&(a.delete(t),s.push(new i.LineRangeMapping(e.range,t.range)),l.add(e.source),l.add(t.source)),!r.isValid())return{moves:s,excludedChanges:l}}return{moves:s,excludedChanges:l}}(e,t,n,h);if(!h.isValid())return[];const m=function(e,t,n,l,u,c){const h=[],f=new o.SetMap;for(const n of e)for(let e=n.original.startLineNumber;e<n.original.endLineNumberExclusive-2;e++){const n=`${t[e-1]}:${t[e+1-1]}:${t[e+2-1]}`;f.add(n,{range:new a.LineRange(e,e+3)})}const g=[];e.sort((0,r.compareBy)((e=>e.modified.startLineNumber),r.numberComparator));for(const t of e){let e=[];for(let i=t.modified.startLineNumber;i<t.modified.endLineNumberExclusive-2;i++){const t=`${n[i-1]}:${n[i+1-1]}:${n[i+2-1]}`,r=new a.LineRange(i,i+3),s=[];f.forEach(t,(({range:t})=>{for(const n of e)if(n.originalLineRange.endLineNumberExclusive+1===t.endLineNumberExclusive&&n.modifiedLineRange.endLineNumberExclusive+1===r.endLineNumberExclusive)return n.originalLineRange=new a.LineRange(n.originalLineRange.startLineNumber,t.endLineNumberExclusive),n.modifiedLineRange=new a.LineRange(n.modifiedLineRange.startLineNumber,r.endLineNumberExclusive),void s.push(n);const n={modifiedLineRange:r,originalLineRange:t};g.push(n),s.push(n)})),e=s}if(!c.isValid())return[]}g.sort((0,r.reverseOrder)((0,r.compareBy)((e=>e.modifiedLineRange.length),r.numberComparator)));const m=new a.LineRangeSet,p=new a.LineRangeSet;for(const e of g){const t=e.modifiedLineRange.startLineNumber-e.originalLineRange.startLineNumber,n=m.subtractFrom(e.modifiedLineRange),r=p.subtractFrom(e.originalLineRange).getWithDelta(t),s=n.getIntersection(r);for(const e of s.ranges){if(e.length<3)continue;const n=e,r=e.delta(-t);h.push(new i.LineRangeMapping(r,n)),m.addRange(n),p.addRange(r)}}h.sort((0,r.compareBy)((e=>e.original.startLineNumber),r.numberComparator));const _=new s.MonotonousArray(e);for(let t=0;t<h.length;t++){const n=h[t],r=_.findLastMonotonous((e=>e.original.startLineNumber<=n.original.startLineNumber)),o=(0,s.findLastMonotonous)(e,(e=>e.modified.startLineNumber<=n.modified.startLineNumber)),f=Math.max(n.original.startLineNumber-r.original.startLineNumber,n.modified.startLineNumber-o.modified.startLineNumber),g=_.findLastMonotonous((e=>e.original.startLineNumber<n.original.endLineNumberExclusive)),b=(0,s.findLastMonotonous)(e,(e=>e.modified.startLineNumber<n.modified.endLineNumberExclusive)),v=Math.max(g.original.endLineNumberExclusive-n.original.endLineNumberExclusive,b.modified.endLineNumberExclusive-n.modified.endLineNumberExclusive);let y,C;for(y=0;y<f;y++){const e=n.original.startLineNumber-y-1,t=n.modified.startLineNumber-y-1;if(e>l.length||t>u.length||m.contains(t)||p.contains(e)||!d(l[e-1],u[t-1],c))break}for(y>0&&(p.addRange(new a.LineRange(n.original.startLineNumber-y,n.original.startLineNumber)),m.addRange(new a.LineRange(n.modified.startLineNumber-y,n.modified.startLineNumber))),C=0;C<v;C++){const e=n.original.endLineNumberExclusive+C,t=n.modified.endLineNumberExclusive+C;if(e>l.length||t>u.length||m.contains(t)||p.contains(e)||!d(l[e-1],u[t-1],c))break}C>0&&(p.addRange(new a.LineRange(n.original.endLineNumberExclusive,n.original.endLineNumberExclusive+C)),m.addRange(new a.LineRange(n.modified.endLineNumberExclusive,n.modified.endLineNumberExclusive+C))),(y>0||C>0)&&(h[t]=new i.LineRangeMapping(new a.LineRange(n.original.startLineNumber-y,n.original.endLineNumberExclusive+C),new a.LineRange(n.modified.startLineNumber-y,n.modified.endLineNumberExclusive+C)))}return h}(e.filter((e=>!g.has(e))),l,u,t,n,h);return(0,r.pushMany)(f,m),f=function(e){if(0===e.length)return e;e.sort((0,r.compareBy)((e=>e.original.startLineNumber),r.numberComparator));const t=[e[0]];for(let n=1;n<e.length;n++){const i=t[t.length-1],r=e[n],s=r.original.startLineNumber-i.original.endLineNumberExclusive,o=r.modified.startLineNumber-i.modified.endLineNumberExclusive;s>=0&&o>=0&&s+o<=2?t[t.length-1]=i.join(r):t.push(r)}return t}(f),f=f.filter((e=>e.original.toOffsetRange().slice(t).map((e=>e.trim())).join("\n").length>=10)),f=function(e,t){const n=new s.MonotonousArray(e);return t=t.filter((t=>{const r=n.findLastMonotonous((e=>e.original.endLineNumberExclusive<t.original.endLineNumberExclusive))||new i.LineRangeMapping(new a.LineRange(1,1),new a.LineRange(1,1)),o=(0,s.findLastMonotonous)(e,(e=>e.modified.endLineNumberExclusive<t.modified.endLineNumberExclusive));return r!==o})),t}(e,f),f}})),r(e[47],t([0,1,7,12,10,3,2,8,45,29,46,43,31,16,30,44]),(function(e,t,n,i,r,s,o,a,l,u,c,h,d,f,g,m){"use strict";function p(e,t,r,s=!1){const o=[];for(const i of(0,n.groupAdjacentBy)(e.map((e=>_(e,t,r))),((e,t)=>e.original.overlapOrTouch(t.original)||e.modified.overlapOrTouch(t.modified)))){const e=i[0],t=i[i.length-1];o.push(new f.DetailedLineRangeMapping(e.original.join(t.original),e.modified.join(t.modified),i.map((e=>e.innerChanges[0]))))}return(0,i.assertFn)((()=>!(!s&&o.length>0&&o[0].original.startLineNumber!==o[0].modified.startLineNumber)&&(0,i.checkAdjacentItems)(o,((e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive==t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)))),o}function _(e,t,n){let i=0,s=0;1===e.modifiedRange.endColumn&&1===e.originalRange.endColumn&&e.originalRange.startLineNumber+i<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+i<=e.modifiedRange.endLineNumber&&(s=-1),e.modifiedRange.startColumn-1>=n[e.modifiedRange.startLineNumber-1].length&&e.originalRange.startColumn-1>=t[e.originalRange.startLineNumber-1].length&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+s&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+s&&(i=1);const o=new r.LineRange(e.originalRange.startLineNumber+i,e.originalRange.endLineNumber+1+s),a=new r.LineRange(e.modifiedRange.startLineNumber+i,e.modifiedRange.endLineNumber+1+s);return new f.DetailedLineRangeMapping(o,a,[e])}Object.defineProperty(t,"__esModule",{value:!0}),t.getLineRangeMapping=t.lineRangeMappingFromRangeMappings=t.DefaultLinesDiffComputer=void 0,t.DefaultLinesDiffComputer=class{constructor(){this.dynamicProgrammingDiffing=new l.DynamicProgrammingDiffing,this.myersDiffingAlgorithm=new u.MyersDiffAlgorithm}computeDiff(e,t,l){if(e.length<=1&&(0,n.equals)(e,t,((e,t)=>e===t)))return new d.LinesDiff([],[],!1);if(1===e.length&&0===e[0].length||1===t.length&&0===t[0].length)return new d.LinesDiff([new f.DetailedLineRangeMapping(new r.LineRange(1,e.length+1),new r.LineRange(1,t.length+1),[new f.RangeMapping(new o.Range(1,1,e.length,e[0].length+1),new o.Range(1,1,t.length,t[0].length+1))])],[],!1);const u=0===l.maxComputationTimeMs?a.InfiniteTimeout.instance:new a.DateTimeout(l.maxComputationTimeMs),c=!l.ignoreTrimWhitespace,g=new Map;function _(e){let t=g.get(e);return void 0===t&&(t=g.size,g.set(e,t)),t}const b=e.map((e=>_(e.trim()))),v=t.map((e=>_(e.trim()))),y=new m.LineSequence(b,e),C=new m.LineSequence(v,t),L=(()=>y.length+C.length<1700?this.dynamicProgrammingDiffing.compute(y,C,u,((n,i)=>e[n]===t[i]?0===t[i].length?.1:1+Math.log(1+t[i].length):.99)):this.myersDiffingAlgorithm.compute(y,C))();let E=L.diffs,S=L.hitTimeout;E=(0,h.optimizeSequenceDiffs)(y,C,E),E=(0,h.removeVeryShortMatchingLinesBetweenDiffs)(y,C,E);const w=[],R=n=>{if(c)for(let i=0;i<n;i++){const n=N+i,r=M+i;if(e[n]!==t[r]){const i=this.refineDiff(e,t,new a.SequenceDiff(new s.OffsetRange(n,n+1),new s.OffsetRange(r,r+1)),u,c);for(const e of i.mappings)w.push(e);i.hitTimeout&&(S=!0)}}};let N=0,M=0;for(const n of E){(0,i.assertFn)((()=>n.seq1Range.start-N==n.seq2Range.start-M)),R(n.seq1Range.start-N),N=n.seq1Range.endExclusive,M=n.seq2Range.endExclusive;const r=this.refineDiff(e,t,n,u,c);r.hitTimeout&&(S=!0);for(const e of r.mappings)w.push(e)}R(e.length-N);const A=p(w,e,t);let x=[];return l.computeMoves&&(x=this.computeMoves(A,e,t,b,v,u,c)),(0,i.assertFn)((()=>{function n(e,t){if(e.lineNumber<1||e.lineNumber>t.length)return!1;const n=t[e.lineNumber-1];return!(e.column<1||e.column>n.length+1)}function i(e,t){return!(e.startLineNumber<1||e.startLineNumber>t.length+1||e.endLineNumberExclusive<1||e.endLineNumberExclusive>t.length+1)}for(const r of A){if(!r.innerChanges)return!1;for(const i of r.innerChanges)if(!(n(i.modifiedRange.getStartPosition(),t)&&n(i.modifiedRange.getEndPosition(),t)&&n(i.originalRange.getStartPosition(),e)&&n(i.originalRange.getEndPosition(),e)))return!1;if(!i(r.modified,t)||!i(r.original,e))return!1}return!0})),new d.LinesDiff(A,x,S)}computeMoves(e,t,n,i,r,s,o){return(0,c.computeMovedLines)(e,t,n,i,r,s).map((e=>{const i=p(this.refineDiff(t,n,new a.SequenceDiff(e.original.toOffsetRange(),e.modified.toOffsetRange()),s,o).mappings,t,n,!0);return new d.MovedText(e,i)}))}refineDiff(e,t,n,i,r){const s=new g.LinesSliceCharSequence(e,n.seq1Range,r),o=new g.LinesSliceCharSequence(t,n.seq2Range,r),a=s.length+o.length<500?this.dynamicProgrammingDiffing.compute(s,o,i):this.myersDiffingAlgorithm.compute(s,o,i);let l=a.diffs;return l=(0,h.optimizeSequenceDiffs)(s,o,l),l=(0,h.extendDiffsToEntireWordIfAppropriate)(s,o,l),l=(0,h.removeShortMatches)(s,o,l),l=(0,h.removeVeryShortMatchingTextBetweenLongDiffs)(s,o,l),{mappings:l.map((e=>new f.RangeMapping(s.translateRange(e.seq1Range),o.translateRange(e.seq2Range)))),hitTimeout:a.hitTimeout}}},t.lineRangeMappingFromRangeMappings=p,t.getLineRangeMapping=_})),r(e[48],t([0,1,24,31,16,6,2,12,10]),(function(e,t,n,i,r,s,o,a,l){"use strict";function u(e,t,i,r){return new n.LcsDiff(e,t,i).ComputeDiff(r)}Object.defineProperty(t,"__esModule",{value:!0}),t.DiffComputer=t.LegacyLinesDiffComputer=void 0,t.LegacyLinesDiffComputer=class{computeDiff(e,t,n){var s;const u=new g(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),c=[];let h=null;for(const e of u.changes){let t,n;t=0===e.originalEndLineNumber?new l.LineRange(e.originalStartLineNumber+1,e.originalStartLineNumber+1):new l.LineRange(e.originalStartLineNumber,e.originalEndLineNumber+1),n=0===e.modifiedEndLineNumber?new l.LineRange(e.modifiedStartLineNumber+1,e.modifiedStartLineNumber+1):new l.LineRange(e.modifiedStartLineNumber,e.modifiedEndLineNumber+1);let i=new r.DetailedLineRangeMapping(t,n,null===(s=e.charChanges)||void 0===s?void 0:s.map((e=>new r.RangeMapping(new o.Range(e.originalStartLineNumber,e.originalStartColumn,e.originalEndLineNumber,e.originalEndColumn),new o.Range(e.modifiedStartLineNumber,e.modifiedStartColumn,e.modifiedEndLineNumber,e.modifiedEndColumn)))));h&&(h.modified.endLineNumberExclusive===i.modified.startLineNumber||h.original.endLineNumberExclusive===i.original.startLineNumber)&&(i=new r.DetailedLineRangeMapping(h.original.join(i.original),h.modified.join(i.modified),h.innerChanges&&i.innerChanges?h.innerChanges.concat(i.innerChanges):void 0),c.pop()),c.push(i),h=i}return(0,a.assertFn)((()=>(0,a.checkAdjacentItems)(c,((e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive==t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)))),new i.LinesDiff(c,[],u.quitEarly)}};class c{constructor(e){const t=[],n=[];for(let i=0,r=e.length;i<r;i++)t[i]=m(e[i],1),n[i]=p(e[i],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const i=[],r=[],s=[];let o=0;for(let a=t;a<=n;a++){const t=this.lines[a],l=e?this._startColumns[a]:1,u=e?this._endColumns[a]:t.length+1;for(let e=l;e<u;e++)i[o]=t.charCodeAt(e-1),r[o]=a+1,s[o]=e,o++;!e&&a<n&&(i[o]=10,r[o]=a+1,s[o]=t.length+1,o++)}return new h(i,r,s)}}class h{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}toString(){return"["+this._charCodes.map(((e,t)=>(10===e?"\\n":String.fromCharCode(e))+`-(${this._lineNumbers[t]},${this._columns[t]})`)).join(", ")+"]"}_assertIndex(e,t){if(e<0||e>=t.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(e){return e>0&&e===this._lineNumbers.length?this.getEndLineNumber(e-1):(this._assertIndex(e,this._lineNumbers),this._lineNumbers[e])}getEndLineNumber(e){return-1===e?this.getStartLineNumber(e+1):(this._assertIndex(e,this._lineNumbers),10===this._charCodes[e]?this._lineNumbers[e]+1:this._lineNumbers[e])}getStartColumn(e){return e>0&&e===this._columns.length?this.getEndColumn(e-1):(this._assertIndex(e,this._columns),this._columns[e])}getEndColumn(e){return-1===e?this.getStartColumn(e+1):(this._assertIndex(e,this._columns),10===this._charCodes[e]?1:this._columns[e]+1)}}class d{constructor(e,t,n,i,r,s,o,a){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=i,this.modifiedStartLineNumber=r,this.modifiedStartColumn=s,this.modifiedEndLineNumber=o,this.modifiedEndColumn=a}static createFromDiffChange(e,t,n){const i=t.getStartLineNumber(e.originalStart),r=t.getStartColumn(e.originalStart),s=t.getEndLineNumber(e.originalStart+e.originalLength-1),o=t.getEndColumn(e.originalStart+e.originalLength-1),a=n.getStartLineNumber(e.modifiedStart),l=n.getStartColumn(e.modifiedStart),u=n.getEndLineNumber(e.modifiedStart+e.modifiedLength-1),c=n.getEndColumn(e.modifiedStart+e.modifiedLength-1);return new d(i,r,s,o,a,l,u,c)}}class f{constructor(e,t,n,i,r){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=i,this.charChanges=r}static createFromDiffResult(e,t,n,i,r,s,o){let a,l,c,h,g;if(0===t.originalLength?(a=n.getStartLineNumber(t.originalStart)-1,l=0):(a=n.getStartLineNumber(t.originalStart),l=n.getEndLineNumber(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(c=i.getStartLineNumber(t.modifiedStart)-1,h=0):(c=i.getStartLineNumber(t.modifiedStart),h=i.getEndLineNumber(t.modifiedStart+t.modifiedLength-1)),s&&t.originalLength>0&&t.originalLength<20&&t.modifiedLength>0&&t.modifiedLength<20&&r()){const s=n.createCharSequence(e,t.originalStart,t.originalStart+t.originalLength-1),a=i.createCharSequence(e,t.modifiedStart,t.modifiedStart+t.modifiedLength-1);if(s.getElements().length>0&&a.getElements().length>0){let e=u(s,a,r,!0).changes;o&&(e=function(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let i=1,r=e.length;i<r;i++){const r=e[i],s=r.originalStart-(n.originalStart+n.originalLength),o=r.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(s,o)<3?(n.originalLength=r.originalStart+r.originalLength-n.originalStart,n.modifiedLength=r.modifiedStart+r.modifiedLength-n.modifiedStart):(t.push(r),n=r)}return t}(e)),g=[];for(let t=0,n=e.length;t<n;t++)g.push(d.createFromDiffChange(e[t],s,a))}}return new f(a,l,c,h,g)}}class g{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new c(e),this.modified=new c(t),this.continueLineDiff=_(n.maxComputationTime),this.continueCharDiff=_(0===n.maxComputationTime?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return 1===this.modified.lines.length&&0===this.modified.lines[0].length?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const e=u(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){const e=[];for(let n=0,i=t.length;n<i;n++)e.push(f.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}const i=[];let r=0,s=0;for(let e=-1,n=t.length;e<n;e++){const o=e+1<n?t[e+1]:null,a=o?o.originalStart:this.originalLines.length,l=o?o.modifiedStart:this.modifiedLines.length;for(;r<a&&s<l;){const e=this.originalLines[r],t=this.modifiedLines[s];if(e!==t){{let n=m(e,1),o=m(t,1);for(;n>1&&o>1&&e.charCodeAt(n-2)===t.charCodeAt(o-2);)n--,o--;(n>1||o>1)&&this._pushTrimWhitespaceCharChange(i,r+1,1,n,s+1,1,o)}{let n=p(e,1),o=p(t,1);const a=e.length+1,l=t.length+1;for(;n<a&&o<l&&e.charCodeAt(n-1)===e.charCodeAt(o-1);)n++,o++;(n<a||o<l)&&this._pushTrimWhitespaceCharChange(i,r+1,n,a,s+1,o,l)}}r++,s++}o&&(i.push(f.createFromDiffResult(this.shouldIgnoreTrimWhitespace,o,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),r+=o.originalLength,s+=o.modifiedLength)}return{quitEarly:n,changes:i}}_pushTrimWhitespaceCharChange(e,t,n,i,r,s,o){if(this._mergeTrimWhitespaceCharChange(e,t,n,i,r,s,o))return;let a;this.shouldComputeCharChanges&&(a=[new d(t,n,t,i,r,s,r,o)]),e.push(new f(t,t,r,r,a))}_mergeTrimWhitespaceCharChange(e,t,n,i,r,s,o){const a=e.length;if(0===a)return!1;const l=e[a-1];return 0!==l.originalEndLineNumber&&0!==l.modifiedEndLineNumber&&(l.originalEndLineNumber===t&&l.modifiedEndLineNumber===r?(this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new d(t,n,t,i,r,s,r,o)),!0):l.originalEndLineNumber+1===t&&l.modifiedEndLineNumber+1===r&&(l.originalEndLineNumber=t,l.modifiedEndLineNumber=r,this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new d(t,n,t,i,r,s,r,o)),!0))}}function m(e,t){const n=s.firstNonWhitespaceIndex(e);return-1===n?t:n+1}function p(e,t){const n=s.lastNonWhitespaceIndex(e);return-1===n?t:n+2}function _(e){if(0===e)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}t.DiffComputer=g})),r(e[49],t([0,1,48,47]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.linesDiffComputers=void 0,t.linesDiffComputers={getLegacy:()=>new n.LegacyLinesDiffComputer,getDefault:()=>new i.DefaultLinesDiffComputer}})),r(e[50],t([0,1,33]),(function(e,t,n){"use strict";function i(e){const t=[];for(const n of e){const e=Number(n);(e||0===e&&""!==n.replace(/\s/g,""))&&t.push(e)}return t}function r(e,t,n,i){return{red:e/255,blue:n/255,green:t/255,alpha:i}}function s(e,t){const n=t.index,i=t[0].length;if(!n)return;const r=e.positionAt(n);return{startLineNumber:r.lineNumber,startColumn:r.column,endLineNumber:r.lineNumber,endColumn:r.column+i}}function o(e,t){if(!e)return;const i=n.Color.Format.CSS.parseHex(t);return i?{range:e,color:r(i.rgba.r,i.rgba.g,i.rgba.b,i.rgba.a)}:void 0}function a(e,t,n){if(!e||1!==t.length)return;const s=i(t[0].values());return{range:e,color:r(s[0],s[1],s[2],n?s[3]:1)}}function l(e,t,s){if(!e||1!==t.length)return;const o=i(t[0].values()),a=new n.Color(new n.HSLA(o[0],o[1]/100,o[2]/100,s?o[3]:1));return{range:e,color:r(a.rgba.r,a.rgba.g,a.rgba.b,a.rgba.a)}}function u(e,t){return"string"==typeof e?[...e.matchAll(t)]:e.findMatches(t)}Object.defineProperty(t,"__esModule",{value:!0}),t.computeDefaultDocumentColors=void 0,t.computeDefaultDocumentColors=function(e){return e&&"function"==typeof e.getValue&&"function"==typeof e.positionAt?function(e){const t=[],n=u(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{3})\b|(#)([A-Fa-f0-9]{4})\b|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(n.length>0)for(const i of n){const n=i.filter((e=>void 0!==e)),r=n[1],c=n[2];if(!c)continue;let h;if("rgb"===r){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;h=a(s(e,i),u(c,t),!1)}else if("rgba"===r){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;h=a(s(e,i),u(c,t),!0)}else if("hsl"===r){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;h=l(s(e,i),u(c,t),!1)}else if("hsla"===r){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;h=l(s(e,i),u(c,t),!0)}else"#"===r&&(h=o(s(e,i),r+c));h&&t.push(h)}return t}(e):[]}})),r(e[51],t([0,1,27]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.computeLinks=t.LinkComputer=t.StateMachine=void 0;class i{constructor(e,t,n){const i=new Uint8Array(e*t);for(let r=0,s=e*t;r<s;r++)i[r]=n;this._data=i,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}}class r{constructor(e){let t=0,n=0;for(let i=0,r=e.length;i<r;i++){const[r,s,o]=e[i];s>t&&(t=s),r>n&&(n=r),o>n&&(n=o)}t++,n++;const r=new i(n,t,0);for(let t=0,n=e.length;t<n;t++){const[n,i,s]=e[t];r.set(n,i,s)}this._states=r,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}}t.StateMachine=r;let s=null,o=null;class a{static _createLink(e,t,n,i,r){let s=r-1;do{const n=t.charCodeAt(s);if(2!==e.get(n))break;s--}while(s>i);if(i>0){const e=t.charCodeAt(i-1),n=t.charCodeAt(s);(40===e&&41===n||91===e&&93===n||123===e&&125===n)&&s--}return{range:{startLineNumber:n,startColumn:i+1,endLineNumber:n,endColumn:s+2},url:t.substring(i,s+1)}}static computeLinks(e,t=function(){return null===s&&(s=new r([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),s}()){const i=function(){if(null===o){o=new n.CharacterClassifier(0);const e=" \t<>'\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…";for(let t=0;t<e.length;t++)o.set(e.charCodeAt(t),1);const t=".,;:";for(let e=0;e<t.length;e++)o.set(t.charCodeAt(e),2)}return o}(),l=[];for(let n=1,r=e.getLineCount();n<=r;n++){const r=e.getLineContent(n),s=r.length;let o=0,u=0,c=0,h=1,d=!1,f=!1,g=!1,m=!1;for(;o<s;){let e=!1;const s=r.charCodeAt(o);if(13===h){let t;switch(s){case 40:d=!0,t=0;break;case 41:t=d?0:1;break;case 91:g=!0,f=!0,t=0;break;case 93:g=!1,t=f?0:1;break;case 123:m=!0,t=0;break;case 125:t=m?0:1;break;case 39:case 34:case 96:t=c===s?1:39===c||34===c||96===c?0:1;break;case 42:t=42===c?1:0;break;case 124:t=124===c?1:0;break;case 32:t=g?0:1;break;default:t=i.get(s)}1===t&&(l.push(a._createLink(i,r,n,u,o)),e=!0)}else if(12===h){let t;91===s?(f=!0,t=0):t=i.get(s),1===t?e=!0:h=13}else h=t.nextState(h,s),0===h&&(e=!0);e&&(h=1,d=!1,f=!1,m=!1,u=o+1,c=s),o++}13===h&&l.push(a._createLink(i,r,n,u,s))}return l}}t.LinkComputer=a,t.computeLinks=function(e){return e&&"function"==typeof e.getLineCount&&"function"==typeof e.getLineContent?a.computeLinks(e):[]}})),r(e[52],t([0,1]),(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BasicInplaceReplace=void 0;class n{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(e,t,n,i,r){if(e&&t){const n=this.doNavigateValueSet(t,r);if(n)return{range:e,value:n}}if(n&&i){const e=this.doNavigateValueSet(i,r);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){const n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)}numberReplace(e,t){const n=Math.pow(10,e.length-(e.lastIndexOf(".")+1));let i=Number(e);const r=parseFloat(e);return isNaN(i)||isNaN(r)||i!==r?null:0!==i||t?(i=Math.floor(i*n),i+=t?n:-n,String(i/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let i=null;for(let r=0,s=e.length;null===i&&r<s;r++)i=this.valueSetReplace(e[r],t,n);return i}valueSetReplace(e,t,n){let i=e.indexOf(t);return i>=0?(i+=n?1:-1,i<0?i=e.length-1:i%=e.length,e[i]):null}}t.BasicInplaceReplace=n,n.INSTANCE=new n})),r(e[53],t([0,1,14]),(function(e,t,n){"use strict";var i,r,s,o,a;Object.defineProperty(t,"__esModule",{value:!0}),t.shouldSynchronizeModel=t.ApplyEditsResult=t.SearchData=t.ValidAnnotatedEditOperation=t.isITextSnapshot=t.FindMatch=t.TextModelResolvedOptions=t.InjectedTextCursorStops=t.MinimapPosition=t.GlyphMarginLane=t.OverviewRulerLane=void 0,(r=i||(t.OverviewRulerLane=i={}))[r.Left=1]="Left",r[r.Center=2]="Center",r[r.Right=4]="Right",r[r.Full=7]="Full",function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"}(s||(t.GlyphMarginLane=s={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(o||(t.MinimapPosition=o={})),function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"}(a||(t.InjectedTextCursorStops=a={})),t.TextModelResolvedOptions=class{get originalIndentSize(){return this._indentSizeIsTabSize?"tabSize":this.indentSize}constructor(e){this._textModelResolvedOptionsBrand=void 0,this.tabSize=Math.max(1,0|e.tabSize),"tabSize"===e.indentSize?(this.indentSize=this.tabSize,this._indentSizeIsTabSize=!0):(this.indentSize=Math.max(1,0|e.indentSize),this._indentSizeIsTabSize=!1),this.insertSpaces=!!e.insertSpaces,this.defaultEOL=0|e.defaultEOL,this.trimAutoWhitespace=!!e.trimAutoWhitespace,this.bracketPairColorizationOptions=e.bracketPairColorizationOptions}equals(e){return this.tabSize===e.tabSize&&this._indentSizeIsTabSize===e._indentSizeIsTabSize&&this.indentSize===e.indentSize&&this.insertSpaces===e.insertSpaces&&this.defaultEOL===e.defaultEOL&&this.trimAutoWhitespace===e.trimAutoWhitespace&&(0,n.equals)(this.bracketPairColorizationOptions,e.bracketPairColorizationOptions)}createChangeEvent(e){return{tabSize:this.tabSize!==e.tabSize,indentSize:this.indentSize!==e.indentSize,insertSpaces:this.insertSpaces!==e.insertSpaces,trimAutoWhitespace:this.trimAutoWhitespace!==e.trimAutoWhitespace}}},t.FindMatch=class{constructor(e,t){this._findMatchBrand=void 0,this.range=e,this.matches=t}},t.isITextSnapshot=function(e){return e&&"function"==typeof e.read},t.ValidAnnotatedEditOperation=class{constructor(e,t,n,i,r,s){this.identifier=e,this.range=t,this.text=n,this.forceMoveMarkers=i,this.isAutoWhitespaceEdit=r,this._isTracked=s}},t.SearchData=class{constructor(e,t,n){this.regex=e,this.wordSeparators=t,this.simpleSearch=n}},t.ApplyEditsResult=class{constructor(e,t,n){this.reverseEdits=e,this.changes=t,this.trimAutoWhitespaceLineNumbers=n}},t.shouldSynchronizeModel=function(e){return!e.isTooLargeForSyncing()&&!e.isForSimpleWidget}})),r(e[54],t([0,1,7,26]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PrefixSumIndexOfResult=t.ConstantTimePrefixSumComputer=t.PrefixSumComputer=void 0,t.PrefixSumComputer=class{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=(0,i.toUint32)(e);const n=this.values,r=this.prefixSum,s=t.length;return 0!==s&&(this.values=new Uint32Array(n.length+s),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+s),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(e,t){return e=(0,i.toUint32)(e),t=(0,i.toUint32)(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=(0,i.toUint32)(e),t=(0,i.toUint32)(t);const n=this.values,r=this.prefixSum;if(e>=n.length)return!1;const s=n.length-e;return t>=s&&(t=s),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return 0===this.values.length?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=(0,i.toUint32)(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,i=0,s=0,o=0;for(;t<=n;)if(i=t+(n-t)/2|0,s=this.prefixSum[i],o=s-this.values[i],e<o)n=i-1;else{if(!(e>=s))break;t=i+1}return new r(i,e-o)}},t.ConstantTimePrefixSumComputer=class{constructor(e){this._values=e,this._isValid=!1,this._validEndIndex=-1,this._prefixSum=[],this._indexBySum=[]}getTotalSum(){return this._ensureValid(),this._indexBySum.length}getPrefixSum(e){return this._ensureValid(),0===e?0:this._prefixSum[e-1]}getIndexOf(e){this._ensureValid();const t=this._indexBySum[e],n=t>0?this._prefixSum[t-1]:0;return new r(t,e-n)}removeValues(e,t){this._values.splice(e,t),this._invalidate(e)}insertValues(e,t){this._values=(0,n.arrayInsert)(this._values,e,t),this._invalidate(e)}_invalidate(e){this._isValid=!1,this._validEndIndex=Math.min(this._validEndIndex,e-1)}_ensureValid(){if(!this._isValid){for(let e=this._validEndIndex+1,t=this._values.length;e<t;e++){const t=this._values[e],n=e>0?this._prefixSum[e-1]:0;this._prefixSum[e]=n+t;for(let i=0;i<t;i++)this._indexBySum[n+i]=e}this._prefixSum.length=this._values.length,this._indexBySum.length=this._prefixSum[this._prefixSum.length-1],this._isValid=!0,this._validEndIndex=this._values.length-1}}setValue(e,t){this._values[e]!==t&&(this._values[e]=t,this._invalidate(e))}};class r{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}}t.PrefixSumIndexOfResult=r})),r(e[55],t([0,1,6,4,54]),(function(e,t,n,i,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MirrorTextModel=void 0,t.MirrorTextModel=class{constructor(e,t,n,i){this._uri=e,this._lines=t,this._eol=n,this._versionId=i,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return null===this._cachedTextValue&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);const t=e.changes;for(const e of t)this._acceptDeleteRange(e.range),this._acceptInsertText(new i.Position(e.range.startLineNumber,e.range.startColumn),e.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let i=0;i<t;i++)n[i]=this._lines[i].length+e;this._lineStarts=new r.PrefixSumComputer(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.setValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}}_acceptInsertText(e,t){if(0===t.length)return;const i=(0,n.splitLines)(t);if(1===i.length)return void this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+i[0]+this._lines[e.lineNumber-1].substring(e.column-1));i[i.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+i[0]);const r=new Uint32Array(i.length-1);for(let t=1;t<i.length;t++)this._lines.splice(e.lineNumber+t-1,0,i[t]),r[t-1]=i[t].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,r)}}})),r(e[56],t([0,1,6,42,4,2,53]),(function(e,t,n,i,r,s,o){"use strict";function a(e){if(!e||0===e.length)return!1;for(let t=0,n=e.length;t<n;t++){const i=e.charCodeAt(t);if(10===i)return!0;if(92===i){if(t++,t>=n)break;const i=e.charCodeAt(t);if(110===i||114===i||87===i)return!0}}return!1}function l(e,t,n){if(!n)return new o.FindMatch(e,null);const i=[];for(let e=0,n=t.length;e<n;e++)i[e]=t[e];return new o.FindMatch(e,i)}Object.defineProperty(t,"__esModule",{value:!0}),t.Searcher=t.isValidMatch=t.TextModelSearch=t.createFindMatch=t.isMultilineRegexSource=t.SearchParams=void 0,t.SearchParams=class{constructor(e,t,n,i){this.searchString=e,this.isRegex=t,this.matchCase=n,this.wordSeparators=i}parseSearchRequest(){if(""===this.searchString)return null;let e;e=this.isRegex?a(this.searchString):this.searchString.indexOf("\n")>=0;let t=null;try{t=n.createRegExp(this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:e,global:!0,unicode:!0})}catch{return null}if(!t)return null;let r=!this.isRegex&&!e;return r&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(r=this.matchCase),new o.SearchData(t,this.wordSeparators?(0,i.getMapForWordSeparators)(this.wordSeparators):null,r?this.searchString:null)}},t.isMultilineRegexSource=a,t.createFindMatch=l;class u{constructor(e){const t=[];let n=0;for(let i=0,r=e.length;i<r;i++)10===e.charCodeAt(i)&&(t[n++]=i);this._lineFeedsOffsets=t}findLineFeedCountBeforeOffset(e){const t=this._lineFeedsOffsets;let n=0,i=t.length-1;if(-1===i||e<=t[0])return 0;for(;n<i;){const r=n+((i-n)/2>>0);t[r]>=e?i=r-1:t[r+1]>=e?(n=r,i=r):n=r+1}return n+1}}function c(e,t,n,i,r){return function(e,t,n,i,r){if(0===i)return!0;const s=t.charCodeAt(i-1);if(0!==e.get(s)||13===s||10===s)return!0;if(r>0){const n=t.charCodeAt(i);if(0!==e.get(n))return!0}return!1}(e,t,0,i,r)&&function(e,t,n,i,r){if(i+r===n)return!0;const s=t.charCodeAt(i+r);if(0!==e.get(s)||13===s||10===s)return!0;if(r>0){const n=t.charCodeAt(i+r-1);if(0!==e.get(n))return!0}return!1}(e,t,n,i,r)}t.TextModelSearch=class{static findMatches(e,t,n,i,r){const s=t.parseSearchRequest();return s?s.regex.multiline?this._doFindMatchesMultiline(e,n,new h(s.wordSeparators,s.regex),i,r):this._doFindMatchesLineByLine(e,n,s,i,r):[]}static _getMultilineMatchRange(e,t,n,i,r,o){let a,l,u=0;if(i?(u=i.findLineFeedCountBeforeOffset(r),a=t+r+u):a=t+r,i){const e=i.findLineFeedCountBeforeOffset(r+o.length)-u;l=a+o.length+e}else l=a+o.length;const c=e.getPositionAt(a),h=e.getPositionAt(l);return new s.Range(c.lineNumber,c.column,h.lineNumber,h.column)}static _doFindMatchesMultiline(e,t,n,i,r){const s=e.getOffsetAt(t.getStartPosition()),o=e.getValueInRange(t,1),a="\r\n"===e.getEOL()?new u(o):null,c=[];let h,d=0;for(n.reset(0);h=n.next(o);)if(c[d++]=l(this._getMultilineMatchRange(e,s,o,a,h.index,h[0]),h,i),d>=r)return c;return c}static _doFindMatchesLineByLine(e,t,n,i,r){const s=[];let o=0;if(t.startLineNumber===t.endLineNumber){const a=e.getLineContent(t.startLineNumber).substring(t.startColumn-1,t.endColumn-1);return o=this._findMatchesInLine(n,a,t.startLineNumber,t.startColumn-1,o,s,i,r),s}const a=e.getLineContent(t.startLineNumber).substring(t.startColumn-1);o=this._findMatchesInLine(n,a,t.startLineNumber,t.startColumn-1,o,s,i,r);for(let a=t.startLineNumber+1;a<t.endLineNumber&&o<r;a++)o=this._findMatchesInLine(n,e.getLineContent(a),a,0,o,s,i,r);if(o<r){const a=e.getLineContent(t.endLineNumber).substring(0,t.endColumn-1);o=this._findMatchesInLine(n,a,t.endLineNumber,0,o,s,i,r)}return s}static _findMatchesInLine(e,t,n,i,r,a,u,d){const f=e.wordSeparators;if(!u&&e.simpleSearch){const l=e.simpleSearch,u=l.length,h=t.length;let g=-u;for(;-1!==(g=t.indexOf(l,g+u));)if((!f||c(f,t,h,g,u))&&(a[r++]=new o.FindMatch(new s.Range(n,g+1+i,n,g+1+u+i),null),r>=d))return r;return r}const g=new h(e.wordSeparators,e.regex);let m;g.reset(0);do{if(m=g.next(t),m&&(a[r++]=l(new s.Range(n,m.index+1+i,n,m.index+1+m[0].length+i),m,u),r>=d))return r}while(m);return r}static findNextMatch(e,t,n,i){const r=t.parseSearchRequest();if(!r)return null;const s=new h(r.wordSeparators,r.regex);return r.regex.multiline?this._doFindNextMatchMultiline(e,n,s,i):this._doFindNextMatchLineByLine(e,n,s,i)}static _doFindNextMatchMultiline(e,t,n,i){const o=new r.Position(t.lineNumber,1),a=e.getOffsetAt(o),c=e.getLineCount(),h=e.getValueInRange(new s.Range(o.lineNumber,o.column,c,e.getLineMaxColumn(c)),1),d="\r\n"===e.getEOL()?new u(h):null;n.reset(t.column-1);const f=n.next(h);return f?l(this._getMultilineMatchRange(e,a,h,d,f.index,f[0]),f,i):1!==t.lineNumber||1!==t.column?this._doFindNextMatchMultiline(e,new r.Position(1,1),n,i):null}static _doFindNextMatchLineByLine(e,t,n,i){const r=e.getLineCount(),s=t.lineNumber,o=e.getLineContent(s),a=this._findFirstMatchInLine(n,o,s,t.column,i);if(a)return a;for(let t=1;t<=r;t++){const o=(s+t-1)%r,a=e.getLineContent(o+1),l=this._findFirstMatchInLine(n,a,o+1,1,i);if(l)return l}return null}static _findFirstMatchInLine(e,t,n,i,r){e.reset(i-1);const o=e.next(t);return o?l(new s.Range(n,o.index+1,n,o.index+1+o[0].length),o,r):null}static findPreviousMatch(e,t,n,i){const r=t.parseSearchRequest();if(!r)return null;const s=new h(r.wordSeparators,r.regex);return r.regex.multiline?this._doFindPreviousMatchMultiline(e,n,s,i):this._doFindPreviousMatchLineByLine(e,n,s,i)}static _doFindPreviousMatchMultiline(e,t,n,i){const o=this._doFindMatchesMultiline(e,new s.Range(1,1,t.lineNumber,t.column),n,i,9990);if(o.length>0)return o[o.length-1];const a=e.getLineCount();return t.lineNumber!==a||t.column!==e.getLineMaxColumn(a)?this._doFindPreviousMatchMultiline(e,new r.Position(a,e.getLineMaxColumn(a)),n,i):null}static _doFindPreviousMatchLineByLine(e,t,n,i){const r=e.getLineCount(),s=t.lineNumber,o=e.getLineContent(s).substring(0,t.column-1),a=this._findLastMatchInLine(n,o,s,i);if(a)return a;for(let t=1;t<=r;t++){const o=(r+s-t-1)%r,a=e.getLineContent(o+1),l=this._findLastMatchInLine(n,a,o+1,i);if(l)return l}return null}static _findLastMatchInLine(e,t,n,i){let r,o=null;for(e.reset(0);r=e.next(t);)o=l(new s.Range(n,r.index+1,n,r.index+1+r[0].length),r,i);return o}},t.isValidMatch=c;class h{constructor(e,t){this._wordSeparators=e,this._searchRegex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(e){const t=e.length;let i;do{if(this._prevMatchStartIndex+this._prevMatchLength===t||(i=this._searchRegex.exec(e),!i))return null;const r=i.index,s=i[0].length;if(r===this._prevMatchStartIndex&&s===this._prevMatchLength){if(0===s){n.getNextCodePoint(e,t,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=r,this._prevMatchLength=s,!this._wordSeparators||c(this._wordSeparators,e,t,r,s))return i}while(i);return null}}t.Searcher=h})),r(e[57],t([0,1,2,56,6,12,28]),(function(e,t,n,i,r,s,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UnicodeTextModelHighlighter=void 0,t.UnicodeTextModelHighlighter=class{static computeUnicodeHighlights(e,t,l){const u=l?l.startLineNumber:1,c=l?l.endLineNumber:e.getLineCount(),h=new a(t),d=h.getCandidateCodePoints();let f;var g;f="allNonBasicAscii"===d?new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):new RegExp((g=Array.from(d),`[${r.escapeRegExpCharacters(g.map((e=>String.fromCodePoint(e))).join(""))}]`),"g");const m=new i.Searcher(null,f),p=[];let _,b=!1,v=0,y=0,C=0;e:for(let t=u,i=c;t<=i;t++){const i=e.getLineContent(t),a=i.length;m.reset(0);do{if(_=m.next(i),_){let e=_.index,l=_.index+_[0].length;if(e>0){const t=i.charCodeAt(e-1);r.isHighSurrogate(t)&&e--}if(l+1<a){const e=i.charCodeAt(l-1);r.isHighSurrogate(e)&&l++}const u=i.substring(e,l);let c=(0,o.getWordAtText)(e+1,o.DEFAULT_WORD_REGEXP,i,0);c&&c.endColumn<=e+1&&(c=null);const d=h.shouldHighlightNonBasicASCII(u,c?c.word:null);if(0!==d){3===d?v++:2===d?y++:1===d?C++:(0,s.assertNever)(d);const i=1e3;if(p.length>=i){b=!0;break e}p.push(new n.Range(t,e+1,t,l+1))}}}while(_)}return{ranges:p,hasMore:b,ambiguousCharacterCount:v,invisibleCharacterCount:y,nonBasicAsciiCharacterCount:C}}static computeUnicodeHighlightReason(e,t){const n=new a(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{const i=e.codePointAt(0),s=n.ambiguousCharacters.getPrimaryConfusable(i),o=r.AmbiguousCharacters.getLocales().filter((e=>!r.AmbiguousCharacters.getInstance(new Set([...t.allowedLocales,e])).isAmbiguous(i)));return{kind:0,confusableWith:String.fromCodePoint(s),notAmbiguousInLocales:o}}case 1:return{kind:2}}}};class a{constructor(e){this.options=e,this.allowedCodePoints=new Set(e.allowedCodePoints),this.ambiguousCharacters=r.AmbiguousCharacters.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const e=new Set;if(this.options.invisibleCharacters)for(const t of r.InvisibleCharacters.codePoints)l(String.fromCodePoint(t))||e.add(t);if(this.options.ambiguousCharacters)for(const t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(const t of this.allowedCodePoints)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){const n=e.codePointAt(0);if(this.allowedCodePoints.has(n))return 0;if(this.options.nonBasicASCII)return 1;let i=!1,s=!1;if(t)for(const e of t){const t=e.codePointAt(0),n=r.isBasicASCII(e);i=i||n,!n&&!this.ambiguousCharacters.isAmbiguous(t)&&!r.InvisibleCharacters.isInvisibleCharacter(t)&&(s=!0)}return!i&&s?0:this.options.invisibleCharacters&&!l(e)&&r.InvisibleCharacters.isInvisibleCharacter(n)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}}function l(e){return" "===e||"\n"===e||"\t"===e}})),r(e[58],t([0,1]),(function(e,t){"use strict";var n,i,r,s,o,a,l,u,c,h,d,f,g,m,p,_,b,v,y,C,L,E,S,w,R,N,M,A,x,D,I,O,T,k,P,F,q,U,B,K;Object.defineProperty(t,"__esModule",{value:!0}),t.WrappingIndent=t.TrackedRangeStickiness=t.TextEditorCursorStyle=t.TextEditorCursorBlinkingStyle=t.SymbolTag=t.SymbolKind=t.SignatureHelpTriggerKind=t.SelectionDirection=t.ScrollbarVisibility=t.ScrollType=t.RenderMinimap=t.RenderLineNumbersType=t.PositionAffinity=t.OverviewRulerLane=t.OverlayWidgetPositionPreference=t.MouseTargetType=t.MinimapPosition=t.MarkerTag=t.MarkerSeverity=t.KeyCode=t.InlineCompletionTriggerKind=t.InlayHintKind=t.InjectedTextCursorStops=t.IndentAction=t.GlyphMarginLane=t.EndOfLineSequence=t.EndOfLinePreference=t.EditorOption=t.EditorAutoIndentStrategy=t.DocumentHighlightKind=t.DefaultEndOfLine=t.CursorChangeReason=t.ContentWidgetPositionPreference=t.CompletionTriggerKind=t.CompletionItemTag=t.CompletionItemKind=t.CompletionItemInsertTextRule=t.CodeActionTriggerType=t.AccessibilitySupport=void 0,(i=n||(t.AccessibilitySupport=n={}))[i.Unknown=0]="Unknown",i[i.Disabled=1]="Disabled",i[i.Enabled=2]="Enabled",function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"}(r||(t.CodeActionTriggerType=r={})),function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"}(s||(t.CompletionItemInsertTextRule=s={})),function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"}(o||(t.CompletionItemKind=o={})),function(e){e[e.Deprecated=1]="Deprecated"}(a||(t.CompletionItemTag=a={})),function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"}(l||(t.CompletionTriggerKind=l={})),function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"}(u||(t.ContentWidgetPositionPreference=u={})),function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"}(c||(t.CursorChangeReason=c={})),function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(h||(t.DefaultEndOfLine=h={})),function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(d||(t.DocumentHighlightKind=d={})),function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"}(f||(t.EditorAutoIndentStrategy=f={})),function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.ariaRequired=5]="ariaRequired",e[e.autoClosingBrackets=6]="autoClosingBrackets",e[e.autoClosingComments=7]="autoClosingComments",e[e.screenReaderAnnounceInlineSuggestion=8]="screenReaderAnnounceInlineSuggestion",e[e.autoClosingDelete=9]="autoClosingDelete",e[e.autoClosingOvertype=10]="autoClosingOvertype",e[e.autoClosingQuotes=11]="autoClosingQuotes",e[e.autoIndent=12]="autoIndent",e[e.automaticLayout=13]="automaticLayout",e[e.autoSurround=14]="autoSurround",e[e.bracketPairColorization=15]="bracketPairColorization",e[e.guides=16]="guides",e[e.codeLens=17]="codeLens",e[e.codeLensFontFamily=18]="codeLensFontFamily",e[e.codeLensFontSize=19]="codeLensFontSize",e[e.colorDecorators=20]="colorDecorators",e[e.colorDecoratorsLimit=21]="colorDecoratorsLimit",e[e.columnSelection=22]="columnSelection",e[e.comments=23]="comments",e[e.contextmenu=24]="contextmenu",e[e.copyWithSyntaxHighlighting=25]="copyWithSyntaxHighlighting",e[e.cursorBlinking=26]="cursorBlinking",e[e.cursorSmoothCaretAnimation=27]="cursorSmoothCaretAnimation",e[e.cursorStyle=28]="cursorStyle",e[e.cursorSurroundingLines=29]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=30]="cursorSurroundingLinesStyle",e[e.cursorWidth=31]="cursorWidth",e[e.disableLayerHinting=32]="disableLayerHinting",e[e.disableMonospaceOptimizations=33]="disableMonospaceOptimizations",e[e.domReadOnly=34]="domReadOnly",e[e.dragAndDrop=35]="dragAndDrop",e[e.dropIntoEditor=36]="dropIntoEditor",e[e.emptySelectionClipboard=37]="emptySelectionClipboard",e[e.experimentalWhitespaceRendering=38]="experimentalWhitespaceRendering",e[e.extraEditorClassName=39]="extraEditorClassName",e[e.fastScrollSensitivity=40]="fastScrollSensitivity",e[e.find=41]="find",e[e.fixedOverflowWidgets=42]="fixedOverflowWidgets",e[e.folding=43]="folding",e[e.foldingStrategy=44]="foldingStrategy",e[e.foldingHighlight=45]="foldingHighlight",e[e.foldingImportsByDefault=46]="foldingImportsByDefault",e[e.foldingMaximumRegions=47]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=48]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=49]="fontFamily",e[e.fontInfo=50]="fontInfo",e[e.fontLigatures=51]="fontLigatures",e[e.fontSize=52]="fontSize",e[e.fontWeight=53]="fontWeight",e[e.fontVariations=54]="fontVariations",e[e.formatOnPaste=55]="formatOnPaste",e[e.formatOnType=56]="formatOnType",e[e.glyphMargin=57]="glyphMargin",e[e.gotoLocation=58]="gotoLocation",e[e.hideCursorInOverviewRuler=59]="hideCursorInOverviewRuler",e[e.hover=60]="hover",e[e.inDiffEditor=61]="inDiffEditor",e[e.inlineSuggest=62]="inlineSuggest",e[e.letterSpacing=63]="letterSpacing",e[e.lightbulb=64]="lightbulb",e[e.lineDecorationsWidth=65]="lineDecorationsWidth",e[e.lineHeight=66]="lineHeight",e[e.lineNumbers=67]="lineNumbers",e[e.lineNumbersMinChars=68]="lineNumbersMinChars",e[e.linkedEditing=69]="linkedEditing",e[e.links=70]="links",e[e.matchBrackets=71]="matchBrackets",e[e.minimap=72]="minimap",e[e.mouseStyle=73]="mouseStyle",e[e.mouseWheelScrollSensitivity=74]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=75]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=76]="multiCursorMergeOverlapping",e[e.multiCursorModifier=77]="multiCursorModifier",e[e.multiCursorPaste=78]="multiCursorPaste",e[e.multiCursorLimit=79]="multiCursorLimit",e[e.occurrencesHighlight=80]="occurrencesHighlight",e[e.overviewRulerBorder=81]="overviewRulerBorder",e[e.overviewRulerLanes=82]="overviewRulerLanes",e[e.padding=83]="padding",e[e.pasteAs=84]="pasteAs",e[e.parameterHints=85]="parameterHints",e[e.peekWidgetDefaultFocus=86]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=87]="definitionLinkOpensInPeek",e[e.quickSuggestions=88]="quickSuggestions",e[e.quickSuggestionsDelay=89]="quickSuggestionsDelay",e[e.readOnly=90]="readOnly",e[e.readOnlyMessage=91]="readOnlyMessage",e[e.renameOnType=92]="renameOnType",e[e.renderControlCharacters=93]="renderControlCharacters",e[e.renderFinalNewline=94]="renderFinalNewline",e[e.renderLineHighlight=95]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=96]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=97]="renderValidationDecorations",e[e.renderWhitespace=98]="renderWhitespace",e[e.revealHorizontalRightPadding=99]="revealHorizontalRightPadding",e[e.roundedSelection=100]="roundedSelection",e[e.rulers=101]="rulers",e[e.scrollbar=102]="scrollbar",e[e.scrollBeyondLastColumn=103]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=104]="scrollBeyondLastLine",e[e.scrollPredominantAxis=105]="scrollPredominantAxis",e[e.selectionClipboard=106]="selectionClipboard",e[e.selectionHighlight=107]="selectionHighlight",e[e.selectOnLineNumbers=108]="selectOnLineNumbers",e[e.showFoldingControls=109]="showFoldingControls",e[e.showUnused=110]="showUnused",e[e.snippetSuggestions=111]="snippetSuggestions",e[e.smartSelect=112]="smartSelect",e[e.smoothScrolling=113]="smoothScrolling",e[e.stickyScroll=114]="stickyScroll",e[e.stickyTabStops=115]="stickyTabStops",e[e.stopRenderingLineAfter=116]="stopRenderingLineAfter",e[e.suggest=117]="suggest",e[e.suggestFontSize=118]="suggestFontSize",e[e.suggestLineHeight=119]="suggestLineHeight",e[e.suggestOnTriggerCharacters=120]="suggestOnTriggerCharacters",e[e.suggestSelection=121]="suggestSelection",e[e.tabCompletion=122]="tabCompletion",e[e.tabIndex=123]="tabIndex",e[e.unicodeHighlighting=124]="unicodeHighlighting",e[e.unusualLineTerminators=125]="unusualLineTerminators",e[e.useShadowDOM=126]="useShadowDOM",e[e.useTabStops=127]="useTabStops",e[e.wordBreak=128]="wordBreak",e[e.wordSeparators=129]="wordSeparators",e[e.wordWrap=130]="wordWrap",e[e.wordWrapBreakAfterCharacters=131]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=132]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=133]="wordWrapColumn",e[e.wordWrapOverride1=134]="wordWrapOverride1",e[e.wordWrapOverride2=135]="wordWrapOverride2",e[e.wrappingIndent=136]="wrappingIndent",e[e.wrappingStrategy=137]="wrappingStrategy",e[e.showDeprecated=138]="showDeprecated",e[e.inlayHints=139]="inlayHints",e[e.editorClassName=140]="editorClassName",e[e.pixelRatio=141]="pixelRatio",e[e.tabFocusMode=142]="tabFocusMode",e[e.layoutInfo=143]="layoutInfo",e[e.wrappingInfo=144]="wrappingInfo",e[e.defaultColorDecorators=145]="defaultColorDecorators",e[e.colorDecoratorsActivatedOn=146]="colorDecoratorsActivatedOn",e[e.inlineCompletionsAccessibilityVerbose=147]="inlineCompletionsAccessibilityVerbose"}(g||(t.EditorOption=g={})),function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"}(m||(t.EndOfLinePreference=m={})),function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"}(p||(t.EndOfLineSequence=p={})),function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"}(_||(t.GlyphMarginLane=_={})),function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"}(b||(t.IndentAction=b={})),function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"}(v||(t.InjectedTextCursorStops=v={})),function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(y||(t.InlayHintKind=y={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(C||(t.InlineCompletionTriggerKind=C={})),function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.F20=78]="F20",e[e.F21=79]="F21",e[e.F22=80]="F22",e[e.F23=81]="F23",e[e.F24=82]="F24",e[e.NumLock=83]="NumLock",e[e.ScrollLock=84]="ScrollLock",e[e.Semicolon=85]="Semicolon",e[e.Equal=86]="Equal",e[e.Comma=87]="Comma",e[e.Minus=88]="Minus",e[e.Period=89]="Period",e[e.Slash=90]="Slash",e[e.Backquote=91]="Backquote",e[e.BracketLeft=92]="BracketLeft",e[e.Backslash=93]="Backslash",e[e.BracketRight=94]="BracketRight",e[e.Quote=95]="Quote",e[e.OEM_8=96]="OEM_8",e[e.IntlBackslash=97]="IntlBackslash",e[e.Numpad0=98]="Numpad0",e[e.Numpad1=99]="Numpad1",e[e.Numpad2=100]="Numpad2",e[e.Numpad3=101]="Numpad3",e[e.Numpad4=102]="Numpad4",e[e.Numpad5=103]="Numpad5",e[e.Numpad6=104]="Numpad6",e[e.Numpad7=105]="Numpad7",e[e.Numpad8=106]="Numpad8",e[e.Numpad9=107]="Numpad9",e[e.NumpadMultiply=108]="NumpadMultiply",e[e.NumpadAdd=109]="NumpadAdd",e[e.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=111]="NumpadSubtract",e[e.NumpadDecimal=112]="NumpadDecimal",e[e.NumpadDivide=113]="NumpadDivide",e[e.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",e[e.ABNT_C1=115]="ABNT_C1",e[e.ABNT_C2=116]="ABNT_C2",e[e.AudioVolumeMute=117]="AudioVolumeMute",e[e.AudioVolumeUp=118]="AudioVolumeUp",e[e.AudioVolumeDown=119]="AudioVolumeDown",e[e.BrowserSearch=120]="BrowserSearch",e[e.BrowserHome=121]="BrowserHome",e[e.BrowserBack=122]="BrowserBack",e[e.BrowserForward=123]="BrowserForward",e[e.MediaTrackNext=124]="MediaTrackNext",e[e.MediaTrackPrevious=125]="MediaTrackPrevious",e[e.MediaStop=126]="MediaStop",e[e.MediaPlayPause=127]="MediaPlayPause",e[e.LaunchMediaPlayer=128]="LaunchMediaPlayer",e[e.LaunchMail=129]="LaunchMail",e[e.LaunchApp2=130]="LaunchApp2",e[e.Clear=131]="Clear",e[e.MAX_VALUE=132]="MAX_VALUE"}(L||(t.KeyCode=L={})),function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"}(E||(t.MarkerSeverity=E={})),function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"}(S||(t.MarkerTag=S={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(w||(t.MinimapPosition=w={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"}(R||(t.MouseTargetType=R={})),function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"}(N||(t.OverlayWidgetPositionPreference=N={})),function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(M||(t.OverviewRulerLane=M={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"}(A||(t.PositionAffinity=A={})),function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"}(x||(t.RenderLineNumbersType=x={})),function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"}(D||(t.RenderMinimap=D={})),function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"}(I||(t.ScrollType=I={})),function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"}(O||(t.ScrollbarVisibility=O={})),function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"}(T||(t.SelectionDirection=T={})),function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(k||(t.SignatureHelpTriggerKind=k={})),function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"}(P||(t.SymbolKind=P={})),function(e){e[e.Deprecated=1]="Deprecated"}(F||(t.SymbolTag=F={})),function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"}(q||(t.TextEditorCursorBlinkingStyle=q={})),function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"}(U||(t.TextEditorCursorStyle=U={})),function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"}(B||(t.TrackedRangeStickiness=B={})),function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"}(K||(t.WrappingIndent=K={}))})),r(e[59],t([0,1,9,13]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TokenizationRegistry=void 0,t.TokenizationRegistry=class{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new n.Emitter,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this._tokenizationSupports.set(e,t),this.handleChange([e]),(0,i.toDisposable)((()=>{this._tokenizationSupports.get(e)===t&&(this._tokenizationSupports.delete(e),this.handleChange([e]))}))}get(e){return this._tokenizationSupports.get(e)||null}registerFactory(e,t){var n;null===(n=this._factories.get(e))||void 0===n||n.dispose();const s=new r(this,e,t);return this._factories.set(e,s),(0,i.toDisposable)((()=>{const t=this._factories.get(e);!t||t!==s||(this._factories.delete(e),t.dispose())}))}getOrCreate(e){return o(this,void 0,void 0,(function*(){const t=this.get(e);if(t)return t;const n=this._factories.get(e);return!n||n.isResolved?null:(yield n.resolve(),this.get(e))}))}isResolved(e){if(this.get(e))return!0;const t=this._factories.get(e);return!(t&&!t.isResolved)}setColorMap(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}};class r extends i.Disposable{get isResolved(){return this._isResolved}constructor(e,t,n){super(),this._registry=e,this._languageId=t,this._factory=n,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}resolve(){return o(this,void 0,void 0,(function*(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise}))}_create(){return o(this,void 0,void 0,(function*(){const e=yield this._factory.tokenizationSupport;this._isResolved=!0,e&&!this._isDisposed&&this._register(this._registry.register(this._languageId,e))}))}}})),r(e[60],t([19,61]),(function(e,t){return e.create("vs/base/common/platform",t)})),r(e[17],t([0,1,60]),(function(e,t,n){"use strict";var i;Object.defineProperty(t,"__esModule",{value:!0}),t.isAndroid=t.isEdge=t.isSafari=t.isFirefox=t.isChrome=t.isLittleEndian=t.OS=t.setTimeout0=t.setTimeout0IsFaster=t.language=t.userAgent=t.isMobile=t.isIOS=t.isWebWorker=t.isWeb=t.isNative=t.isLinux=t.isMacintosh=t.isWindows=t.globals=t.LANGUAGE_DEFAULT=void 0,t.LANGUAGE_DEFAULT="en";let r,s,o,a,l=!1,u=!1,c=!1,h=!1,d=!1,f=!1,g=!1,m=!1,p=!1,_=!1,b=t.LANGUAGE_DEFAULT,v=t.LANGUAGE_DEFAULT;t.globals="object"==typeof self?self:"object"==typeof global?global:{},typeof t.globals.vscode<"u"&&typeof t.globals.vscode.process<"u"?a=t.globals.vscode.process:typeof process<"u"&&(a=process);const y="string"==typeof(null===(i=a?.versions)||void 0===i?void 0:i.electron);if("object"!=typeof navigator||y&&"renderer"===a?.type)if("object"==typeof a){l="win32"===a.platform,u="darwin"===a.platform,c="linux"===a.platform,h=c&&!!a.env.SNAP&&!!a.env.SNAP_REVISION,g=y,p=!!a.env.CI||!!a.env.BUILD_ARTIFACTSTAGINGDIRECTORY,r=t.LANGUAGE_DEFAULT,b=t.LANGUAGE_DEFAULT;const e=a.env.VSCODE_NLS_CONFIG;if(e)try{const n=JSON.parse(e),i=n.availableLanguages["*"];r=n.locale,v=n.osLocale,b=i||t.LANGUAGE_DEFAULT,s=n._translationsConfigFile}catch{}d=!0}else console.error("Unable to resolve platform.");else o=navigator.userAgent,l=o.indexOf("Windows")>=0,u=o.indexOf("Macintosh")>=0,m=(o.indexOf("Macintosh")>=0||o.indexOf("iPad")>=0||o.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,c=o.indexOf("Linux")>=0,_=o?.indexOf("Mobi")>=0,f=!0,r=n.getConfiguredDefaultLocale(n.localize(0,null))||t.LANGUAGE_DEFAULT,b=r,v=navigator.language;let C=0;u?C=1:l?C=3:c&&(C=2),t.isWindows=l,t.isMacintosh=u,t.isLinux=c,t.isNative=d,t.isWeb=f,t.isWebWorker=f&&"function"==typeof t.globals.importScripts,t.isIOS=m,t.isMobile=_,t.userAgent=o,t.language=b,t.setTimeout0IsFaster="function"==typeof t.globals.postMessage&&!t.globals.importScripts,t.setTimeout0=(()=>{if(t.setTimeout0IsFaster){const e=[];t.globals.addEventListener("message",(t=>{if(t.data&&t.data.vscodeScheduleAsyncWork)for(let n=0,i=e.length;n<i;n++){const i=e[n];if(i.id===t.data.vscodeScheduleAsyncWork)return e.splice(n,1),void i.callback()}}));let n=0;return i=>{const r=++n;e.push({id:r,callback:i}),t.globals.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})(),t.OS=u||m?2:l?1:3;let L=!0,E=!1;t.isLittleEndian=function(){if(!E){E=!0;const e=new Uint8Array(2);e[0]=1,e[1]=2,L=513===new Uint16Array(e.buffer)[0]}return L},t.isChrome=!!(t.userAgent&&t.userAgent.indexOf("Chrome")>=0),t.isFirefox=!!(t.userAgent&&t.userAgent.indexOf("Firefox")>=0),t.isSafari=!!(!t.isChrome&&t.userAgent&&t.userAgent.indexOf("Safari")>=0),t.isEdge=!!(t.userAgent&&t.userAgent.indexOf("Edg/")>=0),t.isAndroid=!!(t.userAgent&&t.userAgent.indexOf("Android")>=0)})),r(e[62],t([0,1,17]),(function(e,t,n){"use strict";let i;if(Object.defineProperty(t,"__esModule",{value:!0}),t.platform=t.env=t.cwd=void 0,typeof n.globals.vscode<"u"&&typeof n.globals.vscode.process<"u"){const e=n.globals.vscode.process;i={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd:()=>e.cwd()}}else i=typeof process<"u"?{get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd:()=>process.env.VSCODE_CWD||process.cwd()}:{get platform(){return n.isWindows?"win32":n.isMacintosh?"darwin":"linux"},get arch(){},get env(){return{}},cwd:()=>"/"};t.cwd=i.cwd,t.env=i.env,t.platform=i.platform})),r(e[63],t([0,1,62]),(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sep=t.extname=t.basename=t.dirname=t.relative=t.resolve=t.normalize=t.posix=t.win32=void 0;const i=46,r=47,s=92,o=58;class a extends Error{constructor(e,t,n){let i;"string"==typeof t&&0===t.indexOf("not ")?(i="must not be",t=t.replace(/^not /,"")):i="must be";const r=-1!==e.indexOf(".")?"property":"argument";let s=`The "${e}" ${r} ${i} of type ${t}`;s+=". Received type "+typeof n,super(s),this.code="ERR_INVALID_ARG_TYPE"}}function l(e,t){if("string"!=typeof e)throw new a(t,"string",e)}const u="win32"===n.platform;function c(e){return e===r||e===s}function h(e){return e===r}function d(e){return e>=65&&e<=90||e>=97&&e<=122}function f(e,t,n,s){let o="",a=0,l=-1,u=0,c=0;for(let h=0;h<=e.length;++h){if(h<e.length)c=e.charCodeAt(h);else{if(s(c))break;c=r}if(s(c)){if(l!==h-1&&1!==u)if(2===u){if(o.length<2||2!==a||o.charCodeAt(o.length-1)!==i||o.charCodeAt(o.length-2)!==i){if(o.length>2){const e=o.lastIndexOf(n);-1===e?(o="",a=0):(o=o.slice(0,e),a=o.length-1-o.lastIndexOf(n)),l=h,u=0;continue}if(0!==o.length){o="",a=0,l=h,u=0;continue}}t&&(o+=o.length>0?`${n}..`:"..",a=2)}else o.length>0?o+=`${n}${e.slice(l+1,h)}`:o=e.slice(l+1,h),a=h-l-1;l=h,u=0}else c===i&&-1!==u?++u:u=-1}return o}function g(e,t){!function(e,t){if(null===e||"object"!=typeof e)throw new a("pathObject","Object",e)}(t);const n=t.dir||t.root,i=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${i}`:`${n}${e}${i}`:i}t.win32={resolve(...e){let t="",i="",r=!1;for(let a=e.length-1;a>=-1;a--){let u;if(a>=0){if(u=e[a],l(u,"path"),0===u.length)continue}else 0===t.length?u=n.cwd():(u=n.env[`=${t}`]||n.cwd(),(void 0===u||u.slice(0,2).toLowerCase()!==t.toLowerCase()&&u.charCodeAt(2)===s)&&(u=`${t}\\`));const h=u.length;let f=0,g="",m=!1;const p=u.charCodeAt(0);if(1===h)c(p)&&(f=1,m=!0);else if(c(p))if(m=!0,c(u.charCodeAt(1))){let e=2,t=e;for(;e<h&&!c(u.charCodeAt(e));)e++;if(e<h&&e!==t){const n=u.slice(t,e);for(t=e;e<h&&c(u.charCodeAt(e));)e++;if(e<h&&e!==t){for(t=e;e<h&&!c(u.charCodeAt(e));)e++;(e===h||e!==t)&&(g=`\\\\${n}\\${u.slice(t,e)}`,f=e)}}}else f=1;else d(p)&&u.charCodeAt(1)===o&&(g=u.slice(0,2),f=2,h>2&&c(u.charCodeAt(2))&&(m=!0,f=3));if(g.length>0)if(t.length>0){if(g.toLowerCase()!==t.toLowerCase())continue}else t=g;if(r){if(t.length>0)break}else if(i=`${u.slice(f)}\\${i}`,r=m,m&&t.length>0)break}return i=f(i,!r,"\\",c),r?`${t}\\${i}`:`${t}${i}`||"."},normalize(e){l(e,"path");const t=e.length;if(0===t)return".";let n,i=0,r=!1;const s=e.charCodeAt(0);if(1===t)return h(s)?"\\":e;if(c(s))if(r=!0,c(e.charCodeAt(1))){let r=2,s=r;for(;r<t&&!c(e.charCodeAt(r));)r++;if(r<t&&r!==s){const o=e.slice(s,r);for(s=r;r<t&&c(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&!c(e.charCodeAt(r));)r++;if(r===t)return`\\\\${o}\\${e.slice(s)}\\`;r!==s&&(n=`\\\\${o}\\${e.slice(s,r)}`,i=r)}}}else i=1;else d(s)&&e.charCodeAt(1)===o&&(n=e.slice(0,2),i=2,t>2&&c(e.charCodeAt(2))&&(r=!0,i=3));let a=i<t?f(e.slice(i),!r,"\\",c):"";return 0===a.length&&!r&&(a="."),a.length>0&&c(e.charCodeAt(t-1))&&(a+="\\"),void 0===n?r?`\\${a}`:a:r?`${n}\\${a}`:`${n}${a}`},isAbsolute(e){l(e,"path");const t=e.length;if(0===t)return!1;const n=e.charCodeAt(0);return c(n)||t>2&&d(n)&&e.charCodeAt(1)===o&&c(e.charCodeAt(2))},join(...e){if(0===e.length)return".";let n,i;for(let t=0;t<e.length;++t){const r=e[t];l(r,"path"),r.length>0&&(void 0===n?n=i=r:n+=`\\${r}`)}if(void 0===n)return".";let r=!0,s=0;if("string"==typeof i&&c(i.charCodeAt(0))){++s;const e=i.length;e>1&&c(i.charCodeAt(1))&&(++s,e>2&&(c(i.charCodeAt(2))?++s:r=!1))}if(r){for(;s<n.length&&c(n.charCodeAt(s));)s++;s>=2&&(n=`\\${n.slice(s)}`)}return t.win32.normalize(n)},relative(e,n){if(l(e,"from"),l(n,"to"),e===n)return"";const i=t.win32.resolve(e),r=t.win32.resolve(n);if(i===r||(e=i.toLowerCase())===(n=r.toLowerCase()))return"";let o=0;for(;o<e.length&&e.charCodeAt(o)===s;)o++;let a=e.length;for(;a-1>o&&e.charCodeAt(a-1)===s;)a--;const u=a-o;let c=0;for(;c<n.length&&n.charCodeAt(c)===s;)c++;let h=n.length;for(;h-1>c&&n.charCodeAt(h-1)===s;)h--;const d=h-c,f=u<d?u:d;let g=-1,m=0;for(;m<f;m++){const t=e.charCodeAt(o+m);if(t!==n.charCodeAt(c+m))break;t===s&&(g=m)}if(m!==f){if(-1===g)return r}else{if(d>f){if(n.charCodeAt(c+m)===s)return r.slice(c+m+1);if(2===m)return r.slice(c+m)}u>f&&(e.charCodeAt(o+m)===s?g=m:2===m&&(g=3)),-1===g&&(g=0)}let p="";for(m=o+g+1;m<=a;++m)(m===a||e.charCodeAt(m)===s)&&(p+=0===p.length?"..":"\\..");return c+=g,p.length>0?`${p}${r.slice(c,h)}`:(r.charCodeAt(c)===s&&++c,r.slice(c,h))},toNamespacedPath(e){if("string"!=typeof e||0===e.length)return e;const n=t.win32.resolve(e);if(n.length<=2)return e;if(n.charCodeAt(0)===s){if(n.charCodeAt(1)===s){const e=n.charCodeAt(2);if(63!==e&&e!==i)return`\\\\?\\UNC\\${n.slice(2)}`}}else if(d(n.charCodeAt(0))&&n.charCodeAt(1)===o&&n.charCodeAt(2)===s)return`\\\\?\\${n}`;return e},dirname(e){l(e,"path");const t=e.length;if(0===t)return".";let n=-1,i=0;const r=e.charCodeAt(0);if(1===t)return c(r)?e:".";if(c(r)){if(n=i=1,c(e.charCodeAt(1))){let r=2,s=r;for(;r<t&&!c(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&c(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&!c(e.charCodeAt(r));)r++;if(r===t)return e;r!==s&&(n=i=r+1)}}}}else d(r)&&e.charCodeAt(1)===o&&(n=t>2&&c(e.charCodeAt(2))?3:2,i=n);let s=-1,a=!0;for(let n=t-1;n>=i;--n)if(c(e.charCodeAt(n))){if(!a){s=n;break}}else a=!1;if(-1===s){if(-1===n)return".";s=n}return e.slice(0,s)},basename(e,t){void 0!==t&&l(t,"ext"),l(e,"path");let n,i=0,r=-1,s=!0;if(e.length>=2&&d(e.charCodeAt(0))&&e.charCodeAt(1)===o&&(i=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=i;--n){const l=e.charCodeAt(n);if(c(l)){if(!s){i=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(r=n):(o=-1,r=a))}return i===r?r=a:-1===r&&(r=e.length),e.slice(i,r)}for(n=e.length-1;n>=i;--n)if(c(e.charCodeAt(n))){if(!s){i=n+1;break}}else-1===r&&(s=!1,r=n+1);return-1===r?"":e.slice(i,r)},extname(e){l(e,"path");let t=0,n=-1,r=0,s=-1,a=!0,u=0;e.length>=2&&e.charCodeAt(1)===o&&d(e.charCodeAt(0))&&(t=r=2);for(let o=e.length-1;o>=t;--o){const t=e.charCodeAt(o);if(c(t)){if(!a){r=o+1;break}}else-1===s&&(a=!1,s=o+1),t===i?-1===n?n=o:1!==u&&(u=1):-1!==n&&(u=-1)}return-1===n||-1===s||0===u||1===u&&n===s-1&&n===r+1?"":e.slice(n,s)},format:g.bind(null,"\\"),parse(e){l(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.length;let r=0,s=e.charCodeAt(0);if(1===n)return c(s)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(c(s)){if(r=1,c(e.charCodeAt(1))){let t=2,i=t;for(;t<n&&!c(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&c(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&!c(e.charCodeAt(t));)t++;t===n?r=t:t!==i&&(r=t+1)}}}}else if(d(s)&&e.charCodeAt(1)===o){if(n<=2)return t.root=t.dir=e,t;if(r=2,c(e.charCodeAt(2))){if(3===n)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let a=-1,u=r,h=-1,f=!0,g=e.length-1,m=0;for(;g>=r;--g)if(s=e.charCodeAt(g),c(s)){if(!f){u=g+1;break}}else-1===h&&(f=!1,h=g+1),s===i?-1===a?a=g:1!==m&&(m=1):-1!==a&&(m=-1);return-1!==h&&(-1===a||0===m||1===m&&a===h-1&&a===u+1?t.base=t.name=e.slice(u,h):(t.name=e.slice(u,a),t.base=e.slice(u,h),t.ext=e.slice(a,h))),t.dir=u>0&&u!==r?e.slice(0,u-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null};const m=(()=>{if(u){const e=/\\/g;return()=>{const t=n.cwd().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>n.cwd()})();t.posix={resolve(...e){let t="",n=!1;for(let i=e.length-1;i>=-1&&!n;i--){const s=i>=0?e[i]:m();l(s,"path"),0!==s.length&&(t=`${s}/${t}`,n=s.charCodeAt(0)===r)}return t=f(t,!n,"/",h),n?`/${t}`:t.length>0?t:"."},normalize(e){if(l(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===r,n=e.charCodeAt(e.length-1)===r;return 0===(e=f(e,!t,"/",h)).length?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute:e=>(l(e,"path"),e.length>0&&e.charCodeAt(0)===r),join(...e){if(0===e.length)return".";let n;for(let t=0;t<e.length;++t){const i=e[t];l(i,"path"),i.length>0&&(void 0===n?n=i:n+=`/${i}`)}return void 0===n?".":t.posix.normalize(n)},relative(e,n){if(l(e,"from"),l(n,"to"),e===n||(e=t.posix.resolve(e))===(n=t.posix.resolve(n)))return"";const i=e.length,s=i-1,o=n.length-1,a=s<o?s:o;let u=-1,c=0;for(;c<a;c++){const t=e.charCodeAt(1+c);if(t!==n.charCodeAt(1+c))break;t===r&&(u=c)}if(c===a)if(o>a){if(n.charCodeAt(1+c)===r)return n.slice(1+c+1);if(0===c)return n.slice(1+c)}else s>a&&(e.charCodeAt(1+c)===r?u=c:0===c&&(u=0));let h="";for(c=1+u+1;c<=i;++c)(c===i||e.charCodeAt(c)===r)&&(h+=0===h.length?"..":"/..");return`${h}${n.slice(1+u)}`},toNamespacedPath:e=>e,dirname(e){if(l(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===r;let n=-1,i=!0;for(let t=e.length-1;t>=1;--t)if(e.charCodeAt(t)===r){if(!i){n=t;break}}else i=!1;return-1===n?t?"/":".":t&&1===n?"//":e.slice(0,n)},basename(e,t){void 0!==t&&l(t,"ext"),l(e,"path");let n,i=0,s=-1,o=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,l=-1;for(n=e.length-1;n>=0;--n){const u=e.charCodeAt(n);if(u===r){if(!o){i=n+1;break}}else-1===l&&(o=!1,l=n+1),a>=0&&(u===t.charCodeAt(a)?-1==--a&&(s=n):(a=-1,s=l))}return i===s?s=l:-1===s&&(s=e.length),e.slice(i,s)}for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===r){if(!o){i=n+1;break}}else-1===s&&(o=!1,s=n+1);return-1===s?"":e.slice(i,s)},extname(e){l(e,"path");let t=-1,n=0,s=-1,o=!0,a=0;for(let l=e.length-1;l>=0;--l){const u=e.charCodeAt(l);if(u!==r)-1===s&&(o=!1,s=l+1),u===i?-1===t?t=l:1!==a&&(a=1):-1!==t&&(a=-1);else if(!o){n=l+1;break}}return-1===t||-1===s||0===a||1===a&&t===s-1&&t===n+1?"":e.slice(t,s)},format:g.bind(null,"/"),parse(e){l(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.charCodeAt(0)===r;let s;n?(t.root="/",s=1):s=0;let o=-1,a=0,u=-1,c=!0,h=e.length-1,d=0;for(;h>=s;--h){const t=e.charCodeAt(h);if(t!==r)-1===u&&(c=!1,u=h+1),t===i?-1===o?o=h:1!==d&&(d=1):-1!==o&&(d=-1);else if(!c){a=h+1;break}}if(-1!==u){const i=0===a&&n?1:a;-1===o||0===d||1===d&&o===u-1&&o===a+1?t.base=t.name=e.slice(i,u):(t.name=e.slice(i,o),t.base=e.slice(i,u),t.ext=e.slice(o,u))}return a>0?t.dir=e.slice(0,a-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null},t.posix.win32=t.win32.win32=t.win32,t.posix.posix=t.win32.posix=t.posix,t.normalize=u?t.win32.normalize:t.posix.normalize,t.resolve=u?t.win32.resolve:t.posix.resolve,t.relative=u?t.win32.relative:t.posix.relative,t.dirname=u?t.win32.dirname:t.posix.dirname,t.basename=u?t.win32.basename:t.posix.basename,t.extname=u?t.win32.extname:t.posix.extname,t.sep=u?t.win32.sep:t.posix.sep})),r(e[18],t([0,1,63,17]),(function(e,t,n,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uriToFsPath=t.URI=void 0;const r=/^\w[\w\d+.-]*$/,s=/^\//,o=/^\/\//,a="",l="/",u=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class c{static isUri(e){return e instanceof c||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}constructor(e,t,n,i,u,c=!1){"object"==typeof e?(this.scheme=e.scheme||a,this.authority=e.authority||a,this.path=e.path||a,this.query=e.query||a,this.fragment=e.fragment||a):(this.scheme=function(e,t){return e||t?e:"file"}(e,c),this.authority=t||a,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==l&&(t=l+t):t=l}return t}(this.scheme,n||a),this.query=i||a,this.fragment=u||a,function(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!r.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!s.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(o.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,c))}get fsPath(){return p(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:i,query:r,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=a),void 0===n?n=this.authority:null===n&&(n=a),void 0===i?i=this.path:null===i&&(i=a),void 0===r?r=this.query:null===r&&(r=a),void 0===s?s=this.fragment:null===s&&(s=a),t===this.scheme&&n===this.authority&&i===this.path&&r===this.query&&s===this.fragment?this:new d(t,n,i,r,s)}static parse(e,t=!1){const n=u.exec(e);return n?new d(n[2]||a,y(n[4]||a),y(n[5]||a),y(n[7]||a),y(n[9]||a),t):new d(a,a,a,a,a)}static file(e){let t=a;if(i.isWindows&&(e=e.replace(/\\/g,l)),e[0]===l&&e[1]===l){const n=e.indexOf(l,2);-1===n?(t=e.substring(2),e=l):(t=e.substring(2,n),e=e.substring(n)||l)}return new d("file",t,e,a,a)}static from(e,t){return new d(e.scheme,e.authority,e.path,e.query,e.fragment,t)}static joinPath(e,...t){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return r=i.isWindows&&"file"===e.scheme?c.file(n.win32.join(p(e,!0),...t)).path:n.posix.join(e.path,...t),e.with({path:r})}toString(e=!1){return _(this,e)}toJSON(){return this}static revive(e){var t,n;if(e){if(e instanceof c)return e;{const i=new d(e);return i._formatted=null!==(t=e.external)&&void 0!==t?t:null,i._fsPath=e._sep===h&&null!==(n=e.fsPath)&&void 0!==n?n:null,i}}return e}}t.URI=c;const h=i.isWindows?1:void 0;class d extends c{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=p(this,!1)),this._fsPath}toString(e=!1){return e?_(this,!0):(this._formatted||(this._formatted=_(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=h),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const f={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function g(e,t,n){let i,r=-1;for(let s=0;s<e.length;s++){const o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o||n&&91===o||n&&93===o||n&&58===o)-1!==r&&(i+=encodeURIComponent(e.substring(r,s)),r=-1),void 0!==i&&(i+=e.charAt(s));else{void 0===i&&(i=e.substr(0,s));const t=f[o];void 0!==t?(-1!==r&&(i+=encodeURIComponent(e.substring(r,s)),r=-1),i+=t):-1===r&&(r=s)}}return-1!==r&&(i+=encodeURIComponent(e.substring(r))),void 0!==i?i:e}function m(e){let t;for(let n=0;n<e.length;n++){const i=e.charCodeAt(n);35===i||63===i?(void 0===t&&(t=e.substr(0,n)),t+=f[i]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function p(e,t){let n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,i.isWindows&&(n=n.replace(/\//g,"\\")),n}function _(e,t){const n=t?m:g;let i="",{scheme:r,authority:s,path:o,query:a,fragment:u}=e;if(r&&(i+=r,i+=":"),(s||"file"===r)&&(i+=l,i+=l),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.lastIndexOf(":"),-1===e?i+=n(t,!1,!1):(i+=n(t.substr(0,e),!1,!1),i+=":",i+=n(t.substr(e+1),!1,!0)),i+="@"}s=s.toLowerCase(),e=s.lastIndexOf(":"),-1===e?i+=n(s,!1,!0):(i+=n(s.substr(0,e),!1,!0),i+=s.substr(e))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2)){const e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&58===o.charCodeAt(1)){const e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}i+=n(o,!0,!1)}return a&&(i+="?",i+=n(a,!1,!1)),u&&(i+="#",i+=t?u:g(u,!1,!1)),i}function b(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+b(e.substr(3)):e}}t.uriToFsPath=p;const v=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function y(e){return e.match(v)?e.replace(v,(e=>b(e))):e}})),r(e[67],t([0,1,5,9,13,14,17,6]),(function(e,t,n,i,r,s,o,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.create=t.SimpleWorkerServer=t.SimpleWorkerClient=t.logOnceWebWorkerWarning=void 0;const l="$initialize";let u=!1;t.logOnceWebWorkerWarning=function(e){o.isWeb&&(u||(u=!0,console.warn("Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/microsoft/monaco-editor#faq")),console.warn(e.message))};class c{constructor(e,t,n,i){this.vsWorker=e,this.req=t,this.method=n,this.args=i,this.type=0}}class h{constructor(e,t,n,i){this.vsWorker=e,this.seq=t,this.res=n,this.err=i,this.type=1}}class d{constructor(e,t,n,i){this.vsWorker=e,this.req=t,this.eventName=n,this.arg=i,this.type=2}}class f{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}}class g{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}}class m{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t){const n=String(++this._lastSentReq);return new Promise(((i,r)=>{this._pendingReplies[n]={resolve:i,reject:r},this._send(new c(this._workerId,n,e,t))}))}listen(e,t){let n=null;const r=new i.Emitter({onWillAddFirstListener:()=>{n=String(++this._lastSentReq),this._pendingEmitters.set(n,r),this._send(new d(this._workerId,n,e,t))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(n),this._send(new g(this._workerId,n)),n=null}});return r.event}handleMessage(e){!e||!e.vsWorker||-1!==this._workerId&&e.vsWorker!==this._workerId||this._handleMessage(e)}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq])return void console.warn("Got reply to unknown seq");const t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;return e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),void t.reject(n)}t.resolve(e.res)}_handleRequestMessage(e){const t=e.req;this._handler.handleMessage(e.method,e.args).then((e=>{this._send(new h(this._workerId,t,e,void 0))}),(e=>{e.detail instanceof Error&&(e.detail=(0,n.transformErrorForSerialization)(e.detail)),this._send(new h(this._workerId,t,void 0,(0,n.transformErrorForSerialization)(e)))}))}_handleSubscribeEventMessage(e){const t=e.req,n=this._handler.handleEvent(e.eventName,e.arg)((e=>{this._send(new f(this._workerId,t,e))}));this._pendingEvents.set(t,n)}_handleEventMessage(e){this._pendingEmitters.has(e.req)?this._pendingEmitters.get(e.req).fire(e.event):console.warn("Got event for unknown req")}_handleUnsubscribeEventMessage(e){this._pendingEvents.has(e.req)?(this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req)):console.warn("Got unsubscribe for unknown req")}_send(e){const t=[];if(0===e.type)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else 1===e.type&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}}class p extends r.Disposable{constructor(e,t,n){super();let i=null;this._worker=this._register(e.create("vs/base/common/worker/simpleWorker",(e=>{this._protocol.handleMessage(e)}),(e=>{i?.(e)}))),this._protocol=new m({sendMessage:(e,t)=>{this._worker.postMessage(e,t)},handleMessage:(e,t)=>{if("function"!=typeof n[e])return Promise.reject(new Error("Missing method "+e+" on main thread host."));try{return Promise.resolve(n[e].apply(n,t))}catch(e){return Promise.reject(e)}},handleEvent:(e,t)=>{if(b(e)){const i=n[e].call(n,t);if("function"!=typeof i)throw new Error(`Missing dynamic event ${e} on main thread host.`);return i}if(_(e)){const t=n[e];if("function"!=typeof t)throw new Error(`Missing event ${e} on main thread host.`);return t}throw new Error(`Malformed event name ${e}`)}}),this._protocol.setWorkerId(this._worker.getId());let r=null;const o=globalThis.require;typeof o<"u"&&"function"==typeof o.getConfig?r=o.getConfig():typeof globalThis.requirejs<"u"&&(r=globalThis.requirejs.s.contexts._.config);const a=(0,s.getAllMethodNames)(n);this._onModuleLoaded=this._protocol.sendMessage(l,[this._worker.getId(),JSON.parse(JSON.stringify(r)),t,a]);const u=(e,t)=>this._request(e,t),c=(e,t)=>this._protocol.listen(e,t);this._lazyProxy=new Promise(((e,n)=>{i=n,this._onModuleLoaded.then((t=>{e(v(t,u,c))}),(e=>{n(e),this._onError("Worker failed to load "+t,e)}))}))}getProxyObject(){return this._lazyProxy}_request(e,t){return new Promise(((n,i)=>{this._onModuleLoaded.then((()=>{this._protocol.sendMessage(e,t).then(n,i)}),i)}))}_onError(e,t){console.error(e),console.info(t)}}function _(e){return"o"===e[0]&&"n"===e[1]&&a.isUpperAsciiLetter(e.charCodeAt(2))}function b(e){return/^onDynamic/.test(e)&&a.isUpperAsciiLetter(e.charCodeAt(9))}function v(e,t,n){const i=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},r=e=>function(t){return n(e,t)},s={};for(const t of e)b(t)?s[t]=r(t):_(t)?s[t]=n(t,void 0):s[t]=i(t);return s}t.SimpleWorkerClient=p;class y{constructor(e,t){this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new m({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t)=>this._handleMessage(e,t),handleEvent:(e,t)=>this._handleEvent(e,t)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t){if(e===l)return this.initialize(t[0],t[1],t[2],t[3]);if(!this._requestHandler||"function"!=typeof this._requestHandler[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._requestHandler[e].apply(this._requestHandler,t))}catch(e){return Promise.reject(e)}}_handleEvent(e,t){if(!this._requestHandler)throw new Error("Missing requestHandler");if(b(e)){const n=this._requestHandler[e].call(this._requestHandler,t);if("function"!=typeof n)throw new Error(`Missing dynamic event ${e} on request handler.`);return n}if(_(e)){const t=this._requestHandler[e];if("function"!=typeof t)throw new Error(`Missing event ${e} on request handler.`);return t}throw new Error(`Malformed event name ${e}`)}initialize(t,n,i,r){this._protocol.setWorkerId(t);const o=v(r,((e,t)=>this._protocol.sendMessage(e,t)),((e,t)=>this._protocol.listen(e,t)));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(o),Promise.resolve((0,s.getAllMethodNames)(this._requestHandler))):(n&&(typeof n.baseUrl<"u"&&delete n.baseUrl,typeof n.paths<"u"&&typeof n.paths.vs<"u"&&delete n.paths.vs,void 0!==typeof n.trustedTypesPolicy&&delete n.trustedTypesPolicy,n.catchError=!0,globalThis.require.config(n)),new Promise(((t,n)=>{(globalThis.require||e)([i],(e=>{this._requestHandler=e.create(o),this._requestHandler?t((0,s.getAllMethodNames)(this._requestHandler)):n(new Error("No RequestHandler!"))}),n)})))}}t.SimpleWorkerServer=y,t.create=function(e){return new y(e,null)}})),r(e[64],t([19,61]),(function(e,t){return e.create("vs/editor/common/languages",t)})),r(e[65],t([0,1,40,18,2,59,64]),(function(e,t,n,i,r,s,o){"use strict";var a,l,u,c,h,d,f,g;Object.defineProperty(t,"__esModule",{value:!0}),t.TokenizationRegistry=t.LazyTokenizationSupport=t.InlayHintKind=t.Command=t.FoldingRangeKind=t.TextEdit=t.SymbolKinds=t.getAriaLabelForSymbol=t.symbolKindNames=t.isLocationLink=t.DocumentHighlightKind=t.SignatureHelpTriggerKind=t.SelectedSuggestionInfo=t.InlineCompletionTriggerKind=t.CompletionItemKinds=t.EncodedTokenizationResult=t.TokenizationResult=t.Token=void 0,t.Token=class{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}},t.TokenizationResult=class{constructor(e,t){this.tokens=e,this.endState=t,this._tokenizationResultBrand=void 0}},t.EncodedTokenizationResult=class{constructor(e,t){this.tokens=e,this.endState=t,this._encodedTokenizationResultBrand=void 0}},function(e){const t=new Map;t.set(0,n.Codicon.symbolMethod),t.set(1,n.Codicon.symbolFunction),t.set(2,n.Codicon.symbolConstructor),t.set(3,n.Codicon.symbolField),t.set(4,n.Codicon.symbolVariable),t.set(5,n.Codicon.symbolClass),t.set(6,n.Codicon.symbolStruct),t.set(7,n.Codicon.symbolInterface),t.set(8,n.Codicon.symbolModule),t.set(9,n.Codicon.symbolProperty),t.set(10,n.Codicon.symbolEvent),t.set(11,n.Codicon.symbolOperator),t.set(12,n.Codicon.symbolUnit),t.set(13,n.Codicon.symbolValue),t.set(15,n.Codicon.symbolEnum),t.set(14,n.Codicon.symbolConstant),t.set(15,n.Codicon.symbolEnum),t.set(16,n.Codicon.symbolEnumMember),t.set(17,n.Codicon.symbolKeyword),t.set(27,n.Codicon.symbolSnippet),t.set(18,n.Codicon.symbolText),t.set(19,n.Codicon.symbolColor),t.set(20,n.Codicon.symbolFile),t.set(21,n.Codicon.symbolReference),t.set(22,n.Codicon.symbolCustomColor),t.set(23,n.Codicon.symbolFolder),t.set(24,n.Codicon.symbolTypeParameter),t.set(25,n.Codicon.account),t.set(26,n.Codicon.issues),e.toIcon=function(e){let i=t.get(e);return i||(console.info("No codicon found for CompletionItemKind "+e),i=n.Codicon.symbolProperty),i};const i=new Map;i.set("method",0),i.set("function",1),i.set("constructor",2),i.set("field",3),i.set("variable",4),i.set("class",5),i.set("struct",6),i.set("interface",7),i.set("module",8),i.set("property",9),i.set("event",10),i.set("operator",11),i.set("unit",12),i.set("value",13),i.set("constant",14),i.set("enum",15),i.set("enum-member",16),i.set("enumMember",16),i.set("keyword",17),i.set("snippet",27),i.set("text",18),i.set("color",19),i.set("file",20),i.set("reference",21),i.set("customcolor",22),i.set("folder",23),i.set("type-parameter",24),i.set("typeParameter",24),i.set("account",25),i.set("issue",26),e.fromString=function(e,t){let n=i.get(e);return typeof n>"u"&&!t&&(n=9),n}}(a||(t.CompletionItemKinds=a={})),(u=l||(t.InlineCompletionTriggerKind=l={}))[u.Automatic=0]="Automatic",u[u.Explicit=1]="Explicit",t.SelectedSuggestionInfo=class{constructor(e,t,n,i){this.range=e,this.text=t,this.completionKind=n,this.isSnippetText=i}equals(e){return r.Range.lift(this.range).equalsRange(e.range)&&this.text===e.text&&this.completionKind===e.completionKind&&this.isSnippetText===e.isSnippetText}},function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(c||(t.SignatureHelpTriggerKind=c={})),function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(h||(t.DocumentHighlightKind=h={})),t.isLocationLink=function(e){return e&&i.URI.isUri(e.uri)&&r.Range.isIRange(e.range)&&(r.Range.isIRange(e.originSelectionRange)||r.Range.isIRange(e.targetSelectionRange))},t.symbolKindNames={17:(0,o.localize)(0,null),16:(0,o.localize)(1,null),4:(0,o.localize)(2,null),13:(0,o.localize)(3,null),8:(0,o.localize)(4,null),9:(0,o.localize)(5,null),21:(0,o.localize)(6,null),23:(0,o.localize)(7,null),7:(0,o.localize)(8,null),0:(0,o.localize)(9,null),11:(0,o.localize)(10,null),10:(0,o.localize)(11,null),19:(0,o.localize)(12,null),5:(0,o.localize)(13,null),1:(0,o.localize)(14,null),2:(0,o.localize)(15,null),20:(0,o.localize)(16,null),15:(0,o.localize)(17,null),18:(0,o.localize)(18,null),24:(0,o.localize)(19,null),3:(0,o.localize)(20,null),6:(0,o.localize)(21,null),14:(0,o.localize)(22,null),22:(0,o.localize)(23,null),25:(0,o.localize)(24,null),12:(0,o.localize)(25,null)},t.getAriaLabelForSymbol=function(e,n){return(0,o.localize)(26,null,e,t.symbolKindNames[n])},function(e){const t=new Map;t.set(0,n.Codicon.symbolFile),t.set(1,n.Codicon.symbolModule),t.set(2,n.Codicon.symbolNamespace),t.set(3,n.Codicon.symbolPackage),t.set(4,n.Codicon.symbolClass),t.set(5,n.Codicon.symbolMethod),t.set(6,n.Codicon.symbolProperty),t.set(7,n.Codicon.symbolField),t.set(8,n.Codicon.symbolConstructor),t.set(9,n.Codicon.symbolEnum),t.set(10,n.Codicon.symbolInterface),t.set(11,n.Codicon.symbolFunction),t.set(12,n.Codicon.symbolVariable),t.set(13,n.Codicon.symbolConstant),t.set(14,n.Codicon.symbolString),t.set(15,n.Codicon.symbolNumber),t.set(16,n.Codicon.symbolBoolean),t.set(17,n.Codicon.symbolArray),t.set(18,n.Codicon.symbolObject),t.set(19,n.Codicon.symbolKey),t.set(20,n.Codicon.symbolNull),t.set(21,n.Codicon.symbolEnumMember),t.set(22,n.Codicon.symbolStruct),t.set(23,n.Codicon.symbolEvent),t.set(24,n.Codicon.symbolOperator),t.set(25,n.Codicon.symbolTypeParameter),e.toIcon=function(e){let i=t.get(e);return i||(console.info("No codicon found for SymbolKind "+e),i=n.Codicon.symbolProperty),i}}(d||(t.SymbolKinds=d={})),t.TextEdit=class{};class m{static fromValue(e){switch(e){case"comment":return m.Comment;case"imports":return m.Imports;case"region":return m.Region}return new m(e)}constructor(e){this.value=e}}t.FoldingRangeKind=m,m.Comment=new m("comment"),m.Imports=new m("imports"),m.Region=new m("region"),function(e){e.is=function(e){return!(!e||"object"!=typeof e)&&"string"==typeof e.id&&"string"==typeof e.title}}(f||(t.Command=f={})),function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(g||(t.InlayHintKind=g={})),t.LazyTokenizationSupport=class{constructor(e){this.createSupport=e,this._tokenizationSupport=null}dispose(){this._tokenizationSupport&&this._tokenizationSupport.then((e=>{e&&e.dispose()}))}get tokenizationSupport(){return this._tokenizationSupport||(this._tokenizationSupport=this.createSupport()),this._tokenizationSupport}},t.TokenizationRegistry=new s.TokenizationRegistry})),r(e[66],t([0,1,38,9,35,18,4,2,41,65,58]),(function(e,t,n,i,r,s,o,a,l,u,c){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createMonacoBaseAPI=t.KeyMod=void 0;class h{static chord(e,t){return(0,r.KeyChord)(e,t)}}t.KeyMod=h,h.CtrlCmd=2048,h.Shift=1024,h.Alt=512,h.WinCtrl=256,t.createMonacoBaseAPI=function(){return{editor:void 0,languages:void 0,CancellationTokenSource:n.CancellationTokenSource,Emitter:i.Emitter,KeyCode:c.KeyCode,KeyMod:h,Position:o.Position,Range:a.Range,Selection:l.Selection,SelectionDirection:c.SelectionDirection,MarkerSeverity:c.MarkerSeverity,MarkerTag:c.MarkerTag,Uri:s.URI,Token:u.Token}}})),r(e[68],t([0,1,24,18,4,2,55,28,51,52,66,23,57,49,14,50]),(function(e,t,n,i,r,s,a,l,u,c,h,d,f,g,m,p){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.create=t.EditorSimpleWorker=void 0;class _ extends a.MirrorTextModel{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this._lines.length;n++){const i=this._lines[n],s=this.offsetAt(new r.Position(n+1,1)),o=i.matchAll(e);for(const e of o)(e.index||0===e.index)&&(e.index=e.index+s),t.push(e)}return t}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){const n=(0,l.getWordAtText)(e.column,(0,l.ensureValidWordDefinition)(t),this._lines[e.lineNumber-1],0);return n?new s.Range(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){const t=this._lines,n=this._wordenize.bind(this);let i=0,r="",s=0,o=[];return{*[Symbol.iterator](){for(;;)if(s<o.length){const e=r.substring(o[s].start,o[s].end);s+=1,yield e}else{if(!(i<t.length))break;r=t[i],o=n(r,e),s=0,i+=1}}}}getLineWords(e,t){const n=this._lines[e-1],i=this._wordenize(n,t),r=[];for(const e of i)r.push({word:n.substring(e.start,e.end),startColumn:e.start+1,endColumn:e.end+1});return r}_wordenize(e,t){const n=[];let i;for(t.lastIndex=0;(i=t.exec(e))&&0!==i[0].length;)n.push({start:i.index,end:i.index+i[0].length});return n}getValueInRange(e){if((e=this._validateRange(e)).startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this._eol,n=e.startLineNumber-1,i=e.endLineNumber-1,r=[];r.push(this._lines[n].substring(e.startColumn-1));for(let e=n+1;e<i;e++)r.push(this._lines[e]);return r.push(this._lines[i].substring(0,e.endColumn-1)),r.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();const t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){const t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!r.Position.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,i=!1;if(t<1)t=1,n=1,i=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,i=!0;else{const e=this._lines[t-1].length+1;n<1?(n=1,i=!0):n>e&&(n=e,i=!0)}return i?{lineNumber:t,column:n}:e}}class b{constructor(e,t){this._host=e,this._models=Object.create(null),this._foreignModuleFactory=t,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(e){return this._models[e]}_getModels(){const e=[];return Object.keys(this._models).forEach((t=>e.push(this._models[t]))),e}acceptNewModel(e){this._models[e.url]=new _(i.URI.parse(e.url),e.lines,e.EOL,e.versionId)}acceptModelChanged(e,t){this._models[e]&&this._models[e].onEvents(t)}acceptRemovedModel(e){this._models[e]&&delete this._models[e]}computeUnicodeHighlights(e,t,n){return o(this,void 0,void 0,(function*(){const i=this._getModel(e);return i?f.UnicodeTextModelHighlighter.computeUnicodeHighlights(i,t,n):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}))}computeDiff(e,t,n,i){return o(this,void 0,void 0,(function*(){const r=this._getModel(e),s=this._getModel(t);return r&&s?b.computeDiff(r,s,n,i):null}))}static computeDiff(e,t,n,i){const r="advanced"===i?g.linesDiffComputers.getDefault():g.linesDiffComputers.getLegacy(),s=e.getLinesContent(),o=t.getLinesContent(),a=r.computeDiff(s,o,n);function l(e){return e.map((e=>{var t;return[e.original.startLineNumber,e.original.endLineNumberExclusive,e.modified.startLineNumber,e.modified.endLineNumberExclusive,null===(t=e.innerChanges)||void 0===t?void 0:t.map((e=>[e.originalRange.startLineNumber,e.originalRange.startColumn,e.originalRange.endLineNumber,e.originalRange.endColumn,e.modifiedRange.startLineNumber,e.modifiedRange.startColumn,e.modifiedRange.endLineNumber,e.modifiedRange.endColumn]))]}))}return{identical:!(a.changes.length>0)&&this._modelsAreIdentical(e,t),quitEarly:a.hitTimeout,changes:l(a.changes),moves:a.moves.map((e=>[e.lineRangeMapping.original.startLineNumber,e.lineRangeMapping.original.endLineNumberExclusive,e.lineRangeMapping.modified.startLineNumber,e.lineRangeMapping.modified.endLineNumberExclusive,l(e.changes)]))}}static _modelsAreIdentical(e,t){const n=e.getLineCount();if(n!==t.getLineCount())return!1;for(let i=1;i<=n;i++)if(e.getLineContent(i)!==t.getLineContent(i))return!1;return!0}computeMoreMinimalEdits(e,t,i){return o(this,void 0,void 0,(function*(){const r=this._getModel(e);if(!r)return t;const o=[];let a;t=t.slice(0).sort(((e,t)=>e.range&&t.range?s.Range.compareRangesUsingStarts(e.range,t.range):(e.range?0:1)-(t.range?0:1)));let l=0;for(let e=1;e<t.length;e++)s.Range.getEndPosition(t[l].range).equals(s.Range.getStartPosition(t[e].range))?(t[l].range=s.Range.fromPositions(s.Range.getStartPosition(t[l].range),s.Range.getEndPosition(t[e].range)),t[l].text+=t[e].text):(l++,t[l]=t[e]);t.length=l+1;for(let{range:e,text:l,eol:u}of t){if("number"==typeof u&&(a=u),s.Range.isEmpty(e)&&!l)continue;const t=r.getValueInRange(e);if(l=l.replace(/\r\n|\n|\r/g,r.eol),t===l)continue;if(Math.max(l.length,t.length)>b._diffLimit){o.push({range:e,text:l});continue}const c=(0,n.stringDiff)(t,l,i),h=r.offsetAt(s.Range.lift(e).getStartPosition());for(const e of c){const t=r.positionAt(h+e.originalStart),n=r.positionAt(h+e.originalStart+e.originalLength),i={text:l.substr(e.modifiedStart,e.modifiedLength),range:{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}};r.getValueInRange(i.range)!==i.text&&o.push(i)}}return"number"==typeof a&&o.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),o}))}computeLinks(e){return o(this,void 0,void 0,(function*(){const t=this._getModel(e);return t?(0,u.computeLinks)(t):null}))}computeDefaultDocumentColors(e){return o(this,void 0,void 0,(function*(){const t=this._getModel(e);return t?(0,p.computeDefaultDocumentColors)(t):null}))}textualSuggest(e,t,n,i){return o(this,void 0,void 0,(function*(){const r=new d.StopWatch,s=new RegExp(n,i),o=new Set;e:for(const n of e){const e=this._getModel(n);if(e)for(const n of e.words(s))if(n!==t&&isNaN(Number(n))&&(o.add(n),o.size>b._suggestionsLimit))break e}return{words:Array.from(o),duration:r.elapsed()}}))}computeWordRanges(e,t,n,i){return o(this,void 0,void 0,(function*(){const r=this._getModel(e);if(!r)return Object.create(null);const s=new RegExp(n,i),o=Object.create(null);for(let e=t.startLineNumber;e<t.endLineNumber;e++){const t=r.getLineWords(e,s);for(const n of t){if(!isNaN(Number(n.word)))continue;let t=o[n.word];t||(t=[],o[n.word]=t),t.push({startLineNumber:e,startColumn:n.startColumn,endLineNumber:e,endColumn:n.endColumn})}}return o}))}navigateValueSet(e,t,n,i,r){return o(this,void 0,void 0,(function*(){const s=this._getModel(e);if(!s)return null;const o=new RegExp(i,r);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});const a=s.getValueInRange(t),l=s.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},o);if(!l)return null;const u=s.getValueInRange(l);return c.BasicInplaceReplace.INSTANCE.navigateValueSet(t,a,l,u,n)}))}loadForeignModule(t,n,i){const r={host:(0,m.createProxyObject)(i,((e,t)=>this._host.fhr(e,t))),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(r,n),Promise.resolve((0,m.getAllMethodNames)(this._foreignModule))):new Promise(((i,s)=>{e([t],(e=>{this._foreignModule=e.create(r,n),i((0,m.getAllMethodNames)(this._foreignModule))}),s)}))}fmr(e,t){if(!this._foreignModule||"function"!=typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(e){return Promise.reject(e)}}}t.EditorSimpleWorker=b,b._diffLimit=1e5,b._suggestionsLimit=1e4,t.create=function(e){return new b(e,null)},"function"==typeof importScripts&&(globalThis.monaco=(0,h.createMonacoBaseAPI)())}))}).call(this);