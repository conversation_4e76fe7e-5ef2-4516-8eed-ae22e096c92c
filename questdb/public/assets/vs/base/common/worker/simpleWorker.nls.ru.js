/*! For license information please see simpleWorker.nls.ru.js.LICENSE.txt */
define("vs/base/common/worker/simpleWorker.nls.ru",{"vs/base/common/platform":["_"],"vs/editor/common/languages":["массив","логическое значение","класс","константа","конструктор","перечисление","элемент перечисления","событие","поле","файл","функция","интерфейс","ключ","метод","модуль","пространство имен","NULL","число","объект","оператор","пакет","свойство","строка","структура","параметр типа","Переменная","{0} ({1})"]});