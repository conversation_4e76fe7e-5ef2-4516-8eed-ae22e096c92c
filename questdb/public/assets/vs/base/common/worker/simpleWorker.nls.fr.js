/*! For license information please see simpleWorker.nls.fr.js.LICENSE.txt */
define("vs/base/common/worker/simpleWorker.nls.fr",{"vs/base/common/platform":["_"],"vs/editor/common/languages":["tableau","booléen","classe","constante","constructeur","énumération","membre d'énumération","événement","champ","fichier","fonction","interface","clé","méthode","module","espace de noms","NULL","nombre","objet","opérateur","package","propriété","chaîne","struct","paramètre de type","variable","{0} ({1})"]});