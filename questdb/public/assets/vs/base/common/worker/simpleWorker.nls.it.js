/*! For license information please see simpleWorker.nls.it.js.LICENSE.txt */
define("vs/base/common/worker/simpleWorker.nls.it",{"vs/base/common/platform":["_"],"vs/editor/common/languages":["matrice","valore booleano","classe","costante","costruttore","enumerazione","membro di enumerazione","evento","campo","file","funzione","interfaccia","chiave","metodo","modulo","spazio dei nomi","Null","numero","oggetto","operatore","pacchetto","proprietà","stringa","struct","parametro di tipo","variabile","{0} ({1})"]});