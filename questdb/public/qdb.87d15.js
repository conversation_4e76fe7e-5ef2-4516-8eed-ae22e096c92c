(()=>{var e,t,n,r={53905:(e,t,n)=>{"use strict";n(88146);var r=n(16896),a=n(97335),o=n(60081),i=n(15512),l=n(4527),s=n(76098);const c=(0,s.iv)(["\n  :root {\n    --docsearch-primary-color: ",";\n    --docsearch-text-color: ",";\n    --docsearch-highlight-color: ",";\n    --docsearch-muted-color: #a4a6a8;\n    --docsearch-container-background: rgba(0, 0, 0, 0.5);\n    /* Modal */\n    --docsearch-modal-background: ",";\n    --docsearch-modal-shadow: inset 1px 1px 0 0 #2c2e40, 0 3px 8px 0 #000309;\n    /* Search box */\n    --docsearch-searchbox-background: #2d303e;\n    --docsearch-searchbox-focus-background: #141725;\n    /* Hit */\n    --docsearch-hit-color: ",";\n    --docsearch-hit-active-color: ",";\n    --docsearch-hit-background: ",";\n    --docsearch-hit-shadow: none;\n    /* Footer */\n    --docsearch-footer-background: ",";\n    --docsearch-footer-shadow: inset 0 1px 0 0 rgba(73, 76, 106, 0.5),\n      0 -4px 8px 0 rgba(0, 0, 0, 0.2);\n    /* Keys */\n    --docsearch-key-gradient: linear-gradient(\n      -26.5deg,\n      #32343e 0%,\n      rgb(38, 40, 51) 100%\n    );\n    --docsearch-key-shadow: inset 0 -2px 0 0 #282d55, inset 0 0 1px 1px #51577d,\n      0 2px 2px 0 rgba(3, 4, 9, 0.3);\n\n    // Allotment styling\n    --focus-border: ",";\n    --separator-border: ",";\n    --sash-size: 1.5rem !important;\n    --sash-hover-size: 0.5rem;\n    .allotment-module_splitView__L-yRc.allotment-module_separatorBorder__x-rDS.allotment-module_vertical__WSwwa\n      > .allotment-module_splitViewContainer__rQnVa\n      > .allotment-module_splitViewView__MGZ6O:not(:first-child)::before {\n      height: 2px;\n    }\n    .allotment-module_splitView__L-yRc.allotment-module_separatorBorder__x-rDS.allotment-module_horizontal__7doS8\n      > .allotment-module_splitViewContainer__rQnVa\n      > .allotment-module_splitViewView__MGZ6O:not(:first-child)::before {\n      width: 2px;\n    }\n  }\n  .sash {\n    ::before {\n      transition: none;\n    }\n  }\n\n  .DocSearch-Button {\n    height: 3rem;\n    border-radius: 0.4rem;\n    color: ",";\n    border: 1px solid var(--docsearch-searchbox-background);\n  }\n\n  .DocSearch-Button:hover {\n    background: ",";\n    box-shadow: none;\n  }\n\n  .DocSearch-Button-Placeholder {\n    font-size: 100%;\n  }\n\n  .DocSearch-Button .DocSearch-Search-Icon {\n    color: ",";\n    width: 1.4rem;\n    height: 1.4rem;\n  }\n\n  .DocSearch-Button:hover .DocSearch-Search-Icon {\n    color: ",";\n  }\n\n  .DocSearch-Button-Key {\n    top: 0;\n    padding: 0 4px;\n    background: ",";\n    border-radius: 2px;\n    box-shadow: none;\n    color: ",";\n    font-size: 1.2rem;\n    font-style: inherit;\n    font-family: inherit;\n    font-weight: 600;\n  }\n\n  .DocSearch-Hit-title {\n    font-size: 100%;\n    overflow: hidden;\n  }\n\n  .DocSearch-Logo .cls-1,\n  .DocSearch-Logo .cls-2 {\n    fill: "," !important;\n  }\n"],(({theme:e})=>e.color.pink),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.pink),(({theme:e})=>e.color.background),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.backgroundLighter),(({theme:e})=>e.color.background),(({theme:e})=>e.color.comment),(({theme:e})=>e.color.backgroundDarker),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.comment),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.gray2),(({theme:e})=>e.color.black),(({theme:e})=>e.color.foreground)),d=(0,s.vJ)(["\n  ","\n\n  body {\n    color: ",";\n  }\n"],c,(({theme:e})=>e.color.foreground)),u=(0,s.F4)(["\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n"]),m=(0,s.iv)(["\n  animation: "," 1.5s cubic-bezier(0.62, 0.28, 0.23, 0.99) infinite;\n"],u),p=e=>new Promise(((t,n)=>{if(navigator.clipboard&&window.isSecureContext)setTimeout((()=>{navigator.clipboard.writeText(e).then(t).catch(n)}));else{const r=document.createElement("textarea");r.value=e,r.style.position="absolute",r.style.left="-999999px",document.body.prepend(r),r.select();try{document.execCommand("copy")?t():n(new Error("Copy command failed"))}catch(e){n(e)}finally{r.remove()}}}));var g=n(43176),f=n(82529);const h=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,b=(e,t)=>t?(0,g.WU)(new f.N9(e,h()),t):new f.N9(e,h()).toISOString();let E=function(e){return e.MAIN="telemetry",e.CONFIG="telemetry_config",e.WAL="sys.telemetry_wal",e}({});const y="https://fara.questdb.io",w="26px";var C=n(32981),v=n(50307),_=n(95886),x=n(17517),S=n(63137);const k=(e,t={},n=!0)=>{const r="/"===e[0]?"":"/",a=n?`${window.location.origin}${r}${e}`:e;return(0,v.U)(a,t).pipe((0,_.w)((e=>e.ok?e.headers.get("content-type")?.startsWith("application/json")?e.json():Promise.resolve({}):(0,C.of)({error:!0,message:e.statusText}))),(0,x.K)((e=>(console.error(e),(0,C.of)({error:!0,message:e.message})))),(0,S.U)((e=>e.error?e:{data:e,error:!1})))};let T=function(e){return e.DDL="ddl",e.DML="dml",e.DQL="dql",e.ERROR="error",e.NOTICE="notice",e}({}),N=function(e){return e.DISK_FULL="DISK FULL",e.TOO_MANY_OPEN_FILES="TOO MANY OPEN FILES",e.OUT_OF_MMAP_AREAS="OUT OF MMAP AREAS",e.OUT_OF_MEMORY="OUT OF MEMORY",e.UNSUPPORTED_FILE_SYSTEM="UNSUPPORTED FILE SYSTEM",e}({}),O=function(e){return e.EXISTS="Exists",e.DOES_NOT_EXIST="Does not exist",e.RESERVED_NAME="Reserved name",e}({});var R=n(61318);const I=new class{constructor(){this.emitter=new R.Z}publish(e,t){this.emitter.emit(e,t)}subscribe(e,t){this.emitter.on(e,t)}unsubscribe(e,t){this.emitter.off(e,t)}};let A=function(e){return e.MSG_ACTIVE_SIDEBAR="active.panel",e.MSG_EDITOR_FOCUS="editor.focus",e.MSG_EDITOR_INSERT_COLUMN="editor.insert.column",e.GRID_FOCUS="grid.focus",e.MSG_CHART_DRAW="chart.draw",e.MSG_QUERY_CANCEL="query.in.cancel",e.MSG_QUERY_DATASET="query.out.dataset",e.MSG_QUERY_ERROR="query.out.error",e.MSG_QUERY_EXEC="query.in.exec",e.MSG_QUERY_FIND_N_EXEC="query.build.execute",e.MSG_QUERY_OK="query.out.ok",e.MSG_QUERY_RUNNING="query.out.running",e.MSG_QUERY_SCHEMA="query.out.schema",e.MSG_CONNECTION_OK="query.connection.ok",e.MSG_CONNECTION_ERROR="query.connection.error",e.MSG_CONNECTION_UNAUTHORIZED="query.connection.unauthorized",e.MSG_CONNECTION_FORBIDDEN="query.connection.forbidden",e.REACT_READY="react.ready",e.TAB_FOCUS="tab.focus",e.TAB_BLUR="tab.blur",e.METRICS_REFRESH_DATA="metrics.refresh.data",e}({}),L=function(e){return e.RELEASE_TYPE="RELEASE_TYPE",e.AUTH_PAYLOAD="AUTH_PAYLOAD",e.AUTH_REFRESH_TOKEN="AUTH_REFRESH_TOKEN",e.OAUTH_REDIRECT_COUNT="oauth.redirect.count",e.OAUTH_STATE="oauth.state",e.PKCE_CODE_VERIFIER="pkce.code.verifier",e.QUERY_TEXT="query.text",e.EDITOR_LINE="editor.line",e.EDITOR_COL="editor.col",e.EXAMPLE_QUERIES_VISITED="editor.exampleQueriesVisited",e.EDITOR_SPLITTER_BASIS="splitter.editor.basis",e.RESULTS_SPLITTER_BASIS="splitter.results.basis",e.REST_TOKEN="rest.token",e.BASIC_AUTH_HEADER="basic.auth.header",e.AUTO_REFRESH_TABLES="auto.refresh.tables",e.SSO_USERNAME="sso.username",e}({});class P{_controllers=[];commonHeaders={};static refreshTokenPending=!1;static numOfPendingQueries=0;refreshTokenMethod=async()=>({});tokenNeedsRefresh(){const e=localStorage.getItem(L.AUTH_PAYLOAD),t=localStorage.getItem(L.AUTH_REFRESH_TOKEN);if(e){const n=JSON.parse(e);return new Date(n.expires_at).getTime()-(new Date).getTime()<3e4&&""!==t}}setCommonHeaders(e){this.commonHeaders=e}refreshAuthToken=async()=>{P.refreshTokenPending=!0,await new Promise((e=>{const t=setInterval((async()=>{if(0===P.numOfPendingQueries){clearInterval(t);const n=await this.refreshTokenMethod();return n.access_token&&this.setCommonHeaders({...this.commonHeaders,Authorization:`Bearer ${n.groups_encoded_in_token?n.id_token:n.access_token}`}),P.refreshTokenPending=!1,e(!0)}}),50)}))};static encodeParams=e=>Object.keys(e).filter((t=>void 0!==e[t])).map((t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`)).join("&");abort=()=>{this._controllers.forEach((e=>{e.abort()})),this._controllers=[]};static transformQueryRawResult=e=>{if(e.type===T.DQL){const{columns:t,count:n,dataset:r,timings:a}=e,o=r.map((e=>e.reduce(((e,n,r)=>({...e,[t[r].name]:n})),{})));return{columns:t,count:n,data:o,timings:a,type:T.DQL,...e.explain?{explain:e.explain}:{}}}return e};async query(e,t){const n=await this.queryRaw(e,t);return P.transformQueryRawResult(n)}async mockQueryResult(e){return P.transformQueryRawResult(e)}async queryRaw(e,t){const n=new AbortController,r={count:!0,src:"con",query:e,timings:!0,version:"2",...t};let a;this._controllers.push(n),this.tokenNeedsRefresh()&&!P.refreshTokenPending&&await this.refreshAuthToken(),P.refreshTokenPending&&await new Promise((e=>{const t=setInterval((()=>{if(!P.refreshTokenPending)return clearInterval(t),e(!0)}),50)})),P.numOfPendingQueries++;const o=new Date;try{a=await fetch(`exec?${P.encodeParams(r)}`,{signal:n.signal,headers:this.commonHeaders})}catch(t){const n={position:-1,query:e,type:T.ERROR},r={...n,error:"An error occured, please try again"};return t instanceof DOMException?Promise.reject({...n,error:20===t.code?"Cancelled by user":JSON.stringify(t).toString()}):(I.publish(A.MSG_CONNECTION_ERROR,r),Promise.reject(r))}finally{const e=this._controllers.indexOf(n);e>=0&&this._controllers.splice(e,1),P.numOfPendingQueries--}if(a.ok||400===a.status||a.ok&&403===a.status){let t;try{t=await a.text()}catch(e){return Promise.reject({error:`Failed to read response: ${e}`,type:T.ERROR})}const n=1e6*((new Date).getTime()-o.getTime());let r;try{r=JSON.parse(t)}catch(e){return Promise.reject({error:`Invalid JSON response from the server: ${e}`,type:T.ERROR})}return I.publish(A.MSG_CONNECTION_OK),403===a.status&&I.publish(A.MSG_CONNECTION_FORBIDDEN,r),r.ddl?{query:e,type:T.DDL}:r.dml?{query:e,type:T.DML}:r.error?Promise.reject({...r,type:T.ERROR}):r.notice?{...r,type:T.NOTICE}:{...r,timings:{...r.timings,fetch:n},type:T.DQL}}const i={status:a.status,error:a.statusText};if((e=>408===e.status||e.status>=500)(a)&&(i.error=`QuestDB is not reachable [${a.status}]`,i.position=-1,i.query=e,i.type=T.ERROR,I.publish(A.MSG_CONNECTION_ERROR,i)),401===a.status&&(i.error="Unauthorized",I.publish(A.MSG_CONNECTION_UNAUTHORIZED,i)),403===a.status){const e=(await a.text()).trim();if(e.startsWith("{")){const t=JSON.parse(e);i.error=t.error}else i.error=e;I.publish(A.MSG_CONNECTION_FORBIDDEN,i)}return Promise.reject(i)}async showTables(){const e=await this.query("tables();");return e.type===T.DQL?{...e,data:e.data.slice().sort(((e,t)=>{const n=e.table_name,r=t.table_name;return n>r?1:n<r?-1:0}))}:e}async showPermissions(e){return await this.query(`SHOW PERMISSIONS ${e};`)}async showColumns(e){return await this.query(`SHOW COLUMNS FROM '${e}';`)}async showMatViewDDL(e){return await this.query(`SHOW CREATE MATERIALIZED VIEW '${e}';`)}async showTableDDL(e){return await this.query(`SHOW CREATE TABLE '${e}';`)}async checkCSVFile(e){try{const t=await fetch(`chk?${P.encodeParams({f:"json",j:e,version:"2"})}`,{headers:this.commonHeaders});return await t.json()}catch(e){throw e}}async uploadCSVFile({file:e,name:t,owner:n,settings:r,schema:a,partitionBy:o,timestamp:i,onProgress:l}){const s=new FormData;a&&s.append("schema",JSON.stringify(a)),s.append("data",e);const c={fmt:"json",name:t,owner:n,...o?{partitionBy:o}:{},...i?{timestamp:i}:{},...r?Object.keys(r).reduce(((e,t)=>({...e,[t]:r[t].toString()})),{}):{}};return new Promise(((e,t)=>{let n=new XMLHttpRequest;n.open("POST",`imp?${new URLSearchParams(c)}`),Object.keys(this.commonHeaders).forEach((e=>{n.setRequestHeader(e,this.commonHeaders[e])})),n.upload.addEventListener("progress",(e=>{let t=e.loaded/e.total*100;l(t)})),n.onload=r=>{200===n.status?e(JSON.parse(n.response)):t({status:n.status,statusText:n.statusText})},n.onerror=()=>{t({status:n.status,statusText:n.statusText})},n.send(s)}))}async savePreferences(e){const{version:t,...n}=e,r=await fetch(`settings?version=${t}`,{method:"PUT",headers:this.commonHeaders,body:JSON.stringify(n)});if(!r.ok){let e;try{e=await async function(e){const t=e.headers.get("Content-Type");if(t?.includes("application/json")){const{error:t}=await e.json();return t}return e.text()}(r)}catch(t){e=r.statusText}return{status:r.status,message:e,success:!1}}return{status:r.status,success:!0}}async exportQueryToCsv(e){try{const t=await fetch(`exp?${P.encodeParams({query:e,version:"2"})}`,{headers:this.commonHeaders}),n=await t.blob(),r=t.headers.get("Content-Disposition")?.split("=")[1],a=window.URL.createObjectURL(n),o=document.createElement("a");o.href=a,o.download=r?r.replaceAll('"',""):`questdb-query-${(new Date).getTime()}.csv`,o.click(),window.URL.revokeObjectURL(a)}catch(e){throw e}}async getLatestRelease(){try{const e=await fetch("https://github-api.questdb.io/github/latest");return await e.json()}catch(e){return Promise.reject(e)}}async sendFeedback({email:e,message:t,telemetryConfig:n}){try{const r=await fetch("https://cloud.questdb.com/api/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,message:t,telemetryConfig:n,category:"web-console"})});return await r.json()}catch(e){throw e}}async getNews({category:e,telemetryConfig:t}){try{const n=await fetch(`https://cloud.questdb.com/api/news?category=${e}&telemetryUserId=${t?.id}`);return await n.json()}catch(e){return Promise.reject(e)}}}const D=(e,t,n="...")=>{if(e.length<=t)return e;const r=Math.floor(e.length/2),a=e.length-t+n.length;return e.slice(0,r-Math.floor(a/2))+n+e.slice(r+Math.ceil(a/2))},M=e=>t=>t?t.theme.color[e]:void 0;var B=n(57188);const F=(e,t)=>(0,B.WU)(e,{language:"postgresql",...t}),H={isMacintosh:!1,isWindows:!1,isIOS:!1,isLinux:!1};"object"==typeof navigator&&(H.isMacintosh=-1!==navigator.userAgent.indexOf("Macintosh"),H.isWindows=navigator.userAgent.indexOf("Windows")>=0,H.isIOS=(navigator.userAgent.indexOf("Macintosh")>=0||navigator.userAgent.indexOf("iPad")>=0||navigator.userAgent.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0);const V=()=>navigator.languages&&navigator.languages.length?navigator.languages[0]:navigator.language;var z=n(87905),Z=n(73751),U=n(68522),q=n(47761),G=n(85412),$=n(666),j=n(9576),W=n(32629);const Q=e=>({"en-GB":z.e,"en-US":Z._,"fr-FR":U.fr,"es-ES":q.es,"de-DE":G.de,"ja-JP":$.ja,"ko-KR":j.ko,"zh-CN":W.U}[e]||Z._),Y=e=>Array.from(new Set(e));let K=function(e){return e[e.SLOW=1500]="SLOW",e[e.REG=200]="REG",e[e.FAST=70]="FAST",e}({});const X=(e,t)=>(0,s.vJ)(["\n  .","-enter {\n    opacity: 0;\n  }\n\n  .","-enter-active {\n    opacity: 1;\n    transition: all ","ms;\n  }\n\n  .","-exit {\n    opacity: 1;\n  }\n\n  .","-exit-active {\n    opacity: 0;\n    transition: all ","ms;\n  }\n"],e,e,t,e,e,t),J=(0,s.iv)(["\n  .collapse-enter {\n    max-height: 0;\n  }\n\n  .collapse-enter-active {\n    max-height: ","px;\n    transition: all ","ms;\n  }\n\n  .collapse-exit {\n    max-height: ","px;\n  }\n\n  .collapse-exit-active {\n    max-height: 0;\n    transition: all ","ms;\n  }\n"],(({_height:e})=>e),(({duration:e})=>e||K.REG),(({_height:e})=>e),(({duration:e})=>e||K.REG)),ee=(K.FAST,K.FAST,(0,s.iv)(["\n  transition: all ","ms cubic-bezier(0, 0, 0.38, 0.9);\n"],K.FAST));function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},te.apply(this,arguments)}const ne={fontSize:"md",size:"md",type:"button"},re=({size:e})=>{switch(e){case"sm":return"2rem";case"md":default:return"3rem";case"lg":return"5rem"}},ae=(0,s.iv)(["\n  display: flex;\n  height: ",";\n  padding: 0 1rem;\n  align-items: center;\n  justify-content: center;\n  background: transparent;\n  border-radius: 4px;\n  border: 1px solid transparent;\n  outline: 0;\n  font-size: ",";\n  font-weight: 400;\n  line-height: 1.15;\n  cursor: pointer;\n  ",";\n\n  svg + span {\n    margin-left: 0.5rem;\n  }\n"],re,(({fontSize:e,theme:t})=>t.fontSize[e]),ee),oe=(e,t,n)=>(0,s.iv)(["\n    background: ",";\n    color: ",";\n    border-color: ",";\n\n    &:focus {\n      box-shadow: inset 0 0 0 1px ",";\n    }\n\n    &:hover:not([disabled]) {\n      background: ",";\n      color: ",";\n      border-color: ",";\n    }\n\n    &:active:not([disabled]) {\n      background: ",";\n      filter: brightness(90%);\n    }\n\n    &:disabled {\n      cursor: not-allowed;\n      background: ",";\n      color: ",";\n      border-color: ",";\n    }\n  "],M(e.background),M(e.color),M(e.border),M("foreground"),M(t.background),M(t.color),M(t.border),M(t.background),M(n.background),M(n.color),M(n.border)),ie=s.ZP.button.withConfig({displayName:"Button__PrimaryButtonStyled"})(["\n  ",";\n  ",";\n"],ae,oe({background:"selection",border:"selection",color:"foreground"},{background:"comment",border:"selection",color:"foreground"},{background:"selection",border:"gray1",color:"gray1"})),le=(e,t)=>r.createElement(ie,te({},ne,e,{ref:t}));(0,r.forwardRef)(le).defaultProps=ne;const se=s.ZP.button.withConfig({displayName:"Button__SecondaryButtonStyled"})(["\n  ",";\n  ",";\n"],ae,oe({background:"backgroundLighter",border:"backgroundLighter",color:"foreground"},{background:"comment",border:"comment",color:"foreground"},{background:"gray1",border:"gray1",color:"background"})),ce=(e,t)=>r.createElement(se,te({},ne,e,{ref:t})),de=(0,r.forwardRef)(ce);de.defaultProps=ne;const ue=s.ZP.button.withConfig({displayName:"Button__SuccessButtonStyled"})(["\n  ",";\n  ",";\n"],ae,oe({background:"selection",border:"selection",color:"green"},{background:"comment",border:"selection",color:"green"},{background:"selection",border:"gray1",color:"gray1"})),me=(e,t)=>r.createElement(ue,te({},ne,e,{ref:t}));(0,r.forwardRef)(me).defaultProps=ne;const pe=s.ZP.button.withConfig({displayName:"Button__ErrorButtonStyled"})(["\n  ",";\n  ",";\n"],ae,oe({background:"selection",border:"selection",color:"red"},{background:"comment",border:"selection",color:"red"},{background:"selection",border:"gray1",color:"gray1"})),ge=(e,t)=>r.createElement(pe,te({},ne,e,{ref:t}));(0,r.forwardRef)(ge).defaultProps=ne;const fe=s.ZP.button.withConfig({displayName:"Button__TransparentButtonStyled"})(["\n  ",";\n  ",";\n"],ae,oe({background:"transparent",border:"transparent",color:"foreground"},{background:"comment",border:"transparent",color:"foreground"},{background:"selection",border:"gray1",color:"gray1"})),he=(e,t)=>r.createElement(fe,te({},ne,e,{ref:t})),be=(0,r.forwardRef)(he);be.defaultProps=ne;const Ee=s.ZP.div.withConfig({displayName:"CenteredLayout__Root"})(["\n  display: grid;\n  place-items: center;\n  height: 100vh;\n  background: ",";\n  color: ",";\n"],(({theme:e})=>e.color.backgroundDarker),(({theme:e})=>e.color.foreground)),ye=({children:e})=>r.createElement(Ee,null,e);var we=n(23251),Ce=n(92941),ve=n(44947),_e=n(38638);const xe=s.ZP.div.withConfig({displayName:"group-header__GroupHeader"})(["\n  display: flex;\n  justify-content: center;\n  width: 100%;\n  padding: 2rem 0;\n  border-bottom: 0.1rem "," solid;\n"],(({theme:e})=>e.color.selection)),Se=s.ZP.div.withConfig({displayName:"group-item__GroupItem"})(["\n  display: flex;\n  ",";\n  padding: 2rem;\n\n  &:not(:last-child) {\n    border-bottom: 0.1rem "," solid;\n  }\n\n  &:last-child {\n    border-bottom-left-radius: ",";\n    border-bottom-right-radius: ",";\n  }\n\n  ","\n"],(({direction:e})=>"column"===e?"\n    flex-direction: column;\n    align-items: flex-start;\n    flex-wrap: wrap;\n    width: 100%;\n  ":"\n    justify-content: space-between;\n    flex-wrap: initial;\n  "),(({theme:e})=>e.color.backgroundLighter),(({theme:e})=>e.borderRadius),(({theme:e})=>e.borderRadius),(({theme:e,withLink:t})=>t&&`\n    &:hover {\n      cursor: pointer;\n      background-color: ${e.color.black};\n    }\n  `)),ke=s.ZP.div.attrs((e=>({flexDirection:e.flexDirection||"row",gap:e.gap||"1rem",margin:e.margin||"0",align:e.align||"center",justifyContent:e.justifyContent||"flex-start"}))).withConfig({displayName:"Box"})(["\n  display: flex;\n  flex-direction: ",";\n  gap: ",";\n  margin: ",";\n  align-items: ",";\n  justify-content: ",";\n"],(({flexDirection:e})=>e),(({gap:e})=>e),(({margin:e})=>e),(({align:e})=>e),(({justifyContent:e})=>e)),Te=(0,s.ZP)(ke).attrs({gap:"1rem",justifyContent:"flex-end",align:"center"}).withConfig({displayName:"actions__Actions"})(["\n  width: 100%;\n  padding-left: 2rem;\n  padding-right: 2rem;\n  height: 7rem;\n"]),Ne=(0,s.ZP)(ke).attrs({gap:"0",flexDirection:"column"}).withConfig({displayName:"content-wrapper__ContentWrapper"})(["\n  width: 100%;\n  /*\n    4.5rem = top bar\n    4.5rem = drawer title\n    4rem = footer\n  */\n  height: ",";\n  form {\n    width: 100%;\n    height: 100%;\n  }\n"],(({mode:e})=>"side"===e?"calc(100vh - 4.5rem - 4.5rem - 4rem)":"calc(100vh - 4.5rem)")),Oe=s.ZP.div.withConfig({displayName:"PaneMenu"})(["\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  height: 4.5rem;\n  padding: 0 1rem;\n  align-items: center;\n  background: ",";\n  border-top: 1px solid transparent;\n"],M("backgroundLighter"));function Re(){return Re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Re.apply(this,arguments)}const Ie=(0,s.iv)(["\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n"]),Ae=(0,s.iv)(["\n  color: ",";\n  font-family: ",";\n  font-size: ",";\n  font-style: ",";\n  font-weight: ",";\n  text-transform: ",";\n  ",";\n  ",";\n"],(e=>e.color?M(e.color):"inherit"),(({code:e,theme:t})=>e&&t.fontMonospace),(({size:e,theme:t})=>e?t.fontSize[e]:"inherit"),(({_style:e})=>e??"inherit"),(({weight:e})=>e),(({transform:e})=>e),(({align:e})=>e?`text-align: ${e}`:""),(({ellipsis:e})=>e&&Ie)),Le=s.ZP.label.withConfig({displayName:"Text__TextStyled"})(["\n  ",";\n"],Ae),Pe=({type:e,...t})=>r.createElement(Le,Re({as:e},t,{type:e}));Pe.defaultProps={color:"black",type:"span"};const De=s.ZP.div.withConfig({displayName:"header__Root"})(["\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  height: 4.5rem;\n  padding: 0 1rem;\n  align-items: center;\n  background: ",";\n  border-top: 1px solid transparent;\n  z-index: 5;\n\n  ","\n"],M("backgroundLighter"),(({shadow:e})=>e&&"\n      box-shadow: 0 2px 10px 0 rgba(23, 23, 23, 0.65)\n  ")),Me=(0,s.ZP)(Pe).withConfig({displayName:"header__Title"})(["\n  display: flex;\n  align-items: center;\n  padding-left: 1rem;\n  font-size: 1.8rem;\n  font-weight: 600;\n"]),Be=({children:e})=>e;function Fe(){return Fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Fe.apply(this,arguments)}Be.Header=({title:e,afterTitle:t,shadow:n})=>r.createElement(De,{shadow:n},e&&r.createElement(Me,{color:"foreground"},e),t);const He=(0,s.iv)(["\n  @keyframes animateShow {\n    from {\n      transform: translateX(100%);\n    }\n    to {\n      transform: translateX(0);\n    }\n  }\n"]),Ve=(0,s.iv)(["\n  @keyframes animateHide {\n    from {\n      transform: translateX(0);\n    }\n    to {\n      transform: translateX(100%);\n    }\n  }\n"]),ze=(0,s.ZP)(we.VY).attrs({forceMount:!0}).withConfig({displayName:"Drawer__DrawerContent"})(["\n  background-color: ",";\n  border-left: 0.2rem "," solid;\n  position: ",";\n  top: 0;\n  right: 0;\n  width: ",";\n  max-width: 100%;\n  height: 100%;\n  overflow: auto;\n  z-index: 101;\n\n  ","\n  ","\n\n  ",";\n"],(({theme:e})=>e.color.backgroundLighter),(({theme:e})=>e.color.background),(({mode:e})=>"modal"===e?"fixed":"inherit"),(({width:e})=>e??"50rem"),He,Ve,(({mode:e})=>"modal"===e&&'\n  &[data-state="open"] {\n    animation: animateShow 0.5s cubic-bezier(0.16, 1, 0.3, 1);\n  }\n\n  &[data-state="closed"] {\n    animation: animateHide 0.5s cubic-bezier(0.16, 1, 0.3, 1);\n  }\n\n  &:focus {\n    outline: none;\n  }\n  ')),Ze=(0,s.ZP)(we.x8).attrs({"aria-label":"Close",asChild:!0}).withConfig({displayName:"Drawer__StyledClose"})(["\n  appearance: initial;\n  margin-left: auto;\n  cursor: pointer;\n  color: ",";\n"],(({theme:e})=>e.color.foreground)),Ue=({mode:e="modal",children:t,trigger:n,title:a,afterTitle:o,width:i,open:l,onOpenChange:s,onDismiss:c,withCloseButton:d,closeOnOverlayClick:u=!0,closeOnEscape:m=!0})=>r.createElement(we.fC,{onOpenChange:s,open:l,modal:"modal"===e},r.createElement(we.xz,{asChild:!0},r.createElement(Ce.A,null,n)),r.createElement(we.h_,"side"===e&&{container:document.getElementById("side-panel-right")},"modal"===e&&r.createElement(Ce.A,null,r.createElement(ve.a,{primitive:we.aV})),r.createElement(ze,Fe({mode:e,width:i},c&&{onEscapeKeyDown:m?c:void 0,onInteractOutside:u?c:void 0},"side"===e&&{onInteractOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>e.preventDefault(),onPointerDownOutside:e=>e.preventDefault()}),(a||d)&&r.createElement(Be.Header,Fe({title:a,afterTitle:o},d&&{afterTitle:r.createElement(Ze,c?{onClick:c}:{},r.createElement(_e.x,{size:"18px"}))})),t)));Ue.GroupHeader=xe,Ue.GroupItem=Se,Ue.Actions=Te,Ue.ContentWrapper=Ne;const qe=(e,t)=>{const[n,a]=(0,r.useState)(!1),o=n=>{n.key===e&&(t?.preventDefault&&n.preventDefault(),a(!0))},i=n=>{n.key===e&&(t?.preventDefault&&n.preventDefault(),a(!1))};return(0,r.useEffect)((()=>(window.addEventListener("keydown",o),window.addEventListener("keyup",i),()=>{window.removeEventListener("keydown",o),window.removeEventListener("keyup",i)}))),n},Ge=(e,t)=>{(0,r.useEffect)((()=>{const n=Object.entries(t).reduce(((e,[t,n])=>`${e} ${t}: ${n};`),"z-index: 1000;");e.setAttribute("style",n)}),[e,t])};var $e=n(31095),je=function(e){return e[e.SM=767]="SM",e}(je||{});const We=(0,r.createContext)({sm:window.innerWidth<=je.SM}),Qe=({children:e})=>{const[t,n]=(0,r.useState)({sm:window.innerWidth<=je.SM}),[a,i]=(0,r.useState)();return(0,r.useEffect)((()=>{const e=(0,$e.P)(16,(()=>{i({sm:window.innerWidth<=je.SM})}));return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),(0,r.useEffect)((()=>{a&&!(0,o.wU)(a,t)&&n(a)}),[t,a]),r.createElement(We.Provider,{value:t},e)},Ye=()=>(0,r.useContext)(We),Ke=(e,t,n,a)=>{(0,r.useEffect)((()=>{if(clearTimeout(n.current),t&&!document.body.contains(e))return document.body.appendChild(e),void(null!=a&&a());t||(n.current=window.setTimeout((()=>{document.body.contains(e)&&document.body.removeChild(e)}),K.REG))}),[t,e,n,a])},Xe=({icon:e,tooltip:t,placement:n,textAlign:a})=>r.createElement(dt,{placement:n,trigger:e},r.createElement(_t,{textAlign:a},t));function Je(){return Je=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Je.apply(this,arguments)}const et={size:"md",type:"text"},tt=s.ZP.input.withConfig({displayName:"Input__InputStyled"})(["\n  height: ",";\n  border: none;\n  padding: 0 1rem;\n  line-height: 1.5;\n  outline: none;\n  background: ",";\n  border-radius: 4px;\n  color: ",";\n\n  &:focus {\n    box-shadow: inset 0 0 0 1px ",";\n  }\n\n  &::placeholder {\n    color: ",";\n  }\n"],re,M("selection"),M("foreground"),M("foreground"),M("gray2")),nt=(e,t)=>r.createElement(tt,Je({},et,e,{ref:t}));(0,r.forwardRef)(nt).defaultProps=et;const rt=Pe.defaultProps,at=s.ZP.a.withConfig({displayName:"Link__Wrapper"})(["\n  ",";\n  text-decoration: none;\n  line-height: 1;\n\n  &:hover:not([disabled]),\n  &:focus:not([disabled]) {\n    color: ",";\n  }\n"],Ae,(e=>e.hoverColor?M(e.hoverColor):"inherit")),ot=e=>r.createElement(at,e);ot.defaultProps=rt;const it=s.ZP.div.withConfig({displayName:"PaneContent"})(["\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  background: ",";\n  overflow: auto;\n"],M("backgroundLighter")),lt=s.ZP.div.withConfig({displayName:"PaneWrapper"})(["\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n"]);var st=n(46317),ct=n(1264);const dt=({children:e,delay:t,modifiers:n,placement:o,trigger:i})=>{const[l]=(0,r.useState)(document.createElement("div")),s=(0,r.useRef)(),c=(0,r.useRef)(),[d,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(null),[g,f]=(0,r.useState)(null),{attributes:h,styles:b,forceUpdate:E}=(0,st.D)(m,l,{modifiers:[{name:"arrow",options:{element:g}},{name:"offset",options:{offset:[0,6]}},{name:"eventListeners",enabled:d},...n],placement:o}),y=(0,r.useCallback)((()=>{t?s.current=window.setTimeout((()=>{u(!0)}),t):u(!0)}),[t]),w=(0,r.useCallback)((()=>{clearTimeout(s.current),u(!1)}),[]);return(0,r.useEffect)((()=>()=>{clearTimeout(c.current),clearTimeout(s.current),document.body.contains(l)&&document.body.removeChild(l)}),[l]),Ge(l,b.popper),Ke(l,d,c,E),r.createElement(r.Fragment,null,r.isValidElement(i)&&r.cloneElement(i,{onMouseEnter:y,onMouseLeave:w,ref:p}),a.createPortal(r.createElement(ct.Z,{classNames:"fade-reg",in:d,timeout:K.REG,unmountOnExit:!0},r.isValidElement(e)&&r.cloneElement(e,{...h.popper,arrow:{setArrowElement:f,styles:b.arrow}})),l))};dt.defaultProps={modifiers:[],placement:"auto"};const ut=({active:e,children:t,modifiers:n,onToggle:o,placement:i,trigger:l})=>{const[s]=(0,r.useState)(document.createElement("div")),c=(0,r.useRef)(),[d,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(null),{attributes:g,styles:f}=(0,st.D)(m,s,{modifiers:[...n,{name:"eventListeners",enabled:d}],placement:i}),h=(0,r.useCallback)((()=>{const e=!d;u(e),o&&o(e)}),[d,o]),b=(0,r.useCallback)((e=>{const t=e.target;s.contains(t)||m?.contains(t)||d&&(u(!1),o&&o(!1))}),[s,o,m,d]),E=(0,r.useCallback)((e=>{"Escape"===e.key&&d&&(u(!1),o&&o(!1))}),[d,o]);return Ge(s,f.popper),Ke(s,d,c),(0,r.useEffect)((()=>{void 0!==e&&u(e)}),[e]),(0,r.useEffect)((()=>(document.body.appendChild(s),()=>{clearTimeout(c.current),document.body.contains(s)&&document.body.removeChild(s)})),[s]),(0,r.useEffect)((()=>(document.addEventListener("mousedown",b),document.addEventListener("touchstart",b),document.addEventListener("keydown",E),()=>{document.removeEventListener("mousedown",b),document.removeEventListener("touchstart",b),document.removeEventListener("keydown",E)})),[b,E]),r.createElement(r.Fragment,null,r.isValidElement(l)&&r.cloneElement(l,{onClick:h,ref:p}),r.isValidElement(t)&&r.createElement(ct.Z,{classNames:"fade-reg",in:d,timeout:K.REG,unmountOnExit:!0},r.createElement(r.Fragment,null,a.createPortal(r.cloneElement(t,{...g.popper}),s))))};function mt(){return mt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mt.apply(this,arguments)}ut.defaultProps={modifiers:[],placement:"auto"};const pt={direction:"bottom",fontSize:"md",selected:!1,size:"md",type:"button",readOnly:!1},gt=(0,s.iv)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ",";\n  border: none;\n  outline: 0;\n  font-size: ",";\n  font-weight: 400;\n  line-height: 1.15;\n  width: 3.5rem;\n  height: 3.5rem;\n  border-radius: 0.4rem;\n  cursor: pointer;\n  ",";\n  ",";\n  color: ",";\n\n  svg + span,\n  img + span {\n    margin-left: 1rem;\n  }\n"],(({selected:e,theme:t})=>e?"#2d303e":"transparent"),(({fontSize:e,theme:t})=>t.fontSize[e]),ee,(({disabled:e})=>e&&"cursor: default; pointer-events: none;"),(({selected:e,theme:t})=>t.color[e?"green":"offWhite"])),ft=s.ZP.button.withConfig({displayName:"ToggleButton__PrimaryToggleButtonStyled"})(["\n  ",";\n  ",";\n"],gt,(ht={background:"comment"},(0,s.iv)(["\n    &:hover:not([disabled]) {\n      background: ",";\n      opacity: 1;\n    }\n\n    &:active:not([disabled]) {\n      filter: brightness(90%);\n    }\n\n    ","\n  "],M(ht.background),(({readOnly:e})=>e&&"\n      filter: brightness(0.5);\n      cursor: default;\n    "))));var ht;const bt=(e,t)=>r.createElement(ft,mt({},pt,e,{ref:t})),Et=(0,r.forwardRef)(bt);function yt(){return yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},yt.apply(this,arguments)}Et.defaultProps=pt;const wt=(0,s.iv)(["\n  position: absolute;\n  width: 7px;\n  height: 7px;\n"]),Ct=s.ZP.div.withConfig({displayName:"Tooltip__TooltipArrow"})(["\n  ",";\n\n  &::before {\n    ",';\n    top: 0;\n    left: 0;\n    content: "";\n    transform: rotate(45deg);\n    background: ',";\n    border: 1px solid ",";\n    border-radius: 1px;\n  }\n"],wt,wt,M("backgroundDarker"),M("gray1")),vt=s.ZP.div.withConfig({displayName:"Tooltip__Wrapper"})(["\n  position: relative;\n  max-width: 460px;\n  padding: 1rem;\n  background: ",";\n  border: 1px solid ",';\n  border-radius: 1px;\n\n  &[data-popper-placement^="right"] ',' {\n    left: -4px;\n\n    &::before {\n      border-right: none;\n      border-top: none;\n    }\n  }\n\n  &[data-popper-placement^="left"] ',' {\n    right: -4px;\n\n    &::before {\n      border-left: none;\n      border-bottom: none;\n    }\n  }\n\n  &[data-popper-placement^="top"] ',' {\n    bottom: -4px;\n\n    &::before {\n      border-left: none;\n      border-top: none;\n    }\n  }\n\n  &[data-popper-placement^="bottom"] '," {\n    top: -4px;\n\n    &::before {\n      border-right: none;\n      border-bottom: none;\n    }\n  }\n"],M("backgroundDarker"),M("gray1"),Ct,Ct,Ct,Ct),_t=({arrow:e,children:t,textAlign:n="left",...a})=>r.createElement(vt,yt({},a,{"data-hook":"tooltip",style:{textAlign:n}}),r.createElement(Pe,{color:"foreground"},t),e&&r.createElement(Ct,{ref:e.setArrowElement,style:e.styles}));var xt=n(44631);function St(){return St=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},St.apply(this,arguments)}const kt=({height:e,...t})=>r.createElement(xt.OO,St({style:e?{height:e}:{}},t));var Tt=n(63245);n(97655);let Nt=function(e){return e.SUCCESS="success",e.INFO="info",e.WARNING="warning",e.ERROR="error",e}({});var Ot=n(94937),Rt=n(41531),It=n(84374),At=n(46827);const Lt={baseFontSize:"10px",color:{black:"#191a21",black70:"rgba(25, 26, 33, 0.7)",black40:"rgba(25, 26, 33, 0.4)",black20:"rgba(25, 26, 33, 0.2)",gray1:"#585858",gray2:"#bbbbbb",backgroundDarker:"#21222c",backgroundLighter:"#282a36",background:"#21222c",foreground:"#f8f8f2",selection:"#44475a",selectionDarker:"#333544",comment:"#6272a4",red:"#ff5555",orange:"#ffb86c",yellow:"#f1fa8c",green:"#50fa7b",purple:"#bd93f9",cyan:"#8be9fd",pink:"#d14671",pink50:"rgba(209, 70, 113, 0.25)",pinkDarker:"#be2f5b",pinkLighter:"#ff79c6",transparent:"transparent",white:"#fafafa",inherit:"inherit",tooltipBackground:"#6272a4",tableSelection:"#043c5c",graphLegend:"#6e7078",offWhite:"#bdbdbd"},font:'"Open Sans", -apple-system, BlinkMacSystemFont, Helvetica, Roboto, sans-serif',fontEmoji:'"apple color emoji", "segoe ui emoji", "android emoji", "emojisymbols", "emojione mozilla", "twemoji mozilla", "segoe ui symbol", "noto color emoji"',fontMonospace:'SFMono-Regular, Menlo, Monaco, Consolas,"Liberation Mono", "Courier New", monospace',fontSize:{ms:"1rem",xs:"1.2rem",sm:"1.3rem",md:"1.4rem",lg:"1.5rem",xl:"1.7rem",hg:"3rem"},borderRadius:"0.8rem"};function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pt.apply(this,arguments)}n(85383);const Dt=({type:e,size:t=18,...n})=>{switch(e){case Nt.SUCCESS:return r.createElement(At.W,Pt({},n,{size:t,color:Lt.color.green}));case Nt.WARNING:return r.createElement(Ot.u,Pt({},n,{size:t,color:Lt.color.orange}));case Nt.ERROR:return r.createElement(Rt.P,Pt({},n,{size:t,color:Lt.color.red}));case Nt.INFO:default:return r.createElement(It.d,Pt({},n,{size:t,color:Lt.color.cyan}))}},Mt=(e,t)=>Tt.Am.info(e,{icon:r.createElement(Dt,{type:Nt.INFO}),className:"toast-info-container",progressStyle:{background:Lt.color.cyan},style:{borderColor:Lt.color.cyan,background:Lt.color.backgroundLighter},...t}),Bt=(e,t)=>Tt.Am.success(e,{icon:r.createElement(Dt,{type:Nt.SUCCESS}),className:"toast-success-container",progressStyle:{background:Lt.color.green},style:{borderColor:Lt.color.green,background:Lt.color.backgroundLighter},...t}),Ft=(e,t)=>Tt.Am.error(e,{icon:r.createElement(Dt,{type:Nt.ERROR}),progressStyle:{background:Lt.color.red},className:"toast-error-container",style:{borderColor:Lt.color.red,background:Lt.color.backgroundLighter},...t}),Ht=(Tt.Am.dismiss,Tt.Am.isActive,e=>{const t={autoClose:3e3,draggable:!1,position:"top-right",theme:"dark",transition:Tt.Mi,hideProgressBar:!1,closeButton:!0,closeOnClick:!0,pauseOnHover:!0,pauseOnFocusLoss:!1,...e};return r.createElement(Tt.Ix,t)});let Vt=function(e){return e.TOGGLE_SIDE_MENU="CONSOLE/TOGGLE_SIDE_MENU",e.SET_ACTIVE_TOP_PANEL="CONSOLE/SET_ACTIVE_TOP_PANEL",e.SET_ACTIVE_SIDEBAR="CONSOLE/SET_ACTIVE_SIDEBAR",e.SET_ACTIVE_BOTTOM_PANEL="CONSOLE/SET_ACTIVE_BOTTOM_PANEL",e.SET_IMAGE_TO_ZOOM="CONSOLE/SET_IMAGE_TO_ZOOM",e}({});const zt={toggleSideMenu:()=>({type:Vt.TOGGLE_SIDE_MENU}),setActiveTopPanel:e=>({payload:e,type:Vt.SET_ACTIVE_TOP_PANEL}),setActiveSidebar:e=>({payload:e,type:Vt.SET_ACTIVE_SIDEBAR}),setActiveBottomPanel:e=>({payload:e,type:Vt.SET_ACTIVE_BOTTOM_PANEL}),setImageToZoom:e=>({payload:e,type:Vt.SET_IMAGE_TO_ZOOM})};let Zt=function(e){return e.ERROR="error",e.INFO="info",e.SUCCESS="success",e.NOTICE="notice",e.LOADING="loading",e}({}),Ut=function(e){return e.ADD_NOTIFICATION="QUERY/ADD_NOTIFICATION",e.CLEANUP_NOTIFICATIONS="QUERY/CLEANUP_NOTIFICATIONS",e.REMOVE_NOTIFICATION="QUERY/REMOVE_NOTIFICATION",e.SET_RESULT="QUERY/SET_RESULT",e.STOP_RUNNING="QUERY/STOP_RUNNING",e.TOGGLE_RUNNING="QUERY/TOGGLE_RUNNING",e.SET_TABLES="QUERY/SET_TABLES",e.SET_COLUMNS="QUERY/SET_COLUMNS",e}({}),qt=function(e){return e.START="TELEMETRY/START",e.SET_CONFIG="TELEMETRY/SET_CONFIG",e.SET_REMOTE_CONFIG="TELEMETRY/SET_REMOTE_CONFIG",e}({});const Gt=zt,$t=e=>({payload:{createdAt:new Date,type:Zt.SUCCESS,...e},type:Ut.ADD_NOTIFICATION}),jt=()=>({type:Ut.CLEANUP_NOTIFICATIONS}),Wt=e=>({payload:e,type:Ut.SET_RESULT}),Qt=()=>({type:Ut.STOP_RUNNING}),Yt=(e=!1)=>({type:Ut.TOGGLE_RUNNING,payload:{isRefresh:e}}),Kt=e=>({payload:{tables:e},type:Ut.SET_TABLES}),Xt=e=>({payload:{columns:e},type:Ut.SET_COLUMNS}),Jt=()=>({type:qt.START}),en=e=>({payload:e,type:qt.SET_CONFIG}),tn=e=>({payload:e,type:qt.SET_REMOTE_CONFIG});var nn=n(61507),rn=n(74998),an=n(73937),on=n(63547),ln=n(2304),sn=n(41174),cn=n(3826);const dn=e=>localStorage.getItem(e)??"",un=(e,t)=>localStorage.setItem(e,t),mn=e=>localStorage.removeItem(e),pn=e=>{const t=dn(L.RELEASE_TYPE);("EE"===t||e?.enabled)&&fetch(`${y}/add-ent`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,releaseType:t})}).catch((()=>{}))},gn=new P,fn=[e=>e.pipe((0,rn.l)(qt.START),(0,_.w)((()=>{const e=""!==dn(L.AUTH_PAYLOAD)?dn(L.AUTH_PAYLOAD):"{}",t=JSON.parse(e);if(t.access_token)gn.setCommonHeaders({Authorization:`Bearer ${t.groups_encoded_in_token?t.id_token:t.access_token}`});else{const e=dn(L.REST_TOKEN);if(e)gn.setCommonHeaders({Authorization:`Bearer ${e}`});else{const e=dn(L.BASIC_AUTH_HEADER);e&&gn.setCommonHeaders({Authorization:e})}}return(0,sn.D)(gn.query(`${E.CONFIG} limit -1`))})),(0,_.w)((e=>e.type===T.DQL?(0,C.of)(en(e.data[0])):cn.C))),(e,t)=>e.pipe((0,rn.l)(qt.SET_CONFIG),(0,an.M)(t),(0,_.w)((([e,t])=>{const n=Cn.telemetry.getConfig(t);return n&&pn(n),(e=>e?.enabled?k(`${y}/config`,{method:"POST",body:JSON.stringify(e)},!1):cn.C)(n)})),(0,_.w)((e=>e.error?cn.C:(0,C.of)(tn(e.data))))),(e,t)=>e.pipe((0,rn.l)(qt.SET_REMOTE_CONFIG),(0,an.M)(t),(0,_.w)((([e,t])=>{const n=Cn.telemetry.getRemoteConfig(t);if(n?.lastUpdated){const e=new Date(n.lastUpdated).toISOString();return(0,sn.D)(gn.queryRaw(`with tel as (\n              SELECT cast(created as long), event, origin\n              FROM ${E.MAIN}\n              WHERE created > '${e}'\n              LIMIT -10000 \n            )            \n            SELECT cast(created as long), cast(1000 as short), cast(case when sm >= 0 then sm else 32767 end as short) FROM (\n              SELECT created, cast(ceil(sum(rowCount) / 1000.0) as short) sm\n              FROM ${E.WAL}\n              WHERE created > '${e}' and rowCount > 0\n              SAMPLE BY 1h align to calendar\n            )\n            UNION ALL \n            SELECT cast(created as long), cast(2000 as short), cast(case when sm >= 0 then sm else 32767 end as short) FROM (\n              SELECT created, cast(count() as short) sm\n              FROM ${E.WAL}\n              WHERE created > '${e}' and rowCount > 0\n              SAMPLE BY 1h align to calendar\n            )\n            UNION ALL \n            SELECT cast(created as long), cast(3000 as short), cast(case when sm >= 0 then sm else 32767 end as short) FROM (\n              SELECT created, cast(max(latency) / 1000.0 as short) sm\n              FROM ${E.WAL}\n              WHERE created > '${e}' and rowCount > 0\n              SAMPLE BY 1h align to calendar\n             )\n             UNION ALL\n             SELECT * FROM tel\n             `.replace(/\s+/g," ").trim()))}return cn.C})),(0,an.M)(t),(0,_.w)((([e,t])=>{const n=Cn.telemetry.getRemoteConfig(t),r=Cn.telemetry.getConfig(t);return null!=r?.id&&e.type===T.DQL&&e.count>0?k(`${y}/add`,{method:"POST",body:JSON.stringify({columns:e.columns,dataset:e.dataset,id:r.id,version:r.version,os:r.os,package:r.package})},!1).pipe((0,S.U)((t=>{if(!t.error){const t=e.dataset[e.count-1][0];return tn({...n,lastUpdated:t})}})),(0,on.g)(36e5),(0,ln.h)((e=>!!e))):cn.C})))],hn=(0,nn.l)(...fn),bn={sideMenuOpened:!1,activeTopPanel:"tables",activeSidebar:void 0,activeBottomPanel:"zeroState",imageToZoom:void 0},En={notifications:[],tables:[],columns:[],running:{value:!1,isRefresh:!1},maxNotifications:20},yn={},wn=(0,i.UY)({console:(e=bn,t)=>{switch(t.type){case Vt.TOGGLE_SIDE_MENU:return{...e,sideMenuOpened:!e.sideMenuOpened};case Vt.SET_ACTIVE_TOP_PANEL:return{...e,activeTopPanel:t.payload};case Vt.SET_ACTIVE_SIDEBAR:return{...e,activeSidebar:t.payload};case Vt.SET_ACTIVE_BOTTOM_PANEL:return{...e,activeBottomPanel:t.payload};case Vt.SET_IMAGE_TO_ZOOM:return{...e,imageToZoom:t.payload};default:return e}},query:(e=En,t)=>{switch(t.type){case Ut.ADD_NOTIFICATION:{const n=[...e.notifications,t.payload];for(;n.length===e.maxNotifications;)n.shift();return{...e,notifications:n}}case Ut.CLEANUP_NOTIFICATIONS:return{...e,notifications:[]};case Ut.REMOVE_NOTIFICATION:return{...e,notifications:e.notifications.filter((({createdAt:e})=>e!==t.payload))};case Ut.SET_RESULT:return{...e,result:t.payload};case Ut.STOP_RUNNING:return{...e,running:{value:!1,isRefresh:!1}};case Ut.TOGGLE_RUNNING:return{...e,running:{value:!e.running.value,isRefresh:t.payload.isRefresh}};case Ut.SET_TABLES:return{...e,tables:t.payload.tables};case Ut.SET_COLUMNS:return{...e,columns:t.payload.columns};default:return e}},telemetry:(e=yn,t)=>{switch(t.type){case qt.SET_CONFIG:return{...e,config:t.payload};case qt.SET_REMOTE_CONFIG:return{...e,remoteConfig:{cta:!1,lastUpdated:new Date((new Date).getTime()-321408e5).toISOString(),...t.payload}};default:return e}}}),Cn={console:{getSideMenuOpened:e=>e.console.sideMenuOpened,getActiveTopPanel:e=>e.console.activeTopPanel,getActiveSidebar:e=>e.console.activeSidebar,getActiveBottomPanel:e=>e.console.activeBottomPanel,getImageToZoom:e=>e.console.imageToZoom},query:{getNotifications:e=>e.query.notifications,getResult:e=>e.query.result,getRunning:e=>e.query.running,getTables:e=>e.query.tables,getColumns:e=>e.query.columns},telemetry:{getConfig:e=>e.telemetry.config,getRemoteConfig:e=>e.telemetry.remoteConfig}};var vn=n(95726),_n=n(849);const xn=s.ZP.div.withConfig({displayName:"GithubBanner__Wrapper"})(["\n  position: fixed;\n  display: flex;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  height: 100%;\n  align-items: center;\n  justify-content: center;\n  background: ",";\n  overflow: hidden;\n"],M("pink")),Sn=s.ZP.span.withConfig({displayName:"GithubBanner__GithubLink"})(["\n  text-decoration: underline;\n"]),kn=(0,s.ZP)(_e.x).withConfig({displayName:"GithubBanner__CloseIcon"})(["\n  position: absolute;\n  right: 1rem;\n  color: ",";\n\n  &:hover {\n    cursor: pointer;\n  }\n"],M("black")),Tn=(0,s.ZP)(_n.U).withConfig({displayName:"GithubBanner__StarIcon"})(["\n  color: ",";\n"],M("yellow")),Nn=({onClick:e})=>r.createElement(xn,null,r.createElement(ot,{hoverColor:"black",href:"https://github.com/questdb/questdb",weight:800},"If you like QuestDB, give us a ",r.createElement(Tn,{size:"14px"})," on ",r.createElement(Sn,null,"Github")),r.createElement(kn,{onClick:e,size:"20px"})),On=e=>e["acl.oidc.host"]?`${e["acl.oidc.tls.enabled"]?"https":"http"}://${e["acl.oidc.host"]}:${e["acl.oidc.port"]}`:"",Rn=async(e,t)=>fetch(`${On(e)}${e["acl.oidc.token.endpoint"]}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams(t)}),In=e=>e["acl.enabled"]&&!e["acl.basic.auth.realm.enabled"];var An=n(77798),Ln=n(30919);var Pn=n(46193),Dn=n(65742),Mn=n(79409);const Bn=({basicAuthEnabled:e,errorMessage:t,onLogout:n})=>r.createElement(ye,null,r.createElement(Pn.x,{flexDirection:"column",gap:"2rem"},r.createElement(Pe,{color:"foreground"},t),!e&&r.createElement(Dn.z,{"data-hook":"button-login-with-other-account",skin:"secondary",prefixIcon:r.createElement(Mn.n,{size:"18px"}),onClick:()=>n()},"Login")));var Fn=n(80678),Hn=n(7523);const Vn=s.ZP.div.withConfig({displayName:"FormItem__Root"})(["\n  display: grid;\n  gap: 1rem;\n  white-space: normal;\n  width: 100%;\n\n  ","\n"],(({border:e,theme:t})=>e&&`\n    border-bottom: 1px ${t.color.background} solid;\n  `)),zn=s.ZP.div.withConfig({displayName:"FormItem__Control"})(["\n  display: grid;\n  gap: 1rem;\n  ","\n"],(e=>e.disabled&&"opacity: 0.7;")),Zn=s.ZP.div.withConfig({displayName:"FormItem__LabelWrapper"})(["\n  display: flex;\n  align-self: center;\n  justify-content: space-between;\n  align-items: baseline;\n  width: 100%;\n"]),Un=s.ZP.label.withConfig({displayName:"FormItem__Label"})(["\n  color: ",";\n"],(({theme:e})=>e.color.gray2)),qn=s.ZP.span.withConfig({displayName:"FormItem__AfterLabel"})(["\n  color: ",";\n"],(({theme:e})=>e.color.gray2));var Gn=n(87563),$n=n(68004),jn=n(44047);function Wn(){return Wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wn.apply(this,arguments)}const Qn=s.ZP.div.withConfig({displayName:"FormInput__Wrapper"})(["\n  display: flex;\n  width: 100%;\n  ","\n"],(e=>"off"===e.autoComplete&&"\n    // Hide the LastPass+NordPass icons\n    [data-lastpass-icon-root],\n    span[data-np-uid] {\n      display: none !important;\n    }\n  ")),Yn=(0,s.ZP)(Gn.I).withConfig({displayName:"FormInput__Input"})(["\n  ","\n"],(e=>e.disabled&&"opacity: 0.7;")),Kn=(0,s.ZP)(Dn.z).withConfig({displayName:"FormInput__ToggleButton"})(["\n  cursor: pointer;\n  border-radius: 0;\n\n  ","\n"],(e=>e.last&&"\n    border-top-right-radius: 0.4rem;\n    border-bottom-right-radius: 0.4rem;\n  "));var Xn=n(81213);function Jn(){return Jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Jn.apply(this,arguments)}var er=n(98378);function tr(){return tr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},tr.apply(this,arguments)}const nr=s.ZP.div.withConfig({displayName:"FormGroup__Root"})(["\n  width: 100%;\n  display: grid;\n  grid-template-columns: repeat(",", minmax(0, 1fr));\n  gap: ",";\n"],(({columns:e})=>e),(({gap:e})=>e));function rr(){return rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},rr.apply(this,arguments)}function ar(){return ar=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ar.apply(this,arguments)}const or=(0,s.ZP)(Gn.I).attrs({as:"textarea"}).withConfig({displayName:"FormTextArea__TextArea"})(["\n  width: 100%;\n  height: inherit;\n"]),ir=({name:e,method:t="post",onSubmit:n,onChange:a,onDirtyChange:o,children:i,validationSchema:l,defaultValues:s,preventSubmitOnEnter:c})=>{let d={};s&&(d.defaultValues=s),l&&(d.resolver=(0,Hn.L)(l));const u=(0,Fn.cI)(d);return(0,r.useEffect)((()=>{a&&u.watch(a)}),[]),(0,r.useEffect)((()=>{o&&o(u.formState.isDirty,u.formState.dirtyFields)}),[u.formState]),Object.keys(u.formState.errors).length>0&&console.log("Schema validation errors",u.formState.errors),r.createElement(Fn.RV,u,r.createElement("form",{name:e,onSubmit:async e=>{e.stopPropagation(),await u.handleSubmit(n)(e)},method:t,onKeyDown:e=>{"Enter"===e.key&&c&&e.preventDefault()}},i))};ir.Item=({name:e,label:t,afterLabel:n,helperText:a,children:o,className:i,disabled:l,border:s,required:c})=>{const{formState:{errors:d}}=(0,Fn.Gc)();return r.createElement(Vn,{className:i,disabled:l,border:s},r.createElement(zn,{disabled:l},t&&e&&r.createElement(Zn,null,r.createElement(Un,{htmlFor:e},t,c?r.createElement(Pe,{color:"red"}," *"):""),r.createElement(qn,null,n)),o),e&&!d[e]&&a&&("string"==typeof a?r.createElement(Pe,{color:"comment"},a):a),e&&d&&d[e]&&r.createElement(Pe,{color:"red"},d[e]?.message))},ir.Input=({name:e,placeholder:t,type:n="text",disabled:a,showPassword:o,autoFocus:i,autoComplete:l,...s})=>{const{register:c,setFocus:d}=(0,Fn.Gc)(),[u,m]=(0,r.useState)(o),p=(0,r.useCallback)((()=>{m(!u)}),[u,m]);return(0,r.useEffect)((()=>{i&&d(e)}),[]),r.createElement(Qn,{autoComplete:l},r.createElement(Yn,Wn({},c(e,{valueAsNumber:"number"===n}),{name:e,placeholder:t,type:u?"text":n,disabled:a,showPassword:o,autoComplete:l},s)),"password"===n&&r.createElement(Kn,{skin:"secondary",onClick:p,title:"Toggle password visibility",type:"button",last:!0},u?r.createElement($n.b,{size:"15px"}):r.createElement(jn._,{size:"15px"})))},ir.Checkbox=({name:e,checked:t,...n})=>{const{register:a}=(0,Fn.Gc)();return r.createElement("div",{style:{width:"100%",display:"flex"}},r.createElement(Xn.X,Jn({},n,a(e))))},ir.Select=({name:e,...t})=>{const{register:n}=(0,Fn.Gc)();return r.createElement(er.P,tr({},n(e),t))},ir.Group=({children:e,columns:t,gap:n="2rem"})=>r.createElement(nr,{columns:t??r.Children.count(r.Children.toArray(e)),gap:n},e),ir.Submit=({children:e,disabled:t,variant:n,prefixIcon:a,...o})=>r.createElement(Dn.z,rr({type:"submit",disabled:t,skin:n??"primary",prefixIcon:a,dataHook:"form-submit-button"},o),e),ir.Cancel=({children:e,disabled:t,variant:n,prefixIcon:a,defaultValues:o,onClick:i})=>{const{reset:l}=(0,Fn.Gc)();return r.createElement(Dn.z,{type:"button",onClick:()=>{l(o),i&&i()},disabled:t,skin:n??"secondary",prefixIcon:a,dataHook:"form-cancel-button"},e)},ir.TextArea=({name:e,placeholder:t,rows:n,autoFocus:a,...o})=>{const{register:i,setFocus:l}=(0,Fn.Gc)();return(0,r.useEffect)((()=>{a&&l(e)}),[]),r.createElement(or,ar({},i(e),{rows:n??4,placeholder:t},o))};var lr=n(29674),sr=n.n(lr);const cr=s.ZP.div.withConfig({displayName:"login__Header"})(["\n  position: absolute;\n  width: 100%;\n  padding: 30px;\n"]),dr=s.ZP.div.withConfig({displayName:"login__ErrorContainer"})(["\n  @keyframes smooth-appear {\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes smooth-disappear {\n    to {\n      transform: translateY(10px);\n      opacity: 0;\n    }\n  }\n\n  background: gray;\n  color: white;\n  padding: 20px;\n  margin-top: 10px;\n  opacity: 0;\n  transform: translateY(10px);\n  border-radius: 10px;\n  text-align: center;\n\n  ","\n"],(({hasError:e})=>e?"\n  animation: smooth-appear 0.5s ease forwards;\n  ":"animation: smooth-disappear 0.5s ease forwards;")),ur=s.ZP.div.withConfig({displayName:"login__Container"})(["\n  margin-left: auto;\n  margin-right: auto;\n  margin-top: 4%;\n  width: 500px;\n  font-size: 16px;\n  transition: height 10s ease;\n"]),mr=s.ZP.h2.withConfig({displayName:"login__Title"})(["\n  color: white;\n  text-align: start;\n  font-weight: 600;"]),pr=s.ZP.div.withConfig({displayName:"login__SSOCard"})(["\n  button {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n    width: 100%;\n    margin-bottom: 10px;\n  }\n  margin-bottom: 10px;\n"]),gr=s.ZP.div.withConfig({displayName:"login__Card"})(["\n  border-radius: ",';\n\n  @keyframes horizontal-shaking {\n    0% {\n      transform: translateX(0);\n    }\n    25% {\n      transform: translateX(5px);\n    }\n    50% {\n      transform: translateX(-5px);\n    }\n    75% {\n      transform: translateX(5px);\n    }\n    100% {\n      transform: translateX(0);\n    }\n  }\n\n  button[type="submit"] {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n    border-radius: 5px;\n    width: 100%;\n    margin-top: 40px;\n  }\n\n  ','\n\n  button {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n    border-radius: 0 5px 5px 0;\n  }\n\n  input[name="password"] {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n    border-radius: 5px 0 0 5px;\n  }\n\n  input {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n    border-radius: 5px;\n    box-shadow: 0 0 0 30px #44475a inset !important;\n    -webkit-text-fill-color: white;\n  }\n\n  label {\n    margin-top: 20px;\n  }\n'],(({theme:e})=>e.borderRadius),(({hasError:e})=>e&&'\n    button[type="submit"] {\n      animation: horizontal-shaking 0.3s ease-in-out;\n    }\n  ')),fr=(0,s.ZP)(Dn.z).withConfig({displayName:"login__StyledButton"})(["\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n"]),hr=s.ZP.div.withConfig({displayName:"login__Line"})(['\n  position: relative;\n  text-align: center;\n\n  &:before {\n    content: "";\n    position: absolute;\n    top: 50%;\n    left: 0;\n    width: 100%;\n    height: 1px;\n    background: ',";\n  }\n"],(({theme:e})=>e.color.selection)),br=s.ZP.div.withConfig({displayName:"login__TooltipArrow"})(['\n  &::before {\n    position: absolute;\n    width: 7px;\n    height: 7px;\n    top: -4px;\n    left: 50%;\n    content: "";\n    transform: rotate(45deg);\n    background: gray;\n    border-left: 1px solid gray;\n    border-radius: 1px;\n    border-right: none;\n    border-bottom: none;\n  }\n']),Er=(0,s.ZP)(Pe).withConfig({displayName:"login__LineText"})(["\n  position: relative;\n  z-index: 1;\n  background: ",";\n  padding: 0 1.5rem;\n"],(({theme:e})=>e.color.backgroundDarker)),yr=s.ZP.div.withConfig({displayName:"login__Footer"})(["\n  width: 700px;\n  text-align: center;\n  padding: 20px;\n  margin-top: 40px;\n  margin-right: auto;\n  margin-left: auto;\n"]),wr=sr().object({username:sr().string().required().messages({"string.empty":"User name is required"}),password:sr().string().required().messages({"string.empty":"Password is required"})}),Cr=({onOAuthLogin:e,onBasicAuthSuccess:t})=>{const{settings:n}=Ir(),a="EE"===n["release.type"],[o,i]=r.useState(),l=n["acl.oidc.enabled"]&&n["acl.oidc.client.id"]?(s=n["acl.oidc.client.id"],localStorage.getItem(`${L.SSO_USERNAME}.${s}`)??""):"";var s;const c=a?{query:e=>`alter user '${e}' create token type rest with ttl '1d' refresh transient`,store:async(e,t,n)=>{const r=(await e.json()).dataset[0][1];un(L.REST_TOKEN,r)}}:{query:()=>"select * from long_sequence(1)",store:async(e,t,n)=>{un(L.BASIC_AUTH_HEADER,`Basic ${btoa(`${t}:${n}`)}`)}};return(0,r.useEffect)((()=>{setTimeout((()=>{i(void 0)}),5e3)}),[o]),n["acl.basic.auth.realm.enabled"]?null:r.createElement("div",{"data-hook":"auth-login"},r.createElement(cr,null,r.createElement("a",{href:"https://questdb.io"},r.createElement("img",{alt:"QuestDB logotype",height:"20",src:"assets/questdb-logotype.svg"}))),r.createElement(ur,null,n["acl.oidc.enabled"]&&r.createElement(r.Fragment,null,r.createElement(mr,{style:{marginBottom:"4rem"}},"Single Sign-On"),r.createElement(pr,null,!!l&&r.createElement(fr,{"data-hook":"button-sso-continue",skin:"primary",prefixIcon:r.createElement(Mn.n,{size:"18px"}),onClick:()=>e(!1)},"Continue as ",l),r.createElement(fr,{"data-hook":"button-sso-login",skin:l?"transparent":"primary",prefixIcon:l?void 0:r.createElement(Mn.n,{size:"18px"}),onClick:()=>e(!0)},l?"Choose a different account":"Continue with SSO"),r.createElement(hr,{style:{marginBottom:"4rem",marginTop:"2rem"}},r.createElement(Er,{color:"gray2"},"or")))),r.createElement(mr,null,"Sign In"),r.createElement(gr,{hasError:o},r.createElement(ir,{name:"login",onSubmit:async e=>{const{username:n,password:r}=e;try{const e=await fetch(`exec?query=${c.query(n)}`,{headers:{Authorization:`Basic ${btoa(`${n}:${r}`)}`}});if(200===e.status)return await c.store(e,n,r),t();401===e.status?i("Invalid user name or password"):403===e.status?i("Unauthorized to use the Web Console"):i("Login failed, status code: "+e.status)}catch(e){i("Error occurred while trying to login")}},defaultValues:{},validationSchema:wr},r.createElement(ir.Item,{name:"username",label:"User name"},r.createElement(ir.Input,{name:"username",placeholder:"johndoe"})),r.createElement(ir.Item,{name:"password",label:"Password"},r.createElement(ir.Input,{name:"password",type:"password",placeholder:"••••••••"})),r.createElement(ir.Submit,{variant:"primary"},"Sign In"),r.createElement(dr,{hasError:o},r.createElement(br,null),o)))),r.createElement(yr,null))};var vr=n(17093),_r=n(91197),xr=function(e){return e[e.loading=0]="loading",e[e.ready=1]="ready",e[e.error=2]="error",e}(xr||{});const Sr={view:xr.loading},kr=(e,t)=>({...e,...t}),Tr=(0,r.createContext)({settings:{},preferences:{},consoleConfig:{},warnings:[],refreshSettingsAndPreferences:()=>Promise.resolve({settings:{},preferences:{}})}),Nr=r.createElement(r.Fragment,null,"Error connecting to the database.",r.createElement("br",null),"Please, check if the server is running correctly."),Or=r.createElement(r.Fragment,null,"Error loading the console configuration file"),Rr=({children:e})=>{const[t,n]=(0,r.useReducer)(kr,Sr),[a,o]=(0,r.useState)({}),[i,l]=(0,r.useState)({}),[s,c]=(0,r.useState)([]),[d,u]=(0,r.useState)({}),m={[xr.loading]:()=>null,[xr.ready]:()=>r.createElement(Tr.Provider,{value:{settings:a,consoleConfig:d,warnings:s,preferences:i,refreshSettingsAndPreferences:f}},e),[xr.error]:()=>r.createElement(ye,null,r.createElement(Pn.x,{flexDirection:"column",gap:"2rem"},r.createElement("a",{href:"https://questdb.io"},r.createElement("img",{alt:"QuestDB logotype",width:"95",height:"23",src:"assets/questdb-logotype.svg"})),r.createElement(vr.x,{align:"center",size:"lg"},"Error connecting to the database.",r.createElement("br",null),"Please, check if the server is running correctly."),r.createElement(Dn.z,{skin:"secondary",prefixIcon:r.createElement(_r.h,{size:"18px"}),onClick:()=>window.location.reload()},"Retry")))},p=async(e,t)=>{try{const r=await fetch(e);if(200===r.status)return await r.json();n({view:xr.error,errorMessage:t})}catch(e){n({view:xr.error,errorMessage:t})}},g=e=>{if(e?.instance_name){const t=e?.instance_type?`${e.instance_type.charAt(0).toUpperCase()}${e.instance_type.slice(1)}`:"QuestDB",n=`${e.instance_name} | ${t}`;document.title!==n&&(document.title=n)}l(e)},f=async()=>{const e=await p("settings",Nr),t=e.config,n={version:e["preferences.version"],...e.preferences};return e?(o(t),g(n),{settings:t,preferences:n}):{settings:{},preferences:{}}};return(0,r.useEffect)((()=>{(async()=>{const e=await p("settings",Nr),t=await p("warnings",Nr),n=await p("assets/console-configuration.json",Or);e&&(o(e.config),g({version:e["preferences.version"],...e.preferences}),un(L.RELEASE_TYPE,e.config["release.type"])),t&&c(t),n&&u(n)})().then((()=>n({view:xr.ready})))}),[]),r.createElement(r.Fragment,null,m[t.view]())},Ir=()=>(0,r.useContext)(Tr);var Ar=function(e){return e[e.ready=0]="ready",e[e.loading=1]="loading",e[e.error=2]="error",e[e.login=3]="login",e}(Ar||{});const Lr={view:Ar.loading},Pr={sessionData:void 0,logout:()=>{},refreshAuthToken:async()=>({}),redirectToAuthorizationUrl:()=>{}};class Dr{constructor(e,t){this.error=e,this.error_description=t}}const Mr=(0,r.createContext)(Pr),Br=(e,t)=>({...e,...t}),Fr=({children:e})=>{const{settings:t}=Ir(),[n,a]=(0,r.useState)(void 0),[o,i]=(0,r.useState)(void 0),[l,s]=(0,r.useReducer)(Br,Lr),c=(e,t)=>{if(e.access_token)e.groups_encoded_in_token=t["acl.oidc.groups.encoded.in.token"],e.expires_at=(n=e.expires_in,new Date((new Date).getTime()+1e3*n)).toString(),un(L.AUTH_PAYLOAD,JSON.stringify(e)),un(L.AUTH_REFRESH_TOKEN,e.refresh_token??""),a(e),history.replaceState&&history.replaceState(null,"",location.pathname+location.search.replace(/[?&]code=[^&]+/,"").replace(/^&/,"?"));else{const t=e;s({view:Ar.error,errorMessage:t.error_description??"Error logging in. Please try again."})}var n},d=async e=>{const t=await Rn(e,{grant_type:"refresh_token",refresh_token:dn(L.AUTH_REFRESH_TOKEN),client_id:e["acl.oidc.client.id"]}),n=await t.json();return c(n,e),n},u=async()=>{200===(await fetch("exec?query=select 42")).status?s({view:Ar.ready}):await u()},m=()=>{const e=dn(L.REST_TOKEN),t=dn(L.BASIC_AUTH_HEADER);s(e||t?{view:Ar.ready}:{view:Ar.login})},p=e=>{const n=(e=>e["acl.oidc.state.required"]?(()=>{const e=new Uint8Array(60);crypto.getRandomValues(e);const t=Ln.DS.fromUint8Array(e,!0);return un(L.OAUTH_STATE,t),t})():null)(t),r=(e=>e["acl.oidc.pkce.required"]?(()=>{const e=new Uint8Array(60);crypto.getRandomValues(e);const t=Ln.DS.fromUint8Array(e,!0);return un(L.PKCE_CODE_VERIFIER,t),t})():null)(t),a=(e=>e?(e=>{const t=[];for(let n=0;n<e.length;n++){const r=e.codePointAt(n);if(void 0===r)throw Error(n+" is out of bounds of string: "+e);t.push(r)}const n=new Uint8Array(An.sha256.create().update(t).array());return Ln.DS.fromUint8Array(n,!0)})(e):null)(r);window.location.href=(({settings:e,code_challenge:t=null,state:n=null,loginWithDifferentAccount:r,redirect_uri:a})=>{const o={client_id:e["acl.oidc.client.id"]||"",response_type:"code",scope:e["acl.oidc.scope"]||"openid",redirect_uri:a},i=new URLSearchParams(o);return t&&(i.append("code_challenge",t),i.append("code_challenge_method","S256")),n&&i.append("state",n),r&&i.append("prompt","login"),On(e)+e["acl.oidc.authorization.endpoint"]+"?"+i})({settings:t,code_challenge:a,state:n,loginWithDifferentAccount:e,redirect_uri:t["acl.oidc.redirect.uri"]||window.location.href})},g=e=>{var n;mn(L.AUTH_PAYLOAD),mn(L.REST_TOKEN),mn(L.BASIC_AUTH_HEADER),e&&t["acl.oidc.client.id"]&&(n=t["acl.oidc.client.id"],localStorage.removeItem(`${L.SSO_USERNAME}.${n}`)),s({view:Ar.login})};(0,r.useEffect)((()=>{n&&s({view:Ar.ready})}),[n]),(0,r.useEffect)((()=>{I.subscribe(A.MSG_CONNECTION_FORBIDDEN,(e=>{e&&e.error.match(/Access denied.* \[HTTP]/gm)&&s({view:Ar.error,errorMessage:"Unauthorized to use the Web Console."})})),(async e=>{if(e["acl.enabled"])if(e["acl.oidc.enabled"]){const t=dn(L.AUTH_PAYLOAD),n=new URLSearchParams(window.location.search),r=n.get("code"),o=new Dr(n.get("error"),n.get("error_description"));if(I.subscribe(A.MSG_CONNECTION_UNAUTHORIZED,(()=>{const e=dn(L.OAUTH_REDIRECT_COUNT);if(e){const t=parseInt(e);!isNaN(t)&&t>=5?(mn(L.OAUTH_REDIRECT_COUNT),g(!0)):(un(L.OAUTH_REDIRECT_COUNT,JSON.stringify(e?parseInt(e)+1:1)),g())}else un(L.OAUTH_REDIRECT_COUNT,JSON.stringify(1)),g()})),I.subscribe(A.MSG_CONNECTION_OK,(()=>{mn(L.OAUTH_REDIRECT_COUNT)})),""!==t){const n=JSON.parse(t);new Date(n.expires_at).getTime()-Date.now()<3e4?""!==dn(L.AUTH_REFRESH_TOKEN)?await d(e):g():a(n)}else if(null!==r){const t=dn(L.OAUTH_STATE);if(t){mn(L.OAUTH_STATE);const e=n.get("state");if(!e||t!==e)return void g(!0)}try{const t=dn(L.PKCE_CODE_VERIFIER),n=await Rn(e,{grant_type:"authorization_code",code:r,code_verifier:t,client_id:e["acl.oidc.client.id"],redirect_uri:e["acl.oidc.redirect.uri"]||window.location.origin+window.location.pathname}),a=await n.json();c(a,e)}catch(e){throw e}}else o.error?i(o.error+": "+o.error_description):m()}else e["acl.basic.auth.realm.enabled"]?await u():(I.subscribe(A.MSG_CONNECTION_UNAUTHORIZED,(()=>{g()})),m());else s({view:Ar.ready})})(t)}),[]);const f={[Ar.loading]:()=>null,[Ar.ready]:()=>r.createElement(Mr.Provider,{value:{sessionData:n,logout:g,refreshAuthToken:d,redirectToAuthorizationUrl:p}},e),[Ar.error]:()=>r.createElement(Bn,{errorMessage:o,onLogout:g,basicAuthEnabled:t["acl.basic.auth.realm.enabled"]??!1}),[Ar.login]:()=>r.createElement(Cr,{onOAuthLogin:p,onBasicAuthSuccess:()=>{s({view:Ar.ready})}})};return r.createElement(r.Fragment,null,f[l.view]())},Hr=()=>(0,r.useContext)(Mr),Vr=/Build Information: QuestDB ([\w- ]+ )?([0-9A-Za-z.-]*),/,zr=/Commit Hash ([0-9A-Za-z]*)/,Zr=new P,Ur={quest:Zr,buildVersion:{type:"oss",version:""},commitHash:""},qr=(0,r.createContext)(Ur),Gr=({children:e})=>{const t=(0,o.I0)(),{settings:n}=Ir(),{sessionData:a,refreshAuthToken:i}=Hr(),[l,s]=(0,r.useState)(!In(n)),[c,d]=(0,r.useState)(Ur.buildVersion),[u,m]=(0,r.useState)(""),p=async()=>{await Zr.queryRaw("SELECT 1"),s(!0)};return(0,r.useEffect)((()=>{a&&(async e=>{Zr.setCommonHeaders({Authorization:`Bearer ${e.groups_encoded_in_token?e.id_token:e.access_token}`}),Zr.refreshTokenMethod=()=>i(n),p()})(a)}),[a]),(0,r.useEffect)((()=>{const e=dn(L.REST_TOKEN);if(e)Zr.setCommonHeaders({Authorization:`Bearer ${e}`}),p();else{const e=dn(L.BASIC_AUTH_HEADER);e&&(Zr.setCommonHeaders({Authorization:e}),p())}Zr.queryRaw("select build",{limit:"0,1000"}).then((e=>{e.type===T.DQL&&1===e.count&&(d((e=>{const t=Vr.exec(e.toString());return t?{type:t[1]?t[1].trim().toLowerCase():"oss",version:t[2]}:{type:"dev"}})(e.dataset[0][0])),m((e=>{const t=zr.exec(e.toString());return t?t[1]:""})(e.dataset[0][0])))}))}),[]),(0,r.useEffect)((()=>{l&&t(Jt())}),[l]),l?r.createElement(qr.Provider,{value:{quest:Zr,buildVersion:c,commitHash:u}},e):null},$r="questdb-sql",jr=e=>e.replace(/(?<!["'`])(--\s?.*$)/gm,((e,t)=>{if(t){const e=t.split("\n");return t.startsWith("--")&&e.length>1?"\n"+jr(e[1]):""}return e})),Wr=e=>{const t=e.getModel(),n=e.getSelection();return t&&n?t.getValueInRange(n):void 0},Qr=e=>{const t=jr(e.getValue({preserveBOM:!1,lineEnding:"\n"})),n=e.getPosition();let r=0,a=0;const o=[];let i=0,l=0,s=-1,c=null,d=!1;if(!n)return;let u=0;for(;u<t.length&&null===c;u++)switch(t[u]){case";":if(d){a++;continue}r<n.lineNumber-1||r===n.lineNumber-1&&a<n.column-1?(o.push({row:i,col:l,position:s,endRow:r,endCol:a,limit:u}),i=r,l=a,s=u+1,a++):c={row:i,col:l,position:s,endRow:r,endCol:a,limit:u};break;case" ":s===u&&(i=r,l=a,s=u+1),a++;break;case"\n":r++,a=0,s===u&&(i=r,l=a,s=u+1);break;case"'":d=!d,a++;break;default:a++}const m=n.lineNumber-1,p=o.length>0?o[o.length-1]:null;c||(-1===s?t:t.substring(s)).length>0&&(c={row:i,col:l,position:-1===s?0:s,endRow:r,endCol:a,limit:u});const g=p?{start:p.row,end:p.endRow}:null,f=c?{start:c.row,end:c.endRow}:null,h=g&&m>=g.start&&m<=g.end,b=f&&m>=f.start&&m<=f.end;if(h&&!b)return{query:t.substring(p.position,p.limit),row:p.row,column:p.col};if(b&&!h)return{query:t.substring(c.position,c.limit),row:c.row,column:c.col};if(h&&b){const e=p.endCol;return n.column-1>e+1?{query:t.substring(c.position,c.limit),row:c.row,column:c.col}:{query:t.substring(p.position,p.limit),row:p.row,column:p.col}}},Yr=(e,t,n={appendAt:"cursor"})=>{const r=e.getModel();if(r){const a=e.getPosition();if(a){const o=t.split("\n"),{prefix:i,suffix:l,lineStartOffset:s,selectStartOffset:c}=(({appendAt:e,model:t,position:n})=>{const r=1===n.lineNumber,a=n.lineNumber===t?.getValue().split("\n").length,o=t?.getLineContent(n.lineNumber),i=a?void 0:t?.getLineContent(n.lineNumber+1),l=!r&&!a,s={prefix:1,suffix:2,lineStartOffset:1,selectStartOffset:0},c=({end:[{when:()=>r,then:()=>({prefix:1,suffix:0})},{when:()=>!0,then:()=>({prefix:2,suffix:0,selectStartOffset:1})}],cursor:[{when:()=>""===t?.getValue(),then:()=>({prefix:0,suffix:1,lineStartOffset:0})},{when:()=>r&&""===o,then:()=>({prefix:0,lineStartOffset:0,suffix:""===i?0:1})},{when:()=>r&&""!==o,then:()=>({prefix:""===i?1:2,suffix:1,selectStartOffset:1})},{when:()=>l&&""===o,then:()=>({prefix:0,suffix:""===i?1:2})},{when:()=>l&&""!==o&&""!==i,then:()=>({prefix:1,suffix:2})},{when:()=>l&&""!==o&&""===i,then:()=>({prefix:1,suffix:1,selectStartOffset:1})},{when:()=>a,then:()=>({prefix:""===o?1:2,suffix:1,lineStartOffset:1,selectStartOffset:""===o?0:1})}]}[e].find((({when:e})=>e()))??{then:()=>s}).then();return{...s,...c}})({appendAt:n.appendAt,model:r,position:a}),d=(({model:e,position:t,lineStartOffset:n,newQueryLines:r,appendAt:a})=>{if("cursor"===a)return{lineStart:t.lineNumber+n,lineEnd:t.lineNumber+r.length,columnStart:0,columnEnd:r[r.length-1].length+1};const o=(e?.getValue().split("\n").length??0)+n;return{lineStart:o,lineEnd:o+r.length,columnStart:0,columnEnd:r[r.length-1].length+1}})({model:r,position:a,lineStartOffset:s,appendAt:n.appendAt,newQueryLines:o}),u={lineStart:d.lineStart+c,lineEnd:d.lineStart+c+(o.length-1),columnStart:0,columnEnd:d.columnEnd};(({editor:e,lineNumber:t,column:n,text:r})=>{e.executeEdits("",[{range:{startLineNumber:t,startColumn:n,endLineNumber:t,endColumn:n},text:r}])})({editor:e,lineNumber:d.lineStart,column:d.columnStart,text:`${"\n".repeat(i)}${t}${"\n".repeat(l)}`}),e.setSelection({startLineNumber:u.lineStart,endLineNumber:u.lineEnd,startColumn:u.columnStart,endColumn:u.columnEnd})}e.focus(),"end"===n.appendAt&&e.revealLine(r.getLineCount())}},Kr=(e,t)=>{const n=t.getModel();n&&e.editor.setModelMarkers(n,$r,[])},Xr=(e,t)=>e.findMatches(t,!0,!1,!0,null,!0)??null;var Jr=n(86732);class ea extends Jr.default{constructor(){super("web-console"),this.version(1).stores({buffers:"++id, label",editor_settings:"++id, key"}),this.version(2).stores({read_notifications:"++id, newsId"}),this.version(3).stores({buffers:"++id, label, position, archived, archivedAt"}).upgrade((e=>{let t=0;e.table("buffers").toCollection().modify((e=>{e.position=t,t++}))})),this.on("populate",(()=>{const e=dn(L.QUERY_TEXT);void 0!==e&&localStorage.removeItem(L.QUERY_TEXT),this.buffers.add(aa({label:"SQL",value:e??"",position:0})),this.editor_settings.add({key:"activeBufferId",value:oa.id}),this.editor_settings.add({key:"returnTo",value:""}),this.editor_settings.add({key:"returnToLabel",value:""})})),this.on("ready",(async()=>{0===await this.buffers.count()&&this.buffers.add(aa({label:"SQL",value:"",position:0}));const e=new URL(window.location.href);await Promise.all(["returnTo","returnToLabel"].map((async t=>{if(e.searchParams.has(t)){const n=e.searchParams.get(t)??"";0!==await this.editor_settings.where("key").equals(t).count()?this.editor_settings.where("key").equals(t).modify({value:n}):this.editor_settings.add({key:t,value:n}),e.searchParams.delete(t)}}))),window.history.replaceState({},"",e)}))}}const ta=new ea;let na=function(e){return e.SQL="SQL",e.METRICS="Metrics",e}({});const ra={cursorState:[{inSelectionMode:!1,selectionStart:{lineNumber:1,column:1},position:{lineNumber:1,column:1}}],contributionsState:[{"editor.contrib.wordHighlighter":!1,"editor.contrib.folding":{lineCount:1,provider:"indent",foldedImports:!1}}],viewState:{scrollLeft:0,firstPosition:{lineNumber:1,column:1},firstPositionDeltaTop:0}},aa=({label:e,value:t,editorViewState:n=ra,metricsViewState:r,position:a,archived:o,archivedAt:i})=>({label:e,value:t??"",editorViewState:r?void 0:n,metricsViewState:r,position:a,archived:o,archivedAt:i}),oa={id:1,...aa({label:"SQL",position:0})},ia=()=>ta.buffers.toArray(),la=()=>ta.editor_settings.where("key").equals("activeBufferId").first();var sa=n(90990);const ca={editorRef:{current:null},monacoRef:{current:null},insertTextAtCursor:()=>{},appendQuery:()=>{},buffers:[],activeBuffer:oa,setActiveBuffer:()=>Promise.resolve(),addBuffer:()=>Promise.resolve(oa),deleteBuffer:()=>Promise.resolve(),archiveBuffer:()=>Promise.resolve(),deleteAllBuffers:()=>Promise.resolve(),updateBuffer:()=>Promise.resolve(),editorReadyTrigger:()=>{},inFocus:!1},da=(0,r.createContext)(ca),ua=({children:e})=>{const t=(0,r.useRef)(null),n=(0,r.useRef)(null),a=(0,sa.useLiveQuery)(ia,[]),o=(0,sa.useLiveQuery)((()=>la()),[])?.value,[i,l]=(0,r.useState)(oa),[s,c]=(0,r.useState)(!1),d=(0,r.useRef)(!1);if((0,r.useEffect)((()=>{if(!d.current&&a&&o){const e=a?.find((e=>e.id===o))??a[0];l(e),d.current=!0}}),[a,o]),!a||!o||i===oa)return null;const u=async e=>{try{const a=(await la())?.value;if(n.current?.editor.getModels().forEach((t=>{const n=t.getValue();e.id===a||t.getValue()===e.value&&""!==n||t.dispose()})),a){if(e.id===a)return;await m(i.id)}if(await(r=e.id,ta.editor_settings.where("key").equals("activeBufferId").modify({value:r})),l(e),t.current&&n.current){const r=n.current.editor.createModel(e.value,$r);t.current.setModel(r),t.current.focus(),Kr(n.current,t.current),e.editorViewState&&t.current.restoreViewState(e.editorViewState)}}catch(e){console.warn("Error setting active buffer:",e)}var r},m=async(e,n)=>{const r=t.current?.saveViewState(),a=await(e=>ta.buffers.get(e).then((e=>e?.metricsViewState?na.METRICS:na.SQL)))(e);await((e,t)=>ta.buffers.update(e,t))(e,{...n,...r&&a===na.SQL?{editorViewState:r}:{}})},p=async e=>{const n=(await la())?.value;if(void 0!==n&&n===e){const e=await ta.buffers.toCollection().filter((e=>!e.archived)).last();await u(e??oa)}else t.current?.focus()};return r.createElement(da.Provider,{value:{editorRef:t,monacoRef:n,insertTextAtCursor:e=>{t?.current&&((e,t)=>{e.trigger("keyboard","type",{text:t}),e.focus()})(t.current,e)},appendQuery:(e,n)=>{t?.current&&Yr(t.current,e,n)},inFocus:s,buffers:a,activeBuffer:i,setActiveBuffer:u,addBuffer:async(e,{shouldSelectAll:r=!1}={})=>{const o=(i=e?.metricsViewState?na.METRICS:na.SQL,{id:1,...aa({label:i,position:0})});var i;const l=(await ta.buffers.filter((t=>!t.label.startsWith(o.label)||!e?.metricsViewState||void 0!==t.metricsViewState)).toArray()).map((e=>e.label.slice(o.label.length+1))).filter(Boolean).map((e=>parseInt(e,10))).sort(),s=aa({...e,label:e?.label??`${o.label} ${(()=>{for(let e=0;e<=l.length;e++){const t=e+1;if(!l.includes(t))return t}})()}`,position:a.filter((e=>!e.archived)).length}),c=await ta.buffers.add(s);if(await u(s),t.current&&n.current&&"string"==typeof s.value&&!s.metricsViewState){const e=n.current?.editor.createModel(s.value,$r);t.current.setModel(e),r&&t.current?.setSelection(e.getFullModelRange())}return{id:c,...s}},deleteBuffer:async e=>{await(e=>ta.buffers.delete(e))(e),await p(e)},archiveBuffer:async e=>{await m(e,{archived:!0,archivedAt:(new Date).getTime(),position:-1}),await p(e)},deleteAllBuffers:async()=>{await ta.buffers.clear()},updateBuffer:m,editorReadyTrigger:e=>{e.focus(),c(!0),e.onDidFocusEditorWidget((()=>c(!0))),e.onDidBlurEditorWidget((()=>c(!1))),i.editorViewState&&e.restoreViewState(i.editorViewState)}}},e)},ma=()=>(0,r.useContext)(da);var pa=n(56883);const ga=({children:e})=>{const{settings:t}=Ir(),n=!t["posthog.enabled"]??!0;return r.createElement(pa.zf,{apiKey:null===t["posthog.api.key"]?void 0:t["posthog.api.key"],options:{disable_persistence:n,disable_session_recording:n,disable_surveys:n,advanced_disable_decide:n,advanced_disable_feature_flags:n,advanced_disable_feature_flags_on_first_load:n,advanced_disable_toolbar_metrics:n,autocapture:!n,capture_pageview:!n,capture_pageleave:!n}},e)};var fa=n(45020),ha=n(94505),ba=n(99106),Ea=n(71073),ya=n(18373),wa=n(18226);const Ca=s.ZP.div.withConfig({displayName:"BuildVersion__Wrapper"})(["\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  align-items: center;\n\n  & > :not(:last-child) {\n    margin-right: 1rem;\n  }\n"]),va=(0,s.ZP)(de).withConfig({displayName:"BuildVersion__ReleaseNotesButton"})(["\n  position: relative;\n  ","\n  gap: 0.5rem;\n"],(({enterprise:e})=>e?"background: #322733;":"")),_a=s.ZP.a.withConfig({displayName:"BuildVersion__ReleaseLink"})(["\n  text-decoration: none;\n"]),xa=(0,s.ZP)(fa.k).withConfig({displayName:"BuildVersion__UpgradeIcon"})(["\n  color: ",";\n"],(({theme:e})=>e.color.green)),Sa=s.ZP.span.withConfig({displayName:"BuildVersion__NewestRelease"})(["\n  color: ",";\n  font-size: ",";\n"],(({theme:e})=>e.color.green),(({theme:e})=>e.fontSize.xs)),ka={dev:{label:"QuestDB Dev"},oss:{label:"QuestDB"},enterprise:{icon:r.createElement(ba.S,{size:"18px"}),label:"QuestDB Enterprise"},"enterprise pro":{icon:r.createElement(Ea.H,{size:"18px"}),label:"QuestDB Enterprise Pro"},"enterprise ultimate":{icon:r.createElement(ya.$,{size:"18px"}),label:"QuestDB Enterprise Ultimate"}},Ta=()=>{const{quest:e,buildVersion:t,commitHash:n}=(0,r.useContext)(qr),[a,o]=(0,r.useState)(null);if((0,r.useEffect)((()=>{t.version&&t.type.includes("oss")&&e.getLatestRelease().then((e=>{e.name&&o(e)})).catch((e=>{console.error(e)}))}),[t]),""===t.version&&!n.length)return null;const i=t.type.includes("enterprise"),l=((e,t)=>{if(void 0===t)return!1;const n=e.type.includes("enterprise");try{if(e.version){const r=(0,wa.qu)(e.version,t,"<");return!n&&r}return!1}catch(e){return!1}})(t,a?.name),s=l?a?.html_url:"https://github.com/questdb/questdb"+(t?`/releases/tag/${t.version}`:`/commit/${n}`),{label:c,icon:d}=ka[t.type]??ka.dev;return r.createElement(Ca,null,r.createElement(_a,{href:i?"https://questdb.io/enterprise":s,rel:"noopener noreferrer",target:"_blank"},r.createElement(va,{enterprise:i,title:["dev","oss"].includes(t.type)?"Show "+(t?"release notes":"commit details"):""},d,c,t.version?` ${t.version}`:"",!i&&r.createElement(ha.d,{size:"16px"}),l&&r.createElement(r.Fragment,null,r.createElement(xa,{size:"18px"}),r.createElement(Sa,null,a?.name)))))},Na=s.ZP.div.withConfig({displayName:"ConnectionStatus__Wrapper"})(["\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-start;\n  align-items: center;\n"]),Oa=s.ZP.div.withConfig({displayName:"ConnectionStatus__StatusIcon"})(["\n  display: block;\n  width: 0.6rem;\n  height: 0.6rem;\n  border-radius: 50%;\n  background-color: ",";\n  margin-right: 0.6rem;\n"],(e=>e.isConnected?M("green"):M("red"))),Ra=()=>{const[e,t]=(0,r.useState)(!0);return(0,r.useEffect)((()=>{I.subscribe(A.MSG_CONNECTION_OK,(()=>{t(!0)})),I.subscribe(A.MSG_CONNECTION_ERROR,(()=>{t(!1)}))}),[e]),r.createElement(Na,null,r.createElement(Oa,{isConnected:e}),e?r.createElement(Pe,{color:"white"},"Connected"):r.createElement(Pe,{color:"white"},"Error connecting to QuestDB"))},Ia=s.ZP.div.withConfig({displayName:"Footer__Wrapper"})(["\n  position: absolute;\n  display: flex;\n  height: 4rem;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding-left: 45px;\n"]),Aa=s.ZP.div.withConfig({displayName:"Footer__LeftContainer"})(["\n  display: flex;\n  padding-left: 1rem;\n  align-items: center;\n  flex: 1;\n"]),La=s.ZP.div.withConfig({displayName:"Footer__RightContainer"})(["\n  display: flex;\n  padding-right: 1rem;\n  align-items: center;\n\n  & > *:not(:last-child) {\n    margin-right: 1rem;\n  }\n"]),Pa=(0,s.vJ)(["\n  .github-banner-enter {\n    max-height: 0;\n  }\n\n  .github-banner-enter-active {\n    max-height: 4rem;\n    transition: all ","ms;\n  }\n\n  .github-banner-exit,\n  .github-banner-enter-done {\n    max-height: 4rem;\n  }\n\n  .github-banner-exit-active {\n    max-height: 0;\n    transition: all ","ms;\n  }\n"],K.REG,K.REG),Da=()=>{const[e,t]=(0,r.useState)(!1),[n,a]=(0,r.useState)(!0),o=(0,r.useCallback)((()=>{t(!1)}),[]),{consoleConfig:i}=Ir();return(0,r.useEffect)((()=>{setTimeout((()=>{t(!0)}),2e3),I.subscribe(A.MSG_CONNECTION_ERROR,(()=>{a(!1)})),I.subscribe(A.MSG_CONNECTION_OK,(()=>{a(!0)}))}),[]),r.createElement(Ia,{id:"footer"},r.createElement(Aa,null,r.createElement(Pe,{color:"foreground"},"Copyright © ",(new Date).getFullYear()," QuestDB")),r.createElement(La,null,r.createElement(Ra,null),n&&r.createElement(Ta,null),r.createElement(ot,{color:"foreground",hoverColor:"cyan",href:"https://github.com/questdb/questdb",rel:"noreferrer",target:"_blank"},r.createElement(vn.E,{size:"18px"}))),r.createElement(Pa,null),r.createElement(ct.Z,{classNames:"github-banner",in:e&&i.githubBanner,timeout:K.REG,unmountOnExit:!0},r.createElement(Nn,{onClick:o})))};var Ma=n(42884),Ba=n(82259);const Fa=(0,s.F4)(["\n  0% {\n    background-position: left bottom;\n  }\n\n  100% {\n    background-position: right bottom;\n  }\n"]),Ha=s.ZP.div.withConfig({displayName:"Loader__Wrapper"})(["\n  position: fixed;\n  height: 0.4rem;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 15;\n  background: linear-gradient(\n      to left,\n      "," 30%,\n      "," 80%,\n      "," 100%\n    )\n    repeat;\n  background-size: 50% 100%;\n  animation-name: ",";\n  animation-duration: 1s;\n  animation-iteration-count: infinite;\n  animation-timing-function: linear;\n"],M("selection"),M("foreground"),M("selection"),Fa),Va=({show:e})=>{const[t,n]=(0,r.useState)(!1),a=(0,r.useRef)();return(0,r.useEffect)((()=>()=>{clearTimeout(a.current)}),[]),(0,r.useEffect)((()=>{clearTimeout(a.current),e?a.current=window.setTimeout((()=>{n(!0)}),500):n(!1)}),[e]),r.createElement(r.Fragment,null,r.createElement(ct.Z,{classNames:"fade-slow",in:t&&e,timeout:K.SLOW,unmountOnExit:!0},r.createElement(Ha,null)))},za=s.ZP.div.withConfig({displayName:"QueryResult__Wrapper"})(["\n  display: flex;\n  align-items: center;\n  overflow: hidden;\n  ",";\n\n  svg {\n    margin-right: 0.2rem;\n    color: ",";\n  }\n"],J,M("foreground")),Za=s.ZP.div.withConfig({displayName:"QueryResult__Details"})(["\n  display: flex;\n"]),Ua=s.ZP.div.withConfig({displayName:"QueryResult__DetailsColumn"})(["\n  margin-left: 1rem;\n"]),qa=(0,s.ZP)(Pe).withConfig({displayName:"QueryResult__DetailsText"})(["\n  margin-right: 0.5rem;\n"]),Ga=e=>Math.round(100*(e+Number.EPSILON))/100,$a=e=>"0"===e?r.createElement(Pe,{color:"gray2"},"0"):r.createElement(Pe,{color:"orange"},e),ja=e=>0===e?"0":e>1e9?`${Ga(e/1e9)}s`:e>1e6?`${Ga(e/1e6)}ms`:e>1e3?`${Ga(e/1e3)}μs`:`${e}ns`,Wa=({compiler:e,authentication:t,count:n,execute:a,fetch:o,rowCount:i})=>r.createElement(za,{_height:95,duration:K.FAST},r.createElement("div",null,r.createElement(Pe,{color:"gray2"},i.toLocaleString()," row",i>1||0===i?"s":""," in ",ja(o))),r.createElement(Za,null,r.createElement(Ua,null,r.createElement(qa,{color:"foreground"},"Execute: ",$a(ja(a))),r.createElement(qa,{color:"foreground"},"Network: ",$a(ja(o-a))),r.createElement(qa,{color:"foreground"},"Total: ",$a(ja(o)))),r.createElement(Ua,null,r.createElement(qa,{align:"right",color:"gray2",size:"sm"},"Count: ",ja(n)),r.createElement(qa,{align:"right",color:"gray2",size:"sm"},"Authentication: ",ja(t)),r.createElement(qa,{align:"right",color:"gray2",size:"sm"},"Compile: ",ja(e))))),Qa="#2c2e3d",Ya={base:"vs-dark",inherit:!0,rules:[{background:Qa,token:""},{foreground:"6272a4",token:"comment"},{foreground:"f1fa8c",token:"string"},{foreground:"50fa7b",token:"number"},{foreground:"f1fa8c",token:"string.sql"},{foreground:"ff79c6",token:"operator.sql"},{foreground:"8be9fd",fontStyle:"italic",token:"dataType"},{foreground:"8be9fd",token:"predefined.sql"},{foreground:"8be9fd",token:"function"},{foreground:"bd93f9",token:"constant.numeric"},{foreground:"bd93f9",token:"constant.language"},{foreground:"bd93f9",token:"constant.character"},{foreground:"bd93f9",token:"constant.other"},{foreground:"ffb86c",token:"variable.other.readwrite.instance"},{foreground:"ff79c6",token:"constant.character.escaped"},{foreground:"ff79c6",token:"constant.character.escape"},{foreground:"ff79c6",token:"string source"},{foreground:"ff79c6",token:"string source.ruby"},{foreground:"ff79c6",token:"keyword"},{foreground:"ff79c6",token:"storage"},{foreground:"8be9fd",fontStyle:"italic",token:"storage.type"},{foreground:"50fa7b",fontStyle:"underline",token:"entity.name.class"},{foreground:"50fa7b",fontStyle:"italic underline",token:"entity.other.inherited-class"},{foreground:"50fa7b",token:"entity.name.function"},{foreground:"ffb86c",fontStyle:"italic",token:"variable.parameter"},{foreground:"ff79c6",token:"entity.name.tag"},{foreground:"50fa7b",token:"entity.other.attribute-name"},{foreground:"8be9fd",token:"support.function"},{foreground:"6be5fd",token:"support.constant"},{foreground:"66d9ef",fontStyle:" italic",token:"support.type"},{foreground:"66d9ef",fontStyle:" italic",token:"support.class"},{foreground:"f8f8f0",background:"ff79c6",token:"invalid"},{foreground:"f8f8f0",background:"bd93f9",token:"invalid.deprecated"},{foreground:"cfcfc2",token:"meta.structure.dictionary.json string.quoted.double.json"},{foreground:"6272a4",token:"meta.diff"},{foreground:"6272a4",token:"meta.diff.header"},{foreground:"ff79c6",token:"markup.deleted"},{foreground:"50fa7b",token:"markup.inserted"},{foreground:"e6db74",token:"markup.changed"},{foreground:"bd93f9",token:"constant.numeric.line-number.find-in-files - match"},{foreground:"e6db74",token:"entity.name.filename"},{foreground:"f83333",token:"message.error"},{foreground:"eeeeee",token:"punctuation.definition.string.begin.json - meta.structure.dictionary.value.json"},{foreground:"eeeeee",token:"punctuation.definition.string.end.json - meta.structure.dictionary.value.json"},{foreground:"8be9fd",token:"meta.structure.dictionary.json string.quoted.double.json"},{foreground:"f1fa8c",token:"meta.structure.dictionary.value.json string.quoted.double.json"},{foreground:"50fa7b",token:"meta meta meta meta meta meta meta.structure.dictionary.value string"},{foreground:"ffb86c",token:"meta meta meta meta meta meta.structure.dictionary.value string"},{foreground:"ff79c6",token:"meta meta meta meta meta.structure.dictionary.value string"},{foreground:"bd93f9",token:"meta meta meta meta.structure.dictionary.value string"},{foreground:"50fa7b",token:"meta meta meta.structure.dictionary.value string"},{foreground:"ffb86c",token:"meta meta.structure.dictionary.value string"}],colors:{"activityBar.activeBackground":"#bd93f910","activityBar.activeBorder":"#ff79c680","activityBar.background":"#343746","activityBar.foreground":"#f8f8f2","activityBar.inactiveForeground":"#6272a4","activityBarBadge.background":"#ff79c6","activityBarBadge.foreground":"#f8f8f2","badge.background":"#44475a","badge.foreground":"#f8f8f2","breadcrumb.activeSelectionForeground":"#f8f8f2","breadcrumb.background":Qa,"breadcrumb.focusForeground":"#f8f8f2","breadcrumb.foreground":"#6272a4","breadcrumbPicker.background":"#191a21","button.background":"#44475a","button.foreground":"#f8f8f2","button.secondaryBackground":Qa,"button.secondaryForeground":"#f8f8f2","button.secondaryHoverBackground":"#343746","debugToolBar.background":"#21222c","diffEditor.insertedTextBackground":"#50fa7b20","diffEditor.removedTextBackground":"#ff555550","dropdown.background":"#343746","dropdown.border":"#191a21","dropdown.foreground":"#f8f8f2","editor.background":Qa,"editor.findMatchBackground":"#ffb86c80","editor.findMatchHighlightBackground":"#ffffff40","editor.findRangeHighlightBackground":"#44475a75","editor.foldBackground":"#21222c","editor.foreground":"#f8f8f2","editor.hoverHighlightBackground":"#8be9fd50","editor.lineHighlightBorder":"#383b4b","editor.lineHighlightBackground":"#383b4b","editor.rangeHighlightBackground":"#bd93f915","editor.selectionBackground":"#44475a","editor.selectionHighlightBackground":"#424450","editor.snippetFinalTabstopHighlightBackground":Qa,"editor.snippetFinalTabstopHighlightBorder":"#50fa7b","editor.snippetTabstopHighlightBackground":Qa,"editor.snippetTabstopHighlightBorder":"#6272a4","editor.wordHighlightBackground":"#8be9fd50","editor.wordHighlightStrongBackground":"#50fa7b50","editorBracketHighlight.foreground1":"#f8f8f2","editorBracketHighlight.foreground2":"#ff79c6","editorBracketHighlight.foreground3":"#8be9fd","editorBracketHighlight.foreground4":"#50fa7b","editorBracketHighlight.foreground5":"#bd93f9","editorBracketHighlight.foreground6":"#ffb86c","editorBracketHighlight.unexpectedBracket.foreground":"#ff5555","editorCodeLens.foreground":"#6272a4","editorError.foreground":"#ff5555","editorGroup.border":"#bd93f9","editorGroup.dropBackground":"#44475a70","editorGroupHeader.tabsBackground":"#191a21","editorGutter.addedBackground":"#50fa7b80","editorGutter.deletedBackground":"#ff555580","editorGutter.modifiedBackground":"#8be9fd80","editorHoverWidget.background":Qa,"editorHoverWidget.border":"#6272a4","editorIndentGuide.activeBackground":"#ffffff45","editorIndentGuide.background":"#ffffff1a","editorLineNumber.foreground":"#6272a4","editorLink.activeForeground":"#8be9fd","editorMarkerNavigation.background":"#21222c","editorOverviewRuler.addedForeground":"#50fa7b80","editorOverviewRuler.border":"#191a21","editorOverviewRuler.currentContentForeground":"#50fa7b","editorOverviewRuler.deletedForeground":"#ff555580","editorOverviewRuler.errorForeground":"#ff555580","editorOverviewRuler.incomingContentForeground":"#bd93f9","editorOverviewRuler.infoForeground":"#8be9fd80","editorOverviewRuler.modifiedForeground":"#8be9fd80","editorOverviewRuler.selectionHighlightForeground":"#ffb86c","editorOverviewRuler.warningForeground":"#ffb86c80","editorOverviewRuler.wordHighlightForeground":"#8be9fd","editorOverviewRuler.wordHighlightStrongForeground":"#50fa7b","editorRuler.foreground":"#ffffff1a","editorSuggestWidget.background":"#21222c","editorSuggestWidget.foreground":"#f8f8f2","editorSuggestWidget.selectedBackground":"#44475a","editorWarning.foreground":"#8be9fd","editorWhitespace.foreground":"#ffffff1a","editorWidget.background":"#21222c",errorForeground:"#ff5555","extensionButton.prominentBackground":"#50fa7b90","extensionButton.prominentForeground":"#f8f8f2","extensionButton.prominentHoverBackground":"#50fa7b60",focusBorder:"#6272a4",foreground:"#f8f8f2","gitDecoration.conflictingResourceForeground":"#ffb86c","gitDecoration.deletedResourceForeground":"#ff5555","gitDecoration.ignoredResourceForeground":"#6272a4","gitDecoration.modifiedResourceForeground":"#8be9fd","gitDecoration.untrackedResourceForeground":"#50fa7b","input.background":Qa,"input.border":"#191a21","input.foreground":"#f8f8f2","input.placeholderForeground":"#6272a4","inputOption.activeBorder":"#bd93f9","inputValidation.errorBorder":"#ff5555","inputValidation.infoBorder":"#ff79c6","inputValidation.warningBorder":"#ffb86c","list.activeSelectionBackground":"#44475a","list.activeSelectionForeground":"#f8f8f2","list.dropBackground":"#44475a","list.errorForeground":"#ff5555","list.focusBackground":"#44475a75","list.highlightForeground":"#8be9fd","list.hoverBackground":"#44475a75","list.inactiveSelectionBackground":"#44475a75","list.warningForeground":"#ffb86c","listFilterWidget.background":"#343746","listFilterWidget.noMatchesOutline":"#ff5555","listFilterWidget.outline":"#424450","merge.currentHeaderBackground":"#50fa7b90","merge.incomingHeaderBackground":"#bd93f990","panel.background":Qa,"panel.border":"#bd93f9","panelTitle.activeBorder":"#ff79c6","panelTitle.activeForeground":"#f8f8f2","panelTitle.inactiveForeground":"#6272a4","peekView.border":"#44475a","peekViewEditor.background":Qa,"peekViewEditor.matchHighlightBackground":"#f1fa8c80","peekViewResult.background":"#21222c","peekViewResult.fileForeground":"#f8f8f2","peekViewResult.lineForeground":"#f8f8f2","peekViewResult.matchHighlightBackground":"#f1fa8c80","peekViewResult.selectionBackground":"#44475a","peekViewResult.selectionForeground":"#f8f8f2","peekViewTitle.background":"#191a21","peekViewTitleDescription.foreground":"#6272a4","peekViewTitleLabel.foreground":"#f8f8f2","pickerGroup.border":"#bd93f9","pickerGroup.foreground":"#8be9fd","progressBar.background":"#ff79c6","selection.background":"#bd93f9","settings.checkboxBackground":"#21222c","settings.checkboxBorder":"#191a21","settings.checkboxForeground":"#f8f8f2","settings.dropdownBackground":"#21222c","settings.dropdownBorder":"#191a21","settings.dropdownForeground":"#f8f8f2","settings.headerForeground":"#f8f8f2","settings.modifiedItemIndicator":"#ffb86c","settings.numberInputBackground":"#21222c","settings.numberInputBorder":"#191a21","settings.numberInputForeground":"#f8f8f2","settings.textInputBackground":"#21222c","settings.textInputBorder":"#191a21","settings.textInputForeground":"#f8f8f2","sideBar.background":"#21222c","sideBarSectionHeader.background":Qa,"sideBarSectionHeader.border":"#191a21","sideBarTitle.foreground":"#f8f8f2","statusBar.background":"#191a21","statusBar.debuggingBackground":"#ff5555","statusBar.debuggingForeground":"#191a21","statusBar.foreground":"#f8f8f2","statusBar.noFolderBackground":"#191a21","statusBar.noFolderForeground":"#f8f8f2","statusBarItem.prominentBackground":"#ff5555","statusBarItem.prominentHoverBackground":"#ffb86c","statusBarItem.remoteBackground":"#bd93f9","statusBarItem.remoteForeground":Qa,"tab.activeBackground":Qa,"tab.activeBorderTop":"#ff79c680","tab.activeForeground":"#f8f8f2","tab.border":"#191a21","tab.inactiveBackground":"#21222c","tab.inactiveForeground":"#6272a4","terminal.ansiBlack":"#21222c","terminal.ansiBlue":"#bd93f9","terminal.ansiBrightBlack":"#6272a4","terminal.ansiBrightBlue":"#d6acff","terminal.ansiBrightCyan":"#a4ffff","terminal.ansiBrightGreen":"#69ff94","terminal.ansiBrightMagenta":"#ff92df","terminal.ansiBrightRed":"#ff6e6e","terminal.ansiBrightWhite":"#ffffff","terminal.ansiBrightYellow":"#ffffa5","terminal.ansiCyan":"#8be9fd","terminal.ansiGreen":"#50fa7b","terminal.ansiMagenta":"#ff79c6","terminal.ansiRed":"#ff5555","terminal.ansiWhite":"#f8f8f2","terminal.ansiYellow":"#f1fa8c","terminal.background":Qa,"terminal.foreground":"#f8f8f2","titleBar.activeBackground":"#21222c","titleBar.activeForeground":"#f8f8f2","titleBar.inactiveBackground":"#191a21","titleBar.inactiveForeground":"#6272a4","walkThrough.embeddedEditorBackground":"#21222c"}},Ka={wordPattern:/(-?\d*\.\d\w*)|(::|:=|<<=|>>=|!=|<>|<=|>=|\|\||[-+*/%~<>^|&=!]|\b(?:not|and|or|in|between|within|like|ilike)\b|[^\`\~\!\@\#\$\%\^\&\*\-\+\[\{\]\}\\\|\;\:\"\,\<\>\/\?\s]+)/g,comments:{lineComment:"--",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"],["(",")"]],autoClosingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}],surroundingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}]},Xa=["ALL","AND","ANY","BETWEEN","EXISTS","IN","LIKE","NOT","OR","SOME","EXCEPT","INTERSECT","UNION","APPLY","CROSS","FULL","INNER","JOIN","LEFT","OUTER","RIGHT","CONTAINS","FREETEXT","IS","NULL","PIVOT","UNPIVOT","MATCHED","EXPLAIN"];var Ja=n(54762);const eo={defaultToken:"",tokenPostfix:".sql",ignoreCase:!0,brackets:[{open:"[",close:"]",token:"delimiter.square"},{open:"(",close:")",token:"delimiter.parenthesis"}],dataTypes:Ja.DJ,keywords:Ja.Hw,operators:Xa,builtinFunctions:Ja.wk,builtinVariables:["@@DATEFIRST","@@DBTS","@@LANGID","@@LANGUAGE","@@LOCK_TIMEOUT","@@MAX_CONNECTIONS","@@MAX_PRECISION","@@NESTLEVEL","@@OPTIONS","@@REMSERVER","@@SERVERNAME","@@SERVICENAME","@@SPID","@@TEXTSIZE","@@VERSION","@@CURSOR_ROWS","@@FETCH_STATUS","@@DATEFIRST","@@PROCID","@@ERROR","@@IDENTITY","@@ROWCOUNT","@@TRANCOUNT","@@CONNECTIONS","@@CPU_BUSY","@@IDLE","@@IO_BUSY","@@PACKET_ERRORS","@@PACK_RECEIVED","@@PACK_SENT","@@TIMETICKS","@@TOTAL_ERRORS","@@TOTAL_READ","@@TOTAL_WRITE"],pseudoColumns:["$ACTION","$IDENTITY","$ROWGUID","$PARTITION"],tokenizer:{root:[{include:"@comments"},{include:"@whitespace"},{include:"@pseudoColumns"},{include:"@numbers"},{include:"@strings"},{include:"@complexIdentifiers"},{include:"@scopes"},{include:"@array"},[/[;,.]/,"delimiter"],[/[()]/,"@brackets"],[/[\w@#$]+/,{cases:{"@operators":"operator","@builtinVariables":"predefined","@builtinFunctions":"predefined","@keywords":"keyword","@dataTypes":"dataType","@default":"identifier"}}],[/[<>=!%&+\-*/|~^]/,"operator"]],whitespace:[[/\s+/,"white"]],comments:[[/--+.*/,"comment"],[/\/\*/,{token:"comment.quote",next:"@comment"}]],comment:[[/[^*/]+/,"comment"],[/\*\//,{token:"comment.quote",next:"@pop"}],[/./,"comment"]],pseudoColumns:[[/[$][A-Za-z_][\w@#$]*/,{cases:{"@pseudoColumns":"predefined","@default":"identifier"}}]],numbers:[[/([+-]?\d+\.\d+[eE]?[+-]?\d+)/,"number"],[/0[xX][0-9a-fA-F]*/,"number"],[/[+-]?\d+((_)?\d+)*[Ll]?/,"number"]],strings:[[/N'/,{token:"string",next:"@string"}],[/'/,{token:"string",next:"@string"}]],string:[[/[^']+/,"string"],[/''/,"string"],[/'/,{token:"string",next:"@pop"}]],complexIdentifiers:[[/\[/,{token:"identifier.quote",next:"@bracketedIdentifier"}],[/"/,{token:"identifier.quote",next:"@quotedIdentifier"}]],bracketedIdentifier:[[/[^\]]+/,"identifier"],[/]]/,"identifier"],[/]/,{token:"identifier.quote",next:"@pop"}]],quotedIdentifier:[[/[^"]+/,"identifier"],[/""/,"identifier"],[/"/,{token:"identifier.quote",next:"@pop"}]],scopes:[[/BEGIN\s+(DISTRIBUTED\s+)?TRAN(SACTION)?\b/i,"keyword"],[/BEGIN\s+TRY\b/i,{token:"keyword.try"}],[/END\s+TRY\b/i,{token:"keyword.try"}],[/BEGIN\s+CATCH\b/i,{token:"keyword.catch"}],[/END\s+CATCH\b/i,{token:"keyword.catch"}],[/(BEGIN|CASE)\b/i,{token:"keyword.block"}],[/END\b/i,{token:"keyword.block"}],[/WHEN\b/i,{token:"keyword.choice"}],[/THEN\b/i,{token:"keyword.choice"}]],array:[[/ARRAY\s*\[/,{token:"keyword",next:"@arrayArguments"}]],arrayArguments:[{include:"@comments"},{include:"@whitespace"},{include:"@numbers"},{include:"@strings"},[/\[/,{token:"delimiter.square",next:"@arrayArguments"}],[/\]/,{token:"delimiter.square",next:"@pop"}],[/,/,"delimiter"]]}};let to=function(e){return e.High="1",e.MediumHigh="2",e.Medium="3",e.MediumLow="4",e.Low="5",e}({}),no=function(e){return e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet",e}({});const ro=({tables:e,range:t,priority:n,openQuote:r,nextCharQuote:a})=>e.map((e=>({label:e.table_name,kind:no.Class,insertText:r?e.table_name+(a?"":'"'):/^[a-z0-9_]+$/i.test(e.table_name)?e.table_name:`"${e.table_name}"`,sortText:n,range:t}))),ao=({columns:e,range:t,withTableName:n,priority:r})=>n?e.map((e=>({label:{label:`${e.table_name}.${e.column_name}`,detail:"",description:e.data_type},kind:no.Enum,insertText:`${e.table_name}.${e.column_name}`,sortText:r,range:t}))):Y(e.map((e=>e.column_name))).map((n=>{const a=e.filter((e=>e.column_name===n)).map((e=>e.table_name));return{label:{label:n,detail:` (${a.sort().join(", ")})`,description:a.length>1?"":e.find((e=>e.column_name===n))?.data_type},kind:no.Enum,insertText:n,sortText:r,range:t}})),oo=e=>[...Ja.wk.map((t=>({label:t,kind:no.Function,insertText:t,range:e}))),...Ja.DJ.map((t=>({label:t,kind:no.Keyword,insertText:t,range:e}))),...Ja.Hw.map((t=>{const n=t.toUpperCase();return{label:n,kind:no.Keyword,filterText:t,insertText:n,range:e}})),...Xa.map((t=>{const n=t.toUpperCase();return{label:n,kind:no.Operator,filterText:t,insertText:n,range:e}}))],io=e=>e.replace(/(^")|("$)/g,""),lo=e=>e.match(/(?:,$|,\s$|\b(?:SELECT|UPDATE|COLUMN|ON|JOIN|BY|WHERE|DISTINCT)\s$)/gim),so={provideDocumentFormattingEdits(e,t){const n=F(e.getValue(),{indent:" ".repeat(t.tabSize)});return[{range:e.getFullModelRange(),text:n}]}},co={provideDocumentRangeFormattingEdits:(e,t,n)=>[{range:t,text:F(e.getValueInRange(t),{indent:" ".repeat(n.tabSize)})}]};var uo=function(e){return e.EXECUTE="execute",e.FOCUS_GRID="focus_grid",e.ADD_NEW_TAB="add_new_tab",e.CLOSE_ACTIVE_TAB="close_active_tab",e.SEARCH_DOCS="search_docs",e}(uo||{}),mo=n(8635),po=n(90281);function go(){return go=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},go.apply(this,arguments)}const fo=(0,s.ZP)(Dn.z).withConfig({displayName:"CopyButton__StyledButton"})(["\n  padding: 1.2rem 0.6rem;\n"]),ho=(0,s.ZP)(po.e).withConfig({displayName:"CopyButton__StyledCheckboxCircle"})(["\n  position: absolute;\n  transform: translate(75%, -75%);\n  color: ",";\n"],(({theme:e})=>e.color.green)),bo=({text:e,iconOnly:t})=>{const[n,a]=(0,r.useState)(!1);return r.createElement(fo,go({skin:"secondary","data-hook":"copy-value",onClick:t=>{p(e),t.stopPropagation(),a(!0),setTimeout((()=>a(!1)),2e3)}},!t&&{prefixIcon:r.createElement(mo.f,{size:"16px"})}),n&&r.createElement(ho,{size:"14px"}),t?r.createElement(mo.f,{size:"16px"}):"Copy")},Eo=(0,s.ZP)(Pe).withConfig({displayName:"query-in-notification__StyledText"})(["\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n"]),yo=({query:e})=>e?r.createElement(Pn.x,{gap:"1rem",align:"center"},r.createElement(bo,{text:e,iconOnly:!0}),r.createElement(Eo,{color:"foreground",title:e},e)):null;function wo(){return wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},wo.apply(this,arguments)}Ma._m.config({paths:{vs:"assets/vs"}});const Co=(0,s.ZP)(it).withConfig({displayName:"Monaco__Content"})(["\n  position: relative;\n  overflow: hidden;\n  background: #2c2e3d;\n\n  .monaco-editor .squiggly-error {\n    background: none;\n    border-bottom: 0.3rem "," solid;\n  }\n\n  .monaco-scrollable-element > .scrollbar > .slider {\n    background: ",";\n  }\n\n  .cursorQueryDecoration {\n    width: 0.2rem !important;\n    background: ",";\n    margin-left: 1.2rem;\n\n    &.hasError {\n      background: ",';\n    }\n  }\n\n  .cursorQueryGlyph,\n  .cancelQueryGlyph {\n    margin-left: 2rem;\n    z-index: 1;\n    cursor: pointer;\n\n    &:after {\n      display: block;\n      content: "";\n      width: 18px;\n      height: 18px;\n    }\n  }\n\n  .cursorQueryGlyph {\n    &:after {\n      background-image: url("data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIGhlaWdodD0iMThweCIgd2lkdGg9IjE4cHgiIGFyaWEtaGlkZGVuPSJ0cnVlIiBmb2N1c2FibGU9ImZhbHNlIiBmaWxsPSIjNTBmYTdiIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJTdHlsZWRJY29uQmFzZS1zYy1lYTl1bGotMCBrZkRiTmwiPjxwYXRoIGZpbGw9Im5vbmUiIGQ9Ik0wIDBoMjR2MjRIMHoiPjwvcGF0aD48cGF0aCBkPSJNMTYuMzk0IDEyIDEwIDcuNzM3djguNTI2TDE2LjM5NCAxMnptMi45ODIuNDE2TDguNzc3IDE5LjQ4MkEuNS41IDAgMCAxIDggMTkuMDY2VjQuOTM0YS41LjUgMCAwIDEgLjc3Ny0uNDE2bDEwLjU5OSA3LjA2NmEuNS41IDAgMCAxIDAgLjgzMnoiPjwvcGF0aD48L3N2Zz4K");\n    }\n  }\n\n  .cancelQueryGlyph {\n    &:after {\n      background-image: url("data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIGhlaWdodD0iMThweCIgd2lkdGg9IjE4cHgiIGFyaWEtaGlkZGVuPSJ0cnVlIiBmb2N1c2FibGU9ImZhbHNlIiBmaWxsPSIjZmY1NTU1IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJTdHlsZWRJY29uQmFzZS1zYy1lYTl1bGotMCBqQ2hkR0siPjxwYXRoIGZpbGw9Im5vbmUiIGQ9Ik0wIDBoMjR2MjRIMHoiPjwvcGF0aD48cGF0aCBkPSJNNyA3djEwaDEwVjdIN3pNNiA1aDEyYTEgMSAwIDAgMSAxIDF2MTJhMSAxIDAgMCAxLTEgMUg2YTEgMSAwIDAgMS0xLTFWNmExIDEgMCAwIDEgMS0xeiI+PC9wYXRoPjwvc3ZnPgo=");\n    }\n  }\n\n  .errorGlyph {\n    margin-left: 2.5rem;\n    margin-top: 0.5rem;\n    z-index: 1;\n    width: 0.75rem !important;\n    height: 0.75rem !important;\n    border-radius: 50%;\n    background: ',";\n  }\n"],M("red"),M("selection"),M("green"),M("red"),M("red")),vo=(0,s.ZP)(Dn.z).withConfig({displayName:"Monaco__CancelButton"})(["\n  padding: 1.2rem 0.6rem;\n"]),_o=()=>{const e=ma(),{buffers:t,editorRef:n,monacoRef:a,insertTextAtCursor:i,activeBuffer:l,updateBuffer:s,editorReadyTrigger:c,addBuffer:d}=e,{quest:u}=(0,r.useContext)(qr),[m,p]=(0,r.useState)(),[g,f]=(0,r.useState)(!1),[h,b]=(0,r.useState)(""),[E,y]=(0,r.useState)(!1),w=(0,o.I0)(),C=(0,o.v9)(Cn.query.getRunning),v=(0,o.v9)(Cn.query.getTables),_=(0,o.v9)(Cn.query.getColumns),[x,S]=(0,r.useState)(),k=(0,r.useRef)(),N=(0,r.useRef)(C.value),O=(0,r.useRef)(l),R=(0,r.useRef)(m),L=(0,r.useRef)({}),[P,D]=(0,r.useState)(5+l.value.split("\n").length.toString().length-1),M=(e=!1)=>{w(Yt(e))},B=(e,t)=>{const n=Qr(t);if(!n)return void k.current?.clear();const r=t.getModel();if(n&&null!==r){const a=O.current.id,o=e.editor.createModel(jr(r.getValue()),$r),i=Xr(o,n.query);if(o.dispose(),i.length>0){const r=L.current&&L.current[a]?.error?.query===n.query,o=L.current&&L.current[a]?.range,l=i.find((e=>e.range.startLineNumber===n.row+1));l&&(k.current?.clear(),k.current=t.createDecorationsCollection([{range:new e.Range(l.range.startLineNumber,1,l.range.endLineNumber,1),options:{isWholeLine:!0,linesDecorationsClassName:"cursorQueryDecoration "+(r?"hasError":"")}},{range:new e.Range(l.range.startLineNumber,1,l.range.startLineNumber,1),options:{isWholeLine:!1,glyphMarginClassName:N.current&&R.current?.row&&R.current?.row+1===l.range.startLineNumber?"cancelQueryGlyph":N.current?"":"cursorQueryGlyph"}},...r&&o&&l.range.startLineNumber!==o.startLineNumber?[{range:new e.Range(o.startLineNumber,0,o.startLineNumber,0),options:{isWholeLine:!1,glyphMarginClassName:"errorGlyph"}}]:[]]))}}};(0,r.useEffect)((()=>{Object.keys(L.current).map((e=>{t.find((t=>t.id===parseInt(e)))||delete L.current[e]}))}),[t]),(0,r.useEffect)((()=>{O.current=l}),[l]),(0,r.useEffect)((()=>{!C.value&&m&&(u.abort(),w(Qt()),p(void 0))}),[m,u,w,C]),(0,r.useEffect)((()=>{if(N.current=C.value,C.value&&n?.current){a?.current&&Kr(a.current,n.current),a?.current&&n?.current&&B(a.current,n?.current);const t=C.isRefresh?{query:h,row:0,column:0}:(e=n.current,Wr(e)?(e=>{const t=e.getSelection(),n=Wr(e);if(t&&n){let e=n.length,r=n.charAt(e);for(;e>0&&(" "===r||"\n"===r||";"===r);)e--,r=n.charAt(e);if(e>0)return{query:n.substr(0,e+1),row:t.startLineNumber-1,column:t.startColumn}}})(e):Qr(e));t?.query?(setTimeout((()=>{N.current&&w($t({type:Zt.LOADING,content:r.createElement(Pn.x,{gap:"1rem",align:"center"},r.createElement(Pe,{color:"foreground"},"Running..."),r.createElement(vo,{skin:"error",onClick:()=>M()},r.createElement(Ba.d,{size:"18px"}))),sideContent:r.createElement(yo,{query:t.query})}))}),1e3),u.queryRaw(t.query,{limit:"0,1000",explain:!0}).then((e=>{p(void 0),delete L.current[l.id],w(Qt()),w(Wt(e)),e.type!==T.DDL&&e.type!==T.DML||(w($t({content:r.createElement(yo,{query:e.query})})),I.publish(A.MSG_QUERY_SCHEMA)),e.type===T.NOTICE&&(w($t({content:r.createElement(Pe,{color:"foreground",ellipsis:!0,title:e.query},e.notice,void 0!==e.query&&""!==e.query&&`: ${e.query}`),type:Zt.NOTICE})),I.publish(A.MSG_QUERY_SCHEMA)),e.type===T.DQL&&(b(t.query),w($t({jitCompiled:e.explain?.jitCompiled??!1,content:r.createElement(Wa,wo({},e.timings,{rowCount:e.count})),sideContent:r.createElement(yo,{query:e.query})})),I.publish(A.MSG_QUERY_DATASET,e))})).catch((e=>{if(L.current[l.id]={error:e},p(void 0),w(Qt()),w($t({content:r.createElement(Pe,{color:"red"},e.error),sideContent:r.createElement(yo,{query:t.query}),type:Zt.ERROR})),n?.current&&a?.current){const r=((e,t,n)=>{const r=((e,t)=>{const n=Math.min(t,e.query.length);let r=0,a=0;for(let t=0;t<n;t++)"\n"===e.query.charAt(t)?(r++,a=0):a++;return{lineNumber:r+1+e.row,column:(0===r?a+e.column:a)+1}})(t,n),a=e.getModel();if(a){const t=e.getSelection(),n=Wr(e);let o;if(o=t&&n?a.getWordAtPosition({column:t.startColumn+r.column,lineNumber:r.lineNumber}):a.getWordAtPosition(r),o)return{startColumn:o.startColumn,endColumn:o.endColumn,startLineNumber:r.lineNumber,endLineNumber:r.lineNumber}}return null})(n.current,t,e.position);r&&(L.current[l.id].range=r,((e,t,n,r)=>{const a=t.getModel();a&&e.editor.setModelMarkers(a,$r,[{message:r,severity:e.MarkerSeverity.Error,startLineNumber:n.startLineNumber,endLineNumber:n.endLineNumber,startColumn:n.startColumn,endColumn:n.endColumn}])})(a?.current,n.current,r,e.error),n?.current.focus(),n?.current.setPosition({lineNumber:r.startLineNumber,column:r.startColumn}),n?.current.revealPosition({lineNumber:r.startLineNumber,column:r.endColumn}))}})),p(t)):w(Qt())}else a?.current&&n?.current&&B(a?.current,n?.current);var e}),[C]),(0,r.useEffect)((()=>{R.current=m,a?.current&&n?.current&&B(a?.current,n?.current)}),[m]);const F=async()=>{g&&a?.current&&n?.current&&(x?.dispose(),y(!0),S(a.current.languages.registerCompletionItemProvider($r,((e,t=[],n=[])=>({triggerCharacters:'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\n ."'.split(""),provideCompletionItems(r,a){const o=r.getWordUntilPosition(a),i=Qr(e),l=r.getValueInRange({startLineNumber:a.lineNumber,startColumn:1,endLineNumber:a.lineNumber,endColumn:a.column});let s=[];const c=/^\s*$/.test(l),d=/(-- |--|\/\/ |\/\/)$/gim.test(l);if(c||d)return null;if(i){const e=Xr(r,i.query);if(e.length>0){const l=e.find((e=>e.range.startLineNumber===i.row+1)),d=i.query.match(/(?<=FROM\s)([^ )]+)/gim),u=i.query.match(/(JOIN)\s+([^ ]+)/i),m=i.query.match(/(ALTER TABLE)\s+([^ ]+)/i);d?s=Y(d):m&&m[2]&&s.push(m[2]),u&&u[2]&&s.push(u[2]),s=s.map(io);const p=r.getValueInRange({startLineNumber:l?.range.startLineNumber??1,startColumn:l?.range.startColumn??1,endLineNumber:a.lineNumber,endColumn:o.startColumn}),g={startLineNumber:a.lineNumber,endLineNumber:a.lineNumber,startColumn:o.startColumn,endColumn:o.endColumn},f=r.getValueInRange({startLineNumber:a.lineNumber,startColumn:o.endColumn,endLineNumber:a.lineNumber,endColumn:o.endColumn+1}),h='"'===p.substr(-1),b='"'==f;if(r.getValueInRange({startLineNumber:a.lineNumber,startColumn:1,endLineNumber:a.lineNumber,endColumn:a.column}),/(FROM|INTO|(ALTER|BACKUP|DROP|REINDEX|RENAME|TRUNCATE|VACUUM) TABLE|JOIN|UPDATE)\s$/gim.test(p)||/'$/gim.test(p)&&!p.endsWith("= '"))return{suggestions:ro({tables:t,range:g,priority:to.High,openQuote:h,nextCharQuote:b})};if(/(?:(SELECT|UPDATE).*?(?:(?:,(?:COLUMN )?)|(?:ALTER COLUMN ))?(?:WHERE )?(?: BY )?(?: ON )?(?: SET )?$|ALTER COLUMN )/gim.test(p)&&!c){if(s.length>0){const e=null!==p.match(/\sON\s/gim);return{suggestions:[...lo(p)?ao({columns:n.filter((e=>s.includes(e.table_name))),range:g,withTableName:e,priority:to.High}):[],...oo(g)]}}if(lo(p))return{suggestions:[...ao({columns:n,range:g,withTableName:!1,priority:to.High})]}}if(o.word)return{suggestions:[...ro({tables:t,range:g,priority:to.High,openQuote:h,nextCharQuote:b}),...oo(g)]}}}}}))(n.current,v,_))),y(!1))};return(0,r.useEffect)((()=>{E||F()}),[v,_,a,g]),(0,r.useEffect)((()=>{a.current&&n.current&&Kr(a.current,n.current)}),[l]),(0,r.useEffect)((()=>(window.addEventListener("focus",F),()=>window.removeEventListener("focus",F))),[]),r.createElement(Co,{onClick:e=>{(e.target.classList.contains("cursorQueryGlyph")||e.target.classList.contains("cancelQueryGlyph"))&&(n?.current?.focus(),M())}},r.createElement(Ma.ZP,{beforeMount:e=>{(e=>{e.languages.register({id:$r}),e.languages.setMonarchTokensProvider($r,eo),e.languages.setLanguageConfiguration($r,Ka),e.languages.registerDocumentFormattingEditProvider($r,so),e.languages.registerDocumentRangeFormattingEditProvider($r,co)})(e),e.editor.defineTheme("dracula",Ya)},defaultLanguage:$r,onMount:(t,r)=>{a.current=r,n.current=t,r.editor.setTheme("dracula"),t.setModel(r.editor.createModel(l.value,$r)),f(!0),c(t),(({editor:e,insertTextAtCursor:t,toggleRunning:n})=>{I.subscribe(A.MSG_EDITOR_INSERT_COLUMN,(e=>{e&&t(e)})),I.subscribe(A.MSG_QUERY_FIND_N_EXEC,(t=>{if(t){const r=`${t.query};`;Yr(e,r,t.options),n()}})),I.subscribe(A.MSG_QUERY_EXEC,(()=>{n(!0)})),I.subscribe(A.MSG_EDITOR_FOCUS,(()=>{const t=e.getPosition();t&&e.setPosition({lineNumber:t.lineNumber+1,column:t?.column}),e.focus()}))})({editor:t,insertTextAtCursor:i,toggleRunning:M}),(({editor:e,monaco:t,runQuery:n,dispatch:r,editorContext:a})=>{e.addAction({id:uo.EXECUTE,label:"Execute command",keybindings:[t.KeyCode.F9,t.KeyMod.CtrlCmd|t.KeyCode.Enter],run:()=>{n()}}),e.addAction({id:uo.ADD_NEW_TAB,label:"Add new tab",keybindings:[t.KeyMod.Alt|t.KeyCode.KeyT],run:()=>{a.addBuffer()}}),e.addAction({id:uo.CLOSE_ACTIVE_TAB,label:"Close current tab",keybindings:[t.KeyMod.Alt|t.KeyCode.KeyW],run:async()=>{const e=await ia(),t=await la();e.length>1&&t?.value&&"number"==typeof t?.value&&a.deleteBuffer(t.value)}}),e.addAction({id:uo.SEARCH_DOCS,label:"Search QuestDB Docs",keybindings:[t.KeyMod.CtrlCmd|t.KeyCode.KeyK],run:()=>{const e=document.querySelector(".DocSearch-Button");e&&e.click()}})})({editor:t,monaco:r,runQuery:()=>{N.current||M()},dispatch:w,editorContext:e}),t.onDidChangeCursorPosition((()=>{const e=n.current?.getModel()?.getLineCount();e&&D(e.toString().length-1+5),B(r,t)}));const o=new URLSearchParams(window.location.search),d=o.get("query"),u=t.getModel();if(d&&u){const e=Xr(u,d);if(e&&e.length>0)t.setSelection(e[0].range);else{Yr(t,d,{appendAt:"end"});const e=t.getValue();s(l.id,{value:e})}}o.get("executeQuery")&&M()},saveViewState:!1,onChange:e=>{s(l.id,{value:e})},options:{model:null,fixedOverflowWidgets:!0,fontSize:14,fontFamily:Lt.fontMonospace,glyphMargin:!0,renderLineHighlight:"gutter",minimap:{enabled:!1},selectOnLineNumbers:!1,scrollBeyondLastLine:!1,tabSize:2,lineNumbersMinChars:P},theme:"vs-dark"}),r.createElement(Va,{show:!!m||!v}))};var xo=n(72320),So=n.n(xo);const ko=e=>{},To={title:"New tab",favicon:!1};let No=0;const Oo=class{constructor(){this.draggabillies=[]}init(e,t){this.el=e,this.limit=t,this.instanceId=No,this.el.setAttribute("data-chrome-tabs-instance-id",this.instanceId+""),No+=1,this.setupCustomProperties(),this.setupStyleEl(),this.setupEvents(),this.layoutTabs(),this.setupNewTabButton(),this.setupDraggabilly()}emit(e,t){this.el.dispatchEvent(new CustomEvent(e,{detail:t}))}setupCustomProperties(){this.el.style.setProperty("--tab-content-margin","10px")}setupStyleEl(){this.styleEl=document.createElement("style"),this.el.appendChild(this.styleEl)}setupEvents(){window.addEventListener("resize",(e=>{this.cleanUpPreviouslyDraggedTabs(),this.layoutTabs()})),new ResizeObserver((e=>{this.cleanUpPreviouslyDraggedTabs(),this.layoutTabs()})).observe(this.el),this.el.addEventListener("click",(({target:e})=>{e instanceof Element&&e.classList.contains("new-tab-button")&&(this.emit("newTab",{}),this.setupNewTabButton())})),this.tabEls.forEach((e=>this.setTabCloseEventListener(e))),this.tabEls.forEach((e=>this.setTabRenameConfirmEventListener(e))),document.addEventListener("click",(({target:e})=>{e instanceof Element&&!e.classList.contains("chrome-tab-rename")&&!e.classList.contains("chrome-tab-content")&&this.tabEls.forEach((e=>{const t=e.querySelector(".chrome-tab-rename").value;null!==e.getAttribute("is-renaming")&&""!==t.trim()&&t.trim()!==e.getAttribute("data-tab-title")&&(e.setAttribute("data-tab-title",t),this.emit("tabRename",{tabEl:e,title:t})),this.hideRenameTab(e)}))}))}get tabEls(){return Array.prototype.slice.call(this.el.querySelectorAll(".chrome-tab"))}get tabContentEl(){return this.el.querySelector(".chrome-tabs-content")}get tabContentWidths(){const e=this.tabEls.length,t=this.el.clientWidth-90,n=1*(e-1),r=(t-20+n)/e,a=Math.max(24,Math.min(240,r)),o=Math.floor(a),i=[];let l=t-(o*e+20-n);for(let t=0;t<e;t+=1){const e=o<240&&l>0?1:0;i.push(o+e),l>0&&(l-=1)}return i}get tabContentPositions(){const e=[],t=this.tabContentWidths;let n=10;return t.forEach(((t,r)=>{const a=1*r;e.push(n-a),n+=t})),e}get tabPositions(){const e=[];return this.tabContentPositions.forEach((t=>{e.push(t-10)})),e}layoutTabs(){const e=this.tabContentWidths;this.tabEls.forEach(((t,n)=>{const r=e[n],a=r+20;t.style.width=a+"px",t.removeAttribute("is-small"),t.removeAttribute("is-smaller"),t.removeAttribute("is-mini"),r<84&&t.setAttribute("is-small",""),r<60&&t.setAttribute("is-smaller",""),r<48&&t.setAttribute("is-mini",""),t.querySelector(".chrome-tab-close").style.display=this.tabEls.length>1?"block":"none"}));let t="";this.tabPositions.forEach(((e,n)=>{t+=`\n            .chrome-tabs[data-chrome-tabs-instance-id="${this.instanceId}"] .chrome-tab:nth-child(${n+1}) {\n              transform: translate3d(${e}px, 0, 0)\n            }\n          `})),this.styleEl.innerHTML=t;const n=this.tabEls.length;this.el.offsetWidth-this.tabContentEl.offsetWidth>95||n<5?(this.tabContentEl.style.width=(this.tabEls[0]?this.tabEls[0].offsetWidth*n:0)-(n>0?10*n*2-24+10:0)+"px",this.tabContentEl.nextElementSibling.classList.remove("overflow-shadow")):this.tabContentEl.nextElementSibling.classList.add("overflow-shadow")}createNewTabEl(){const e=document.createElement("div");return e.innerHTML='\n      <div class="chrome-tab">\n        <div class="chrome-tab-dividers"></div>\n        <div class="chrome-tab-background">\n          <svg xmlns="http://www.w3.org/2000/svg"><defs><symbol id="chrome-tab-geometry-left" viewBox="0 0 214 36"><path d="M17 0h197v36H0v-2c4.5 0 9-3.5 9-8V8c0-4.5 3.5-8 8-8z"/></symbol><symbol id="chrome-tab-geometry-right" viewBox="0 0 214 36"><use xlink:href="#chrome-tab-geometry-left"/></symbol><clipPath id="crop"><rect class="mask" width="100%" height="100%" x="0"/></clipPath></defs><svg width="52%" height="100%"><use xlink:href="#chrome-tab-geometry-left" width="214" height="36" class="chrome-tab-geometry"/></svg><g transform="scale(-1, 1)"><svg width="52%" height="100%" x="-100%" y="0"><use xlink:href="#chrome-tab-geometry-right" width="214" height="36" class="chrome-tab-geometry"/></svg></g></svg>\n        </div>\n        <div class="chrome-tab-content">\n          <div class="chrome-tab-favicon"></div>\n          <div class="chrome-tab-title"></div>\n          <input class="chrome-tab-rename" type="text" />\n          <div class="chrome-tab-drag-handle"></div>\n          <div class="chrome-tab-close"></div>\n        </div>\n      </div>\n    ',e.firstElementChild}addTab(e,{animate:t=!0,background:n=!1}={}){const r=this.createNewTabEl();return r.oncontextmenu=e=>{this.emit("contextmenu",{tabEl:r,event:e})},t&&(r.classList.add("chrome-tab-was-just-added"),setTimeout((()=>r.classList.remove("chrome-tab-was-just-added")),500)),e=Object.assign({},To,e),this.tabContentEl.appendChild(r),this.setTabCloseEventListener(r),this.setTabRenameConfirmEventListener(r),this.updateTab(r,e),this.emit("tabAdd",{tabEl:r}),n||this.setCurrentTab(r),this.cleanUpPreviouslyDraggedTabs(),this.layoutTabs(),this.setupDraggabilly(),r}setTabCloseEventListener(e){const t=t=>{t.stopImmediatePropagation(),this.emit("tabClose",{tabEl:e}),this.setupNewTabButton()};e.querySelector(".chrome-tab-close").addEventListener("click",t),e.addEventListener("auxclick",t)}setTabRenameConfirmEventListener(e){const t=e.querySelector(".chrome-tab-rename");t.addEventListener("keydown",(n=>{"Enter"===n.key&&""!==t.value?(e.setAttribute("data-tab-title",t.value),this.emit("tabRename",{tabEl:e,title:t.value}),this.toggleRenameTab(e)):"Escape"===n.key&&this.toggleRenameTab(e)}))}get activeTabEl(){return this.el.querySelector(".chrome-tab[active]")}hasActiveTab(){return!!this.activeTabEl}setCurrentTab(e){const t=this.activeTabEl;t!==e&&(t&&t.removeAttribute("active"),e.setAttribute("active",""),this.emit("activeTabChange",{tabEl:e}))}removeTab(e){e===this.activeTabEl&&(e.nextElementSibling?this.setCurrentTab(e.nextElementSibling):e.previousElementSibling&&this.setCurrentTab(e.previousElementSibling)),e.parentNode.removeChild(e),this.emit("tabRemove",{tabEl:e}),this.cleanUpPreviouslyDraggedTabs(),this.layoutTabs(),this.setupDraggabilly()}updateTab(e,t){e.setAttribute("data-tab-title",t.title),e.querySelector(".chrome-tab-title").textContent=t.title;const n=e.querySelector(".chrome-tab-rename");n.setAttribute("value",t.title),n.setAttribute("placeholder",t.title);const r=e.querySelector(".chrome-tab-favicon"),{favicon:a,faviconClass:o,className:i}=t;i&&(e.className=[e.className,i].join(" ")),r.className="chrome-tab-favicon",r.style.backgroundImage="",a||o?(o&&(r.className=["chrome-tab-favicon",o].join(" ")),a&&(r.style.backgroundImage=`url('${a}')`),r?.removeAttribute("hidden")):(r?.setAttribute("hidden",""),r?.removeAttribute("style")),t.id&&e.setAttribute("data-tab-id",t.id)}showRenameTab(e){e.setAttribute("is-renaming",""),e.setAttribute("data-tab-title",e.textContent?.trim()||"");const t=e.querySelector(".chrome-tab-title"),n=e.querySelector(".chrome-tab-rename"),r=e.querySelector(".chrome-tab-close");t.style.display="none",n.style.display="block",r.style.display="none",n.focus(),n.select()}hideRenameTab(e){e.removeAttribute("is-renaming");const t=e.querySelector(".chrome-tab-title"),n=e.querySelector(".chrome-tab-rename");e.getAttribute("data-tab-title")&&(n.value=e.getAttribute("data-tab-title")||"",t.textContent=e.getAttribute("data-tab-title")||"");const r=e.querySelector(".chrome-tab-close");t.style.display="block",n.style.display="none",r.style.display=this.tabEls.length>1?"block":"none"}toggleRenameTab(e){"none"===e.querySelector(".chrome-tab-title").style.display?this.hideRenameTab(e):this.showRenameTab(e)}cleanUpPreviouslyDraggedTabs(){this.tabEls.forEach((e=>e.classList.remove("chrome-tab-was-just-dragged")))}setupDraggabilly(){const e=this.tabEls,t=this.tabPositions;this.isDragging&&(this.isDragging=!1,this.el.classList.remove("chrome-tabs-is-sorting"),this.draggabillyDragging.element.classList.remove("chrome-tab-is-dragging"),this.draggabillyDragging.element.style.transform="",this.draggabillyDragging.dragEnd(),this.draggabillyDragging.isDragging=!1,this.draggabillyDragging.positionDrag=ko,this.draggabillyDragging.destroy(),this.draggabillyDragging=null),this.draggabillies.forEach((e=>e.destroy())),e.forEach(((n,r)=>{const a=t[r],o=new(So())(n,{axis:"x",handle:".chrome-tab-drag-handle",containment:this.tabContentEl});let i,l,s,c=0;this.draggabillies.push(o),o.on("pointerDown",((t,r)=>{const a=r.timeStamp;t.target===n.querySelector(".chrome-tab-drag-handle")&&(i===r.clientX&&l===r.clientY&&a-c<500&&s&&(e.forEach((e=>this.hideRenameTab(e))),this.showRenameTab(n),t.stopImmediatePropagation(),s=!1),s=n.hasAttribute("active"),i=r.clientX,l=r.clientY,c=a),this.emit("tabClick",{tabEl:n})})),o.on("dragStart",(e=>{this.isDragging=!0,this.draggabillyDragging=o,n.classList.add("chrome-tab-is-dragging"),this.el.classList.add("chrome-tabs-is-sorting"),this.emit("dragStart",{})})),o.on("dragEnd",(e=>{this.isDragging=!1;const t=parseFloat(n.style.left);n.style.transform="translate3d(0, 0, 0)",this.emit("dragEnd",{}),requestAnimationFrame((e=>{n.style.left="0",n.style.transform=`translate3d(${t}px, 0, 0)`,requestAnimationFrame((e=>{n.classList.remove("chrome-tab-is-dragging"),this.el.classList.remove("chrome-tabs-is-sorting"),n.classList.add("chrome-tab-was-just-dragged"),requestAnimationFrame((e=>{n.style.transform="",this.layoutTabs(),this.setupDraggabilly()}))}))}))})),o.on("dragMove",((e,r,o)=>{const i=this.tabEls,l=i.indexOf(n),s=((e,t)=>{let n=1/0,r=-1;return t.forEach(((t,a)=>{Math.abs(e-t)<n&&(n=Math.abs(e-t),r=a)})),r})(a+o.x,t),c=Math.max(0,Math.min(i.length,s));l!==c&&this.animateTabMove(n,l,c)}))}))}animateTabMove(e,t,n){n<t?e.parentNode.insertBefore(e,this.tabEls[n]):e.parentNode.insertBefore(e,this.tabEls[n+1]),this.emit("tabReorder",{tabEl:e,originIndex:t,destinationIndex:n}),this.layoutTabs()}setupNewTabButton(){const e=this.tabContentEl.parentNode?.querySelector(".new-tab-button-wrapper"),t=this.limit&&this.tabEls.length>=this.limit;e&&t?(e.parentNode?.removeChild(e),this.layoutTabs()):e||(this.tabContentEl.insertAdjacentHTML("afterend",'\n    <div class="new-tab-button-wrapper">\n      <button class="new-tab-button">✚</button>\n    </div>\n  '),this.layoutTabs())}};function Ro(){return Ro=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ro.apply(this,arguments)}const Io=(0,r.forwardRef)(((e,t)=>{const n=["chrome-tabs"];return e.darkMode&&n.push("chrome-tabs-dark-theme"),e.className&&n.push(e.className),r.createElement("div",{ref:t,className:n.join(" "),style:{"--tab-content-margin":"9px",paddingBottom:"3px"}},r.createElement("div",{className:"chrome-tabs-content"}))}));var Ao=n(23701),Lo=n.n(Ao);function Po({tabs:e,limit:t,className:n,darkMode:a,onTabActive:o,onTabClose:i,onTabRename:l,onTabReorder:s,onContextMenu:c,onNewTab:d}){const u=(0,r.useRef)([]),m=(0,r.useRef)({tabId:"",fromIndex:-1,toIndex:-1}),p=(0,r.useCallback)(((e,t,n)=>{const[r]=u.current.splice(t,1);u.current.splice(n,0,r);const a=m.current.fromIndex;m.current={tabId:e,fromIndex:a>-1?a:t,toIndex:n}}),[]),g=(0,r.useCallback)((()=>{const{tabId:e,fromIndex:t,toIndex:n}=m.current;t>-1&&s?.(e,t,n),m.current={tabId:"",fromIndex:-1,toIndex:-1}}),[s]),{ChromeTabs:f,addTab:h,activeTab:b,removeTab:E,updateTab:y}=function(e,t){const n=(0,r.useRef)(null),a=(0,r.useRef)(null);(0,r.useEffect)((()=>{const e=new Oo;a.current=e,e.init(n.current,t)}),[]),(0,r.useEffect)((()=>{const t=({detail:t})=>{const n=t.tabEl.getAttribute("data-tab-id");e.onTabActive?.(n)},n=({detail:t})=>{const{tabEl:n,title:r}=t,a=n.getAttribute("data-tab-id");e.onTabRename?.(a,r)},r=a.current?.el;return r?.addEventListener("tabClick",t),r?.addEventListener("tabRename",n),()=>{r?.removeEventListener("tabClick",t),r?.removeEventListener("tabRename",n)}}),[e.onTabActive]),(0,r.useEffect)((()=>{const t=a.current?.el,n=({detail:t})=>{const{tabEl:n,originIndex:r,destinationIndex:a}=t,o=n.getAttribute("data-tab-id");e.onTabReorder?.(o,r,a)};return t?.addEventListener("tabReorder",n),()=>{t?.removeEventListener("tabReorder",n)}}),[e.onTabReorder]),(0,r.useEffect)((()=>{const t=a.current?.el,n=({detail:t})=>{const n=t.tabEl.getAttribute("data-tab-id");e.onTabClose?.(n)};return t?.addEventListener("tabClose",n),()=>{t?.removeEventListener("tabClose",n)}}),[e.onTabClose]),(0,r.useEffect)((()=>{const t=()=>{e.onDragBegin?.()},n=a.current?.el;return n?.addEventListener("dragBegin",t),()=>{n?.removeEventListener("dragBegin",t)}}),[e.onDragBegin]),(0,r.useEffect)((()=>{const t=a.current?.el,n=({detail:t})=>{const n=t.tabEl;if(!n)return;const r=n.getAttribute("data-tab-id");e.onContextMenu?.(r,t.event)};return t?.addEventListener("contextmenu",n),()=>{t?.removeEventListener("contextmenu",n)}}),[e.onContextMenu]),(0,r.useEffect)((()=>{const t=()=>{e.onDragEnd?.()},n=a.current?.el;return n?.addEventListener("dragEnd",t),()=>{n?.removeEventListener("dragEnd",t)}}),[e.onDragEnd]),(0,r.useEffect)((()=>{const t=()=>{e.onNewTab?.()},n=a.current?.el;return n?.addEventListener("newTab",t),()=>{n?.removeEventListener("newTab",t)}}),[e.onNewTab]);const o=(0,r.useCallback)((e=>{a.current?.addTab(e)}),[]),i=(0,r.useCallback)((e=>{const t=n.current?.querySelector(`[data-tab-id="${e}"]`);t&&a.current?.removeTab(t)}),[]),l=(0,r.useCallback)((e=>{const t=n.current?.querySelector(`[data-tab-id="${e}"]`);t!==a.current?.activeTabEl&&a.current?.setCurrentTab(t)}),[]),s=(0,r.useCallback)(((e,t)=>{const r=n.current?.querySelector(`[data-tab-id="${e}"]`);r?a.current?.updateTab(r,{...t}):a.current?.addTab(t)}),[]);return{ChromeTabs:(0,r.useCallback)((function(e){return r.createElement(Io,Ro({},e,{ref:n}))}),[]),addTab:o,updateTab:s,removeTab:i,activeTab:l}}({onTabClose:i,onTabActive:o,onTabRename:l,onContextMenu:c,onNewTab:d,onDragEnd:g,onTabReorder:p},t);return(0,r.useEffect)((()=>{Lo()(u.current,e)||(u.current.slice(e.length).forEach((e=>{E(e.id)})),e.forEach(((e,t)=>{const n=u.current[t];n?Lo()(e,n)||y(n.id,e):h(e)})),e.forEach((e=>{e.active&&b(e.id)}))),u.current=e}),[e]),r.createElement(f,{className:n,darkMode:a})}var Do=n(93301),Mo=n(64611),Bo=n(66622),Fo=n(30711),Ho=n(49333),Vo=n(14413);function zo(){return zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zo.apply(this,arguments)}const Zo=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"space-between"}).withConfig({displayName:"tabs__Root"})(["\n  width: 100%;\n  display: flex;\n  background: ",";\n  padding-right: 1rem;\n"],(({theme:e})=>e.color.backgroundLighter)),Uo=(0,s.ZP)(Dn.z).withConfig({displayName:"tabs__HistoryButton"})(["\n  &.active {\n    background: ",";\n  }\n"],(({theme:e})=>e.color.comment)),qo=(0,s.ZP)(Ho.h.Content).withConfig({displayName:"tabs__DropdownMenuContent"})(["\n  margin-top: 0.5rem;\n  z-index: 100;\n  background: ",";\n"],(({theme:e})=>e.color.backgroundDarker)),Go=e=>e.metricsViewState?"assets/icon-chart.svg":"assets/icon-file.svg",$o=()=>{const{activeBuffer:e,buffers:t,setActiveBuffer:n,addBuffer:a,updateBuffer:o,deleteBuffer:i,archiveBuffer:l}=ma(),[s,c]=(0,r.useState)(!1),d=(0,r.useMemo)(V,[]),[u,m]=(0,r.useState)(!1),p=t.filter((e=>e.archived&&e.archivedAt&&-1===e.position)).sort(((e,t)=>e.archivedAt&&t.archivedAt?t.archivedAt-e.archivedAt:0));return(0,r.useLayoutEffect)((()=>{c(!0)}),[]),s?r.createElement(Zo,null,r.createElement(Po,{limit:40,darkMode:!0,onTabClose:async e=>{const n=t.find((t=>t.id===parseInt(e)));n&&1!==t.filter((e=>!e.archived)).length&&(""!==n?.value||n.metricsViewState?.metrics&&n.metricsViewState.metrics.length>0?await l(parseInt(e)):await i(parseInt(e)),await(async e=>{const n=t.filter((t=>!t.archived&&t.id!==parseInt(e))).sort(((e,t)=>e.position-t.position));for(const e of n){const t=n.indexOf(e);e.id&&await o(e.id,{position:t})}})(e),p.length>=10&&await Promise.all(p.slice(9).map((e=>i(e.id)))))},onTabReorder:async(e,n,r)=>{const a=t.find((t=>t.id===parseInt(e)));if(!a)return;let i=t.filter((t=>t.id!==parseInt(e)&&!t.archived)).sort(((e,t)=>e.position-t.position));i.splice(r,0,a),i.forEach((async(e,t)=>{await o(e.id,{position:t})}))},onTabActive:async e=>{const r=t.find((t=>t.id===parseInt(e)));r&&await n(r)},onTabRename:async(e,t)=>{await o(parseInt(e),{label:t})},onNewTab:a,tabs:t.filter((e=>!e.archived)).sort(((e,t)=>e.position-t.position)).map((t=>({id:t.id?.toString(),favicon:Go(t),title:t.label,active:e.id===t.id,className:t.metricsViewState?"metrics-tab":""})))}),r.createElement(Ho.h.Root,{modal:!1,onOpenChange:m},r.createElement(Ho.h.Trigger,{asChild:!0},r.createElement(Ce.A,null,r.createElement(Uo,zo({skin:"transparent","data-hook":"editor-tabs-history-button",prefixIcon:r.createElement(Do.A,{size:"20px"})},u?{className:"active"}:{}),"History"))),r.createElement(Ho.h.Portal,null,r.createElement(qo,{"data-hook":"editor-tabs-history"},0===p.length&&r.createElement("div",{style:{padding:"0 1rem"}},r.createElement(Pe,{color:"gray2"},"History is empty")),p.map((e=>r.createElement(Ho.h.Item,{"data-hook":"editor-tabs-history-item",key:e.id,onClick:async()=>{await o(e.id,{archived:!1,archivedAt:void 0,position:t.length-1}),await n(e)}},r.createElement(Pn.x,{align:"flex-start",justifyContent:"flex-start",gap:"0.5rem",title:e.label},e.metricsViewState?r.createElement(Mo.w,{size:"18px"}):r.createElement(Bo.$,{size:"18px"}),r.createElement(Pn.x,zo({flexDirection:"column",align:"flex-start",gap:"0"},e.archivedAt?{title:(0,g.WU)(new Date(e.archivedAt),"P pppp",{locale:Q(d)})}:{}),r.createElement(Pe,{color:"foreground",ellipsis:!0},e.label.substring(0,30),e.label.length>30?"...":""),e.archivedAt&&r.createElement(Pe,{color:"gray2"},(0,Vo.B)(e.archivedAt,(new Date).getTime(),{locale:Q(d)})," ago")))))),p.length>0&&r.createElement(r.Fragment,null,r.createElement(Ho.h.Divider,null),r.createElement(Ho.h.Item,{onClick:async()=>{for(const e of p)await i(e.id)},"data-hook":"editor-tabs-history-clear"},r.createElement(Fo.r,{size:"18px"}),r.createElement(Pe,{color:"foreground"},"Clear history"))))))):null};var jo=n(45869),Wo=n(39565);const Qo="yyyy-MM-dd HH:mm:ss";let Yo=function(e){return e.WAL_TRANSACTION_THROUGHPUT="TABLE_WAL_TRANSACTION_THROUGHPUT",e.WAL_ROW_THROUGHPUT="TABLE_WAL_ROW_THROUGHPUT",e.WAL_TRANSACTION_LATENCY="TABLE_WAL_TRANSACTION_LATENCY",e.TABLE_WRITE_AMPLIFICATION="TABLE_WRITE_AMPLIFICATION",e.TABLE_AVERAGE_TRANSACTION_SIZE="TABLE_AVERAGE_TRANSACTION_SIZE",e}({});const Ko=[{dateFrom:"now-5m",dateTo:"now",label:"Last 5m"},{dateFrom:"now-15m",dateTo:"now",label:"Last 15m"},{dateFrom:"now-1h",dateTo:"now",label:"Last 1h"},{dateFrom:"now-3h",dateTo:"now",label:"Last 3h"},{dateFrom:"now-6h",dateTo:"now",label:"Last 6h"},{dateFrom:"now-12h",dateTo:"now",label:"Last 12h"},{dateFrom:"now-24h",dateTo:"now",label:"Last 24h"},{dateFrom:"now-3d",dateTo:"now",label:"Last 3 days"},{dateFrom:"now-7d",dateTo:"now",label:"Last 7 days"}];let Xo=function(e){return e.LIST="List",e.GRID="Grid",e}({}),Jo=function(e){return e.AUTO="Auto",e.OFF="Off",e.ONE_SECOND="1s",e.FIVE_SECONDS="5s",e.TEN_SECONDS="10s",e.THIRTY_SECONDS="30s",e.ONE_MINUTE="1m",e}({});const ei={[Jo.AUTO]:0,[Jo.OFF]:0,[Jo.ONE_SECOND]:1,[Jo.FIVE_SECONDS]:5,[Jo.TEN_SECONDS]:10,[Jo.THIRTY_SECONDS]:30,[Jo.ONE_MINUTE]:60},ti=(e,t=2)=>{const n=parseFloat(e);return Number(n.toFixed(t))},ni=e=>e>=1e6?(e/1e6).toFixed(1).replace(/\.0$/,"")+" M":e>=1e3?(e/1e3).toFixed(1).replace(/\.0$/,"")+" k":e.toString(),ri=e=>e instanceof Date?(0,jo.c)(e):e,ai=[1,2,3,4,5,10,15,20,25,30,45,75,90],oi=[1,2,3,4,5,10,15,20,25,30,45,75,90],ii=[1,2,3,4,6,8,12,18,24],li=(e,t,n=600)=>{const r=(new Date(t).getTime()-new Date(e).getTime())/1e3;return[...ai,...oi.map((e=>60*e)),...ii.map((e=>3600*e))].sort(((e,t)=>Math.abs(e)-Math.abs(t))).find((e=>e>r/n))},si=e=>!(!e||0===e[1].length)&&e[1].length>0&&e[1].some((e=>null!==e)),ci=e=>/^now(-\d+[hdm]$)?$/.test(e),di=e=>{if(!ci(e))return t=e,isNaN(Date.parse(t))?"Invalid date":e;var t;const n=new Date;if("now"===e)return(0,jo.c)(n);const[r,a,o,i]=e.match(/now(-)?(\d+)([a-z]+)$/);let l=0;switch(i){case"m":l=parseInt(o);break;case"h":l=60*parseInt(o);break;case"d":l=60*parseInt(o)*24;break;default:return"Invalid date"}return(0,jo.c)((0,Wo._)(n,l))},ui=(e,t)=>{const n=Ko.find((n=>n.dateFrom===e&&n.dateTo===t));return n?n.label:`${e.startsWith("now")?e:(0,g.WU)(new Date(e),Qo)} - ${t.startsWith("now")?t:(0,g.WU)(new Date(t),Qo)}\n  `};var mi=n(76697),pi=n(24217),gi=n(41324),fi=n(35146),hi=n(84001),bi=n(22562),Ei=n.n(bi),yi=n(51871);const wi=["#FF6B6B","#4ECDC4","#FFD93D","#95D86E","#FF8F40","#BD93F9","#50FA7B","#FF79C6","#8BE9FD","#F1FA8C"],Ci=s.ZP.div.withConfig({displayName:"color-palette__Root"})(["\n  padding: 0.5rem;\n"]),vi=s.ZP.div.withConfig({displayName:"color-palette__ColorBox"})(["\n  position: relative;\n  width: 1.6rem;\n  height: 1.6rem;\n  cursor: pointer;\n"]),_i=(0,s.ZP)(yi.J).withConfig({displayName:"color-palette__CheckIcon"})(["\n  position: absolute;\n  color: black;\n"]),xi=({selectedColor:e,onSelect:t})=>r.createElement(Ci,null,r.createElement(Pn.x,{gap:"0.5rem"},wi.map((n=>r.createElement(vi,{key:n,style:{backgroundColor:n},onClick:()=>t(n)},e===n&&r.createElement(_i,{size:"16px"})))))),Si={distribution:1,label:"WAL Transaction Latency",chartTitle:"WAL Transaction Latency (90th percentile)",getDescription:()=>r.createElement(r.Fragment,null,"This chart tracks the time required for data to become readable after being written. Higher latency may stem from:",r.createElement("ul",null,r.createElement("li",null,"Large transaction sizes (refer to Avg Transaction Size chart if elevated)"),r.createElement("li",null,"Unordered data requiring additional processing"),r.createElement("li",null,"Write amplification (see dedicated chart if batch size is optimal)"),r.createElement("li",null,"Storage I/O limitations or contention")),"Monitor this metric alongside related charts to identify the root cause of performance variations and optimize accordingly."),icon:'<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M28 37H37.18C37.3471 37.4797 37.6333 37.909 38.0117 38.2479C38.3901 38.5867 38.8483 38.824 39.3434 38.9373C39.8386 39.0507 40.3544 39.0365 40.8425 38.896C41.3307 38.7556 41.7751 38.4935 42.1343 38.1343C42.4935 37.7751 42.7556 37.3307 42.896 36.8425C43.0365 36.3544 43.0507 35.8386 42.9373 35.3434C42.824 34.8483 42.5867 34.3901 42.2479 34.0117C41.909 33.6333 41.4797 33.3471 41 33.18V24H39V33.18C38.5806 33.3293 38.1997 33.5701 37.8849 33.8849C37.5701 34.1997 37.3293 34.5806 37.18 35H28V37ZM40 35C40.1978 35 40.3911 35.0586 40.5556 35.1685C40.72 35.2784 40.8482 35.4346 40.9239 35.6173C40.9996 35.8 41.0194 36.0011 40.9808 36.1951C40.9422 36.3891 40.847 36.5673 40.7071 36.7071C40.5673 36.847 40.3891 36.9422 40.1951 36.9808C40.0011 37.0194 39.8 36.9996 39.6173 36.9239C39.4346 36.8482 39.2784 36.72 39.1685 36.5556C39.0586 36.3911 39 36.1978 39 36C39 35.7348 39.1054 35.4804 39.2929 35.2929C39.4804 35.1054 39.7348 35 40 35Z" fill="url(#paint0_linear_56_695)"/>\n    <path d="M55.06 20L56.4 18.2L57.2 18.8C57.3731 18.9298 57.5836 19 57.8 19H57.94C58.0703 18.9816 58.1956 18.9377 58.3089 18.8708C58.4221 18.8038 58.521 18.7152 58.6 18.61L61 15.4C61.1591 15.1878 61.2275 14.9211 61.1899 14.6586C61.1524 14.396 61.0122 14.1591 60.8 14L54.4 9.2C54.1878 9.04087 53.9211 8.97254 53.6586 9.01005C53.396 9.04756 53.1591 9.18783 53 9.4L50.6 12.6C50.4409 12.8122 50.3725 13.0789 50.41 13.3414C50.4476 13.604 50.5878 13.8409 50.8 14L51.6 14.6L50.17 16.5C48.2326 15.4828 46.1523 14.7647 44 14.37V12H46C46.2652 12 46.5196 11.8946 46.7071 11.7071C46.8946 11.5196 47 11.2652 47 11V7C47 6.73478 46.8946 6.48043 46.7071 6.29289C46.5196 6.10536 46.2652 6 46 6H34C33.7348 6 33.4804 6.10536 33.2929 6.29289C33.1054 6.48043 33 6.73478 33 7V11C33 11.2652 33.1054 11.5196 33.2929 11.7071C33.4804 11.8946 33.7348 12 34 12H36V14.37C33.9104 14.7609 31.889 15.4548 30 16.43L28.6 14.6L29.4 14C29.5052 13.921 29.5938 13.8221 29.6608 13.7089C29.7277 13.5956 29.7716 13.4703 29.79 13.34C29.8271 13.0779 29.7588 12.8118 29.6 12.6L27.2 9.4C27.0409 9.18783 26.804 9.04756 26.5414 9.01005C26.2789 8.97254 26.0122 9.04087 25.8 9.2L19.4 14C19.2949 14.0788 19.2064 14.1775 19.1395 14.2905C19.0726 14.4035 19.0286 14.5286 19.01 14.6586C18.9915 14.7886 18.9987 14.921 19.0313 15.0482C19.0639 15.1754 19.1212 15.2949 19.2 15.4L21.6 18.6C21.679 18.7052 21.7779 18.7938 21.8911 18.8608C22.0044 18.9277 22.1297 18.9716 22.26 18.99H22.4C22.6164 18.99 22.8269 18.9198 23 18.79L23.8 18.19L25.06 19.86C24.0312 20.8146 23.0969 21.8661 22.27 23H10V25H21C20.264 26.2692 19.6576 27.6093 19.19 29H6V31H18.58C18.2638 32.3126 18.0696 33.6516 18 35H2V37H18C18.0598 38.3474 18.2439 39.6864 18.55 41H6V43H19.15C19.6304 44.3922 20.2502 45.7324 21 47H10V49H22.27C24.0647 51.4991 26.3637 53.594 29.0184 55.1494C31.6732 56.7047 34.6246 57.6859 37.6822 58.0296C40.7397 58.3732 43.8354 58.0718 46.7691 57.1446C49.7029 56.2174 52.4096 54.6852 54.7143 52.6468C57.0191 50.6085 58.8706 48.1093 60.1494 45.3109C61.4282 42.5124 62.1057 39.4768 62.1383 36.4002C62.1709 33.3236 61.5578 30.2743 60.3385 27.4494C59.1193 24.6245 57.3211 22.0867 55.06 20ZM54 11.4L58.8 15L57.6 16.6L52.8 13L54 11.4ZM53.2 15.8L54.8 17L53.54 18.68C53.01 18.27 52.47 17.88 51.91 17.52L53.2 15.8ZM35 8H45V10H35V8ZM38 12H42V14.09C41.34 14 40.67 14 40 14C39.33 14 38.66 14 38 14.09V12ZM22.6 16.6L21.4 15L26.2 11.4L27.4 13L22.6 16.6ZM25.4 17L27 15.8L28.22 17.43C27.66 17.79 27.11 18.17 26.59 18.58L25.4 17ZM23.31 25H27.06C26.0356 26.2098 25.1842 27.556 24.53 29H21.29C21.8114 27.5952 22.4889 26.2535 23.31 25ZM32.55 23C34.8138 21.6887 37.3838 20.9988 40 21C43.9782 21 47.7936 22.5804 50.6066 25.3934C53.4196 28.2064 55 32.0218 55 36C55 39.9782 53.4196 43.7936 50.6066 46.6066C47.7936 49.4196 43.9782 51 40 51C37.3838 51.0012 34.8138 50.3113 32.55 49H34V47H29.84C28.5864 45.8529 27.538 44.5001 26.74 43H32V41H25.88C25.4201 39.7113 25.1408 38.3652 25.05 37H26V35H25.05C25.1408 33.6348 25.4201 32.2887 25.88 31H32V29H26.74C27.538 27.4999 28.5864 26.1471 29.84 25H34V23H32.55ZM20.66 31H23.75C23.3577 32.3006 23.1227 33.6435 23.05 35H20.05C20.1149 33.6492 20.3193 32.3087 20.66 31ZM20.05 37H23.05C23.1227 38.3565 23.3577 39.6994 23.75 41H20.66C20.3193 39.6913 20.1149 38.3508 20.05 37ZM21.29 43H24.53C25.1842 44.444 26.0356 45.7902 27.06 47H23.31C22.4889 45.7465 21.8114 44.4048 21.29 43ZM40 56C37.1162 55.9959 34.2675 55.3682 31.6491 54.1599C29.0306 52.9517 26.7044 51.1915 24.83 49H29.07C31.5469 51.0872 34.5685 52.4233 37.7792 52.8511C40.9899 53.2789 44.2559 52.7805 47.1929 51.4147C50.1299 50.0489 52.6156 47.8724 54.3574 45.1415C56.0991 42.4107 57.0245 39.239 57.0245 36C57.0245 32.761 56.0991 29.5893 54.3574 26.8585C52.6156 24.1276 50.1299 21.9511 47.1929 20.5853C44.2559 19.2195 40.9899 18.7211 37.7792 19.1489C34.5685 19.5767 31.5469 20.9128 29.07 23H24.83C27.0115 20.4496 29.7996 18.4892 32.9377 17.2993C36.0758 16.1094 39.4628 15.7283 42.7868 16.1911C46.1108 16.6539 49.2649 17.9458 51.9586 19.9477C54.6522 21.9496 56.7988 24.5971 58.2006 27.6465C59.6023 30.6958 60.2142 34.0488 59.9798 37.3967C59.7455 40.7446 58.6724 43.9797 56.8594 46.804C55.0465 49.6282 52.552 51.9509 49.6057 53.5579C46.6594 55.165 43.3561 56.0048 40 56Z" fill="url(#paint1_linear_56_695)"/>\n    <defs>\n        <linearGradient id="paint0_linear_56_695" x1="35.5065" y1="24" x2="35.5065" y2="39.013" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint1_linear_56_695" x1="32.0698" y1="6" x2="32.0698" y2="58.1672" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n    </defs>\n</svg>\n',isTableMetric:!0,querySupportsRollingAppend:!0,getQuery:({tableId:e,sampleBySeconds:t,from:n,to:r})=>`\n    select created, approx_percentile(latency, 0.9, 3) latency\n      from ${E.WAL}\n      where \n          event = 105\n          and rowCount > 0\n          ${e?`and tableId = ${e}`:""}\n      sample by ${t}s\n      FROM timestamp_floor('${t}s', '${n}')\n      TO timestamp_floor('${t}s', '${r}')\n      fill(0)\n    `,alignData:e=>[e.map((e=>new Date(e.created).getTime())),e.map((e=>ti(e.latency)))],mapYValue:e=>e>=1e3?`${(e/1e3).toFixed(2)} s`:`${e} ms`},ki={distribution:1,label:"Table Write Amplification",chartTitle:"Write Amplification",getDescription:()=>r.createElement(r.Fragment,null,"This chart tracks the data write overhead during merge operations. Write amplification occurs when:",r.createElement("ul",null,r.createElement("li",null,"Copy-on-write operations affect large data blocks"),r.createElement("li",null,"Datasets are re-ingested for deduplication"),r.createElement("li",null,"Data requires extensive rewriting during merges")),"Scale ranges from optimal (1x) to problematic (1000x+). High amplification typically indicates duplicate data ingestion or suboptimal data ordering patterns."),icon:'<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M61 14H3C2.73478 14 2.48043 14.1054 2.29289 14.2929C2.10536 14.4804 2 14.7348 2 15V49C2 49.2652 2.10536 49.5196 2.29289 49.7071C2.48043 49.8946 2.73478 50 3 50H61C61.2652 50 61.5196 49.8946 61.7071 49.7071C61.8946 49.5196 62 49.2652 62 49V15C62 14.7348 61.8946 14.4804 61.7071 14.2929C61.5196 14.1054 61.2652 14 61 14ZM4 48V42H8V40H4V39H8V37H4V36H8V34H4V33H8V31H4V30H8V28H4V27H8V25H4V24H8V22H4V16H60V18H53C52.7348 18 52.4804 18.1054 52.2929 18.2929C52.1054 18.4804 52 18.7348 52 19V45C52 45.2652 52.1054 45.5196 52.2929 45.7071C52.4804 45.8946 52.7348 46 53 46H60V48H4ZM60 23V24H56V26H60V27H56V29H60V30H56V32H60V33H56V35H60V38H56V40H60V41H56V43H60V44H54V20H60V21H56V23H60Z" fill="url(#paint0_linear_56_720)"/>\n    <path d="M8 18H6V20H8V18Z" fill="url(#paint1_linear_56_720)"/>\n    <path d="M8 44H6V46H8V44Z" fill="url(#paint2_linear_56_720)"/>\n    <path d="M50 18H48V20H50V18Z" fill="url(#paint3_linear_56_720)"/>\n    <path d="M50 44H48V46H50V44Z" fill="url(#paint4_linear_56_720)"/>\n    <path d="M45 18H11C10.7348 18 10.4804 18.1054 10.2929 18.2929C10.1054 18.4804 10 18.7348 10 19V28C10 28.2652 10.1054 28.5196 10.2929 28.7071C10.4804 28.8946 10.7348 29 11 29H13C13.7956 29 14.5587 29.3161 15.1213 29.8787C15.6839 30.4413 16 31.2044 16 32C16 32.7956 15.6839 33.5587 15.1213 34.1213C14.5587 34.6839 13.7956 35 13 35H11C10.7348 35 10.4804 35.1054 10.2929 35.2929C10.1054 35.4804 10 35.7348 10 36V45C10 45.2652 10.1054 45.5196 10.2929 45.7071C10.4804 45.8946 10.7348 46 11 46H45C45.2652 46 45.5196 45.8946 45.7071 45.7071C45.8946 45.5196 46 45.2652 46 45V35H44V44H12V37H13C14.3261 37 15.5979 36.4732 16.5355 35.5355C17.4732 34.5979 18 33.3261 18 32C18 30.6739 17.4732 29.4021 16.5355 28.4645C15.5979 27.5268 14.3261 27 13 27H12V20H44V29H46V19C46 18.7348 45.8946 18.4804 45.7071 18.2929C45.5196 18.1054 45.2652 18 45 18Z" fill="url(#paint5_linear_56_720)"/>\n    <path d="M46 31H44V33H46V31Z" fill="url(#paint6_linear_56_720)"/>\n    <defs>\n        <linearGradient id="paint0_linear_56_720" x1="32" y1="14" x2="32" y2="50" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint1_linear_56_720" x1="7" y1="18" x2="7" y2="20" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint2_linear_56_720" x1="7" y1="44" x2="7" y2="46" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint3_linear_56_720" x1="49" y1="18" x2="49" y2="20" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint4_linear_56_720" x1="49" y1="44" x2="49" y2="46" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint5_linear_56_720" x1="28" y1="18" x2="28" y2="46" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint6_linear_56_720" x1="45" y1="31" x2="45" y2="33" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n    </defs>\n</svg>\n',isTableMetric:!0,querySupportsRollingAppend:!0,getQuery:({tableId:e,sampleBySeconds:t,from:n,to:r})=>`\n      select\n        created,\n        COALESCE(phy_row_count / row_count,0) writeAmplification\n      from\n        (\n            select\n              created,\n              sum(rowcount) row_count,\n              sum(physicalRowCount) phy_row_count,\n            from ${E.WAL}\n            where \n              ${e?`tableId = ${e} `:""}\n              and event = 105\n              and rowCount > 0 \n            sample by ${t}s FROM timestamp_floor('${t}s', '${n}') TO timestamp_floor('${t}s', '${r}') fill(0,0)\n        );\n    `,alignData:e=>[e.map((e=>new Date(e.created).getTime())),e.map((e=>e.writeAmplification?ti(e.writeAmplification):1))],mapYValue:e=>e},Ti={distribution:1,label:"WAL Row Throughput",chartTitle:"Row Processing Throughput (rows/s)",getDescription:()=>r.createElement(r.Fragment,null,"This chart displays rows processed per second during transaction merges. While similar to transaction throughput, this metric helps identify:",r.createElement("ul",null,r.createElement("li",null,"Data density variations within transactions"),r.createElement("li",null,"Processing overhead for row-heavy transactions"),r.createElement("li",null,"Resource utilization from row-level operations"),r.createElement("li",null,"Impact of row complexity on merge performance")),"Use alongside transaction throughput to understand the relationship between transaction size and processing efficiency."," "),icon:'<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M61 43H52C52 42.45 51.55 42 51 42H39.17C38.46 39.39 37.38 38 36 38H6C3.25 38 2 43.7 2 49C2 54.3 3.25 60 6 60H36C37.37 60 38.46 58.61 39.17 56H54C54.55 56 55 55.55 55 55V52H57C57.55 52 58 51.55 58 51V49H61C61.55 49 62 48.55 62 48V44C62 43.45 61.55 43 61 43ZM35.98 40C36.11 40.04 36.6 40.51 37.08 42H34.9C35.31 40.72 35.75 40.07 35.97 40H35.98ZM6.01 58C5.65 57.9 4.89 56.56 4.42 54H10V52H4.14C4.05 51.1 4 50.1 4 49C4 47.9 4.05 46.9 4.14 46H18V44H4.42C4.89 41.44 5.65 40.1 6 40H33.57C32.7 41.75 32.22 44.39 32.06 46.94C32.02 47.6 32 48.29 32 49C32 49.71 32.02 50.4 32.06 51.06C32.22 53.61 32.69 56.25 33.57 58H6.01ZM36.01 58C35.77 57.93 35.32 57.28 34.91 56H37.08C36.6 57.5 36.12 57.97 36.01 58ZM52.99 54H34.41C34.3 53.41 34.21 52.74 34.14 52H53V54H52.99ZM55.99 50H34.02C34.01 49.67 33.99 49.35 33.99 49C33.99 48.65 34.01 48.33 34.02 48H35.99V46H34.13C34.2 45.26 34.3 44.59 34.4 44H49.98V46H37.98V48H55.98V50H55.99ZM59.99 47H57.99C57.99 46.45 57.54 46 56.99 46H51.99V45H59.99V47Z" fill="url(#paint0_linear_56_708)"/>\n    <path d="M29 48H14V50H29V48Z" fill="url(#paint1_linear_56_708)"/>\n    <path d="M12 48H10V50H12V48Z" fill="url(#paint2_linear_56_708)"/>\n    <path d="M22 44H20V46H22V44Z" fill="url(#paint3_linear_56_708)"/>\n    <path d="M14 52H12V54H14V52Z" fill="url(#paint4_linear_56_708)"/>\n    <path d="M18 36C26.48 36 33.43 29.36 33.95 21H36V19H33.95C33.43 10.64 26.49 4 18 4C9.51 4 2 11.18 2 20C2 28.82 9.18 36 18 36ZM18 6C25.72 6 32 12.28 32 20C32 27.72 25.72 34 18 34C10.28 34 4 27.72 4 20C4 12.28 10.28 6 18 6Z" fill="url(#paint5_linear_56_708)"/>\n    <path d="M52 20C52 19.45 51.55 19 51 19H38V21H50V36H52V20Z" fill="url(#paint6_linear_56_708)"/>\n    <path d="M52 38H50V40H52V38Z" fill="url(#paint7_linear_56_708)"/>\n    <path d="M12 28C12.14 28 12.29 27.97 12.42 27.91C12.77 27.75 13 27.39 13 27V26H20C20.55 26 21 25.55 21 25V22H23V23C23 23.39 23.22 23.74 23.58 23.91C23.71 23.97 23.86 24 24 24C24.23 24 24.46 23.92 24.64 23.77L30.64 18.77C30.87 18.58 31 18.3 31 18C31 17.7 30.87 17.42 30.64 17.23L24.64 12.23C24.34 11.98 23.93 11.93 23.58 12.09C23.23 12.25 23 12.61 23 13V14H16C15.45 14 15 14.45 15 15V18H13V17C13 16.61 12.78 16.26 12.42 16.09C12.07 15.93 11.66 15.98 11.36 16.23L5.36 21.23C5.13 21.42 5 21.7 5 22C5 22.3 5.13 22.58 5.36 22.77L11.36 27.77C11.54 27.92 11.77 28 12 28ZM17 16H24C24.51 16 24.93 15.62 24.99 15.13L28.44 18L24.99 20.87C24.93 20.38 24.51 20 24 20H21V19C21 18.45 20.55 18 20 18H17V16ZM11.01 24.87L7.56 22L11.01 19.13C11.07 19.62 11.49 20 12 20H19V24H12C11.49 24 11.07 24.38 11.01 24.87Z" fill="url(#paint8_linear_56_708)"/>\n    <defs>\n        <linearGradient id="paint0_linear_56_708" x1="32" y1="38" x2="32" y2="60" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint1_linear_56_708" x1="21.5" y1="48" x2="21.5" y2="50" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint2_linear_56_708" x1="11" y1="48" x2="11" y2="50" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint3_linear_56_708" x1="21" y1="44" x2="21" y2="46" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint4_linear_56_708" x1="13" y1="52" x2="13" y2="54" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint5_linear_56_708" x1="19" y1="4" x2="19" y2="36" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint6_linear_56_708" x1="45" y1="19" x2="45" y2="36" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint7_linear_56_708" x1="51" y1="38" x2="51" y2="40" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint8_linear_56_708" x1="18" y1="11.998" x2="18" y2="28" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n    </defs>\n</svg>\n',isTableMetric:!0,querySupportsRollingAppend:!0,getQuery:({tableId:e,sampleBySeconds:t,from:n,to:r})=>`\n      select created time,\n          sum(rowCount) numOfRowsApplied\n      from ${E.WAL}\n      where ${e?`tableId = ${e} and `:""}\n        event = 105\n        sample by 1s\n      FROM timestamp_floor('${t}s', '${n}') TO timestamp_floor('${t}s', '${r}') fill(0)\n    `,alignData:e=>[e.map((e=>new Date(e.time).getTime())),e.map((e=>e.numOfRowsApplied?ti(e.numOfRowsApplied):0))],mapYValue:e=>ni(e)},Ni={distribution:1,label:"WAL Transaction Throughput",chartTitle:"Transaction Throughput (txn/s)",getDescription:()=>r.createElement(r.Fragment,null,"This chart monitors the rate at which transactions are applied to tables. Performance is influenced by:",r.createElement("ul",null,r.createElement("li",null,"Batch merging efficiency (multiple transactions processed together)"),r.createElement("li",null,"Data ingestion rate from source"),r.createElement("li",null,"Storage performance and contention"),r.createElement("li",null,"Concurrent writes across multiple tables sharing resources")),"Compare against data source metrics to distinguish between ingestion bottlenecks and system performance limitations."),icon:'<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <path d="M1.99997 30.0002C1.99804 23.5333 4.23467 17.2651 8.32997 12.2602L6.78997 11.0002C2.40316 16.3605 0.00634766 23.0737 0.00634766 30.0002C0.00634766 36.9268 2.40316 43.6399 6.78997 49.0002L8.32997 47.7402C4.23467 42.7353 1.99804 36.4671 1.99997 30.0002Z" fill="url(#paint0_linear_208_15)"/>\n    <path d="M30 58.0002C24.9262 57.9962 19.9489 56.6136 15.6 54.0002L14.6 55.7202C19.2639 58.5085 24.5961 59.9809 30.03 59.9809C35.4638 59.9809 40.7961 58.5085 45.46 55.7202L44.4 54.0002C40.051 56.6136 35.0737 57.9962 30 58.0002Z" fill="url(#paint1_linear_208_15)"/>\n    <path d="M51.67 12.2602C55.7677 17.2642 58.0068 23.5325 58.0068 30.0002C58.0068 36.468 55.7677 42.7362 51.67 47.7402L53.21 49.0002C57.5968 43.6399 59.9936 36.9268 59.9936 30.0002C59.9936 23.0737 57.5968 16.3605 53.21 11.0002L51.67 12.2602Z" fill="url(#paint2_linear_208_15)"/>\n    <path d="M15.6 6.00023C19.9502 3.39152 24.9275 2.01356 30 2.01356C35.0725 2.01356 40.0497 3.39152 44.4 6.00023L45.4 4.28023C40.7361 1.49194 35.4038 0.0195313 29.97 0.0195312C24.5361 0.0195313 19.2039 1.49194 14.54 4.28023L15.6 6.00023Z" fill="url(#paint3_linear_208_15)"/>\n    <path d="M7.99997 8.00023C7.99997 8.59357 8.17592 9.17359 8.50557 9.66694C8.83521 10.1603 9.30375 10.5448 9.85192 10.7719C10.4001 10.9989 11.0033 11.0583 11.5852 10.9426C12.1672 10.8268 12.7017 10.5411 13.1213 10.1216C13.5409 9.70199 13.8266 9.16744 13.9423 8.5855C14.0581 8.00356 13.9987 7.40036 13.7716 6.85218C13.5446 6.304 13.16 5.83547 12.6667 5.50582C12.1733 5.17618 11.5933 5.00023 11 5.00023C10.2043 5.00023 9.44126 5.3163 8.87865 5.87891C8.31604 6.44152 7.99997 7.20458 7.99997 8.00023ZM12 8.00023C12 8.19801 11.9413 8.39135 11.8314 8.5558C11.7216 8.72025 11.5654 8.84842 11.3827 8.92411C11.1999 8.9998 10.9989 9.0196 10.8049 8.98102C10.6109 8.94243 10.4327 8.84719 10.2929 8.70734C10.153 8.56748 10.0578 8.3893 10.0192 8.19532C9.9806 8.00134 10.0004 7.80027 10.0761 7.61755C10.1518 7.43482 10.28 7.27864 10.4444 7.16876C10.6089 7.05888 10.8022 7.00023 11 7.00023C11.2652 7.00023 11.5195 7.10559 11.7071 7.29312C11.8946 7.48066 12 7.73501 12 8.00023Z" fill="url(#paint4_linear_208_15)"/>\n    <path d="M52 8.00023C52 7.40689 51.824 6.82687 51.4944 6.33352C51.1647 5.84017 50.6962 5.45566 50.148 5.22859C49.5998 5.00153 48.9966 4.94212 48.4147 5.05788C47.8328 5.17363 47.2982 5.45935 46.8787 5.87891C46.4591 6.29847 46.1734 6.83302 46.0576 7.41496C45.9419 7.9969 46.0013 8.6001 46.2283 9.14828C46.4554 9.69646 46.8399 10.165 47.3333 10.4946C47.8266 10.8243 48.4066 11.0002 49 11.0002C49.7956 11.0002 50.5587 10.6842 51.1213 10.1216C51.6839 9.55894 52 8.79588 52 8.00023ZM48 8.00023C48 7.80245 48.0586 7.60911 48.1685 7.44466C48.2784 7.28021 48.4346 7.15204 48.6173 7.07635C48.8 7.00066 49.0011 6.98086 49.1951 7.01945C49.389 7.05803 49.5672 7.15327 49.7071 7.29312C49.8469 7.43298 49.9422 7.61116 49.9808 7.80514C50.0193 7.99912 49.9995 8.20019 49.9239 8.38291C49.8482 8.56564 49.72 8.72182 49.5555 8.8317C49.3911 8.94158 49.1978 9.00023 49 9.00023C48.7348 9.00023 48.4804 8.89487 48.2929 8.70734C48.1053 8.5198 48 8.26545 48 8.00023Z" fill="url(#paint5_linear_208_15)"/>\n    <path d="M11 49.0002C10.4066 49.0002 9.82661 49.1762 9.33326 49.5058C8.83992 49.8355 8.4554 50.304 8.22833 50.8522C8.00127 51.4004 7.94186 52.0036 8.05762 52.5855C8.17337 53.1674 8.4591 53.702 8.87865 54.1215C9.29821 54.5411 9.83276 54.8268 10.4147 54.9426C10.9966 55.0583 11.5998 54.9989 12.148 54.7719C12.6962 54.5448 13.1647 54.1603 13.4944 53.6669C13.824 53.1736 14 52.5936 14 52.0002C14 51.2046 13.6839 50.4415 13.1213 49.8789C12.5587 49.3163 11.7956 49.0002 11 49.0002ZM11 53.0002C10.8022 53.0002 10.6089 52.9416 10.4444 52.8317C10.28 52.7218 10.1518 52.5656 10.0761 52.3829C10.0004 52.2002 9.9806 51.9991 10.0192 51.8051C10.0578 51.6112 10.153 51.433 10.2929 51.2931C10.4327 51.1533 10.6109 51.058 10.8049 51.0194C10.9989 50.9809 11.1999 51.0007 11.3827 51.0764C11.5654 51.152 11.7216 51.2802 11.8314 51.4447C11.9413 51.6091 12 51.8024 12 52.0002C12 52.2654 11.8946 52.5198 11.7071 52.7073C11.5195 52.8949 11.2652 53.0002 11 53.0002Z" fill="url(#paint6_linear_208_15)"/>\n    <path d="M49 49.0002C48.4066 49.0002 47.8266 49.1762 47.3333 49.5058C46.8399 49.8355 46.4554 50.304 46.2283 50.8522C46.0013 51.4004 45.9419 52.0036 46.0576 52.5855C46.1734 53.1674 46.4591 53.702 46.8787 54.1215C47.2982 54.5411 47.8328 54.8268 48.4147 54.9426C48.9966 55.0583 49.5998 54.9989 50.148 54.7719C50.6962 54.5448 51.1647 54.1603 51.4944 53.6669C51.824 53.1736 52 52.5936 52 52.0002C52 51.2046 51.6839 50.4415 51.1213 49.8789C50.5587 49.3163 49.7956 49.0002 49 49.0002ZM49 53.0002C48.8022 53.0002 48.6089 52.9416 48.4444 52.8317C48.28 52.7218 48.1518 52.5656 48.0761 52.3829C48.0004 52.2002 47.9806 51.9991 48.0192 51.8051C48.0578 51.6112 48.153 51.433 48.2929 51.2931C48.4327 51.1533 48.6109 51.058 48.8049 51.0194C48.9989 50.9809 49.1999 51.0007 49.3827 51.0764C49.5654 51.152 49.7216 51.2802 49.8314 51.4447C49.9413 51.6091 50 51.8024 50 52.0002C50 52.2654 49.8946 52.5198 49.7071 52.7073C49.5195 52.8949 49.2652 53.0002 49 53.0002Z" fill="url(#paint7_linear_208_15)"/>\n    <path d="M49 22.0002V14.0002C49 10.0502 37.06 9.00023 30 9.00023C22.94 9.00023 11 10.0502 11 14.0002V22.0002C11.0266 22.4098 11.1537 22.8064 11.3699 23.1553C11.5862 23.5041 11.885 23.7943 12.24 24.0002C11.8873 24.2089 11.5903 24.4997 11.3744 24.848C11.1585 25.1962 11.03 25.5916 11 26.0002V34.0002C11.0266 34.4098 11.1537 34.8064 11.3699 35.1553C11.5862 35.5041 11.885 35.7943 12.24 36.0002C11.8873 36.2089 11.5903 36.4997 11.3744 36.848C11.1585 37.1962 11.03 37.5916 11 38.0002V46.0002C11 50.0002 22.94 51.0002 30 51.0002C37.06 51.0002 49 50.0002 49 46.0002V38.0002C48.9699 37.5916 48.8415 37.1962 48.6256 36.848C48.4096 36.4997 48.1127 36.2089 47.76 36.0002C48.1149 35.7943 48.4137 35.5041 48.63 35.1553C48.8463 34.8064 48.9733 34.4098 49 34.0002V26.0002C48.9699 25.5916 48.8415 25.1962 48.6256 24.848C48.4096 24.4997 48.1127 24.2089 47.76 24.0002C48.1149 23.7943 48.4137 23.5041 48.63 23.1553C48.8463 22.8064 48.9733 22.4098 49 22.0002ZM13 16.4602C16.7 18.4102 24.72 19.0002 30 19.0002C35.28 19.0002 43.3 18.4102 47 16.4602V22.0002C46.69 23.0502 40.6 25.0002 30 25.0002C19.4 25.0002 13.31 23.0002 13 22.0002V16.4602ZM30 11.0002C40.55 11.0002 46.64 12.9402 47 14.0002C46.64 15.0602 40.55 17.0002 30 17.0002C19.45 17.0002 13.31 15.0002 13 14.0002C13.31 13.0002 19.41 11.0002 30 11.0002ZM13 28.4602C16.7 30.4102 24.72 31.0002 30 31.0002C35.28 31.0002 43.3 30.4102 47 28.4602V34.0002C46.69 35.0002 40.6 37.0002 30 37.0002C19.4 37.0002 13.31 35.0002 13 34.0002V28.4602ZM30 49.0002C19.4 49.0002 13.31 47.0002 13 46.0002V40.4602C16.7 42.4102 24.72 43.0002 30 43.0002C35.28 43.0002 43.3 42.4102 47 40.4602V46.0002C46.69 47.0002 40.6 49.0002 30 49.0002ZM47 38.0002C46.7 39.0602 40.6 41.0002 30 41.0002C19.4 41.0002 13.31 39.0002 13 38.0002C13 38.0002 13.2 37.5802 14.44 37.0602C18.51 38.5402 25.33 39.0002 30 39.0002C34.67 39.0002 41.49 38.5402 45.56 37.0802C46.79 37.6002 47 38.0002 47 38.0002ZM47 26.0002C46.7 27.0602 40.6 29.0002 30 29.0002C19.4 29.0002 13.31 27.0002 13 26.0002C13 26.0002 13.2 25.5802 14.44 25.0602C18.51 26.5402 25.33 27.0002 30 27.0002C34.67 27.0002 41.49 26.5402 45.56 25.0802C46.79 25.6002 47 26.0002 47 26.0002Z" fill="url(#paint8_linear_208_15)"/>\n    <path d="M31 21.0002H29V23.0002H31V21.0002Z" fill="url(#paint9_linear_208_15)"/>\n    <path d="M31 33.0002H29V35.0002H31V33.0002Z" fill="url(#paint10_linear_208_15)"/>\n    <path d="M31 45.0002H29V47.0002H31V45.0002Z" fill="url(#paint11_linear_208_15)"/>\n    <defs>\n        <linearGradient id="paint0_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint1_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint2_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint3_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint4_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint5_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint6_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint7_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint8_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint9_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint10_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n        <linearGradient id="paint11_linear_208_15" x1="30" y1="0.0195312" x2="30" y2="59.9809" gradientUnits="userSpaceOnUse">\n            <stop stop-color="#8BE9FD"/>\n            <stop offset="1" stop-color="#3EA0B4"/>\n        </linearGradient>\n    </defs>\n</svg>\n',isTableMetric:!0,querySupportsRollingAppend:!0,getQuery:({tableId:e,sampleBySeconds:t,from:n,to:r})=>1===t?`select\n            created created\n            , count() commit_rate\n          from ${E.WAL}\n          where ${e?`tableId = ${e} and `:""}\n          event = 103\n          sample by 1s\n          FROM timestamp_floor('${t}s', '${n}')\n             TO timestamp_floor('${t}s', '${r}')\n          fill(0)`:`select created, max(commit_rate) commit_rate from (\n        select\n          created created\n          , count() commit_rate\n        from ${E.WAL}\n        where ${e?`tableId = ${e} and `:""}\n        event = 103\n        sample by 1s FROM timestamp_floor('1s', '${n}') TO timestamp_floor('1s', '${r}') fill(0)\n        ) sample by ${t}s`,alignData:e=>[e.map((e=>Date.parse(e.created))),e.map((e=>ti(e.commit_rate)))],mapYValue:e=>ni(e)},Oi={distribution:1,label:"Average Transaction Size",chartTitle:"Average Transaction Size (rows/txn)",getDescription:()=>r.createElement(r.Fragment,null,"This chart tracks the mean size of transactions processed through the database API. While the database is optimized for both small and large transactions, larger batch sizes generally lead to better database performance. Monitor this metric to understand your API's transaction patterns and identify opportunities for batch size optimization. Key aspects to observe:",r.createElement("ul",null,r.createElement("li",null,"Transaction size trends and variations"),r.createElement("li",null,"Any unusually small transactions that could be batched"),r.createElement("li",null,"Consistency of batch sizes across time periods"))),icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 24">\n  <defs>\n    <linearGradient id="dbGradient" x1="0%" y1="0%" x2="100%" y2="100%">\n      <stop offset="0%" style="stop-color:#A5F3F9;stop-opacity:1" />\n      <stop offset="12.5%" style="stop-color:#9AEEF4;stop-opacity:1" />\n      <stop offset="25%" style="stop-color:#8FE7EF;stop-opacity:1" />\n      <stop offset="37.5%" style="stop-color:#84E4EC;stop-opacity:1" />\n      <stop offset="50%" style="stop-color:#77E1E9;stop-opacity:1" />\n      <stop offset="62.5%" style="stop-color:#6FDCE6;stop-opacity:1" />\n      <stop offset="75%" style="stop-color:#65D8E0;stop-opacity:1" />\n      <stop offset="87.5%" style="stop-color:#61D4DC;stop-opacity:1" />\n      <stop offset="100%" style="stop-color:#5CCFD7;stop-opacity:1" />\n    </linearGradient>\n  </defs>\n\n  \x3c!-- Main database outline --\x3e\n  <path d="M4 5c0 1.38 2.69 2.5 6 2.5s6-1.12 6-2.5-2.69-2.5-6-2.5-6 1.12-6 2.5v12c0 1.38 2.69 2.5 6 2.5s6-1.12 6-2.5v-12" fill="none" stroke="url(#dbGradient)" stroke-width="1.5"/>\n  \n  \x3c!-- Concentric lines for database layers --\x3e\n  <path d="M4 9c0 1.38 2.69 2.5 6 2.5s6-1.12 6-2.5" fill="none" stroke="url(#dbGradient)" stroke-width="1.5"/>\n  <path d="M4 13c0 1.38 2.69 2.5 6 2.5s6-1.12 6-2.5" fill="none" stroke="url(#dbGradient)" stroke-width="1.5"/>\n  \n  \x3c!-- Arrow --\x3e\n  <path d="M22 8l2-2l2 2M22 16l2 2l2-2M24 6v12" fill="none" stroke="url(#dbGradient)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>\n</svg>',isTableMetric:!0,querySupportsRollingAppend:!0,getQuery:({tableId:e,sampleBySeconds:t,from:n,to:r})=>`\n      select\n           created,\n           avg(rowCount) avg_rows,\n      from sys.telemetry_wal\n      where ${e?`tableId = ${e} `:""}\n           and event = 105\n      sample by ${t}s FROM timestamp_floor('${t}s', '${n}') TO timestamp_floor('${t}s', '${r}') fill(0)\n    `,alignData:e=>[e.map((e=>new Date(e.created).getTime())),e.map((e=>e.avg_rows?ti(e.avg_rows):1))],mapYValue:e=>e},Ri={[Yo.WAL_TRANSACTION_THROUGHPUT]:Ni,[Yo.WAL_TRANSACTION_LATENCY]:Si,[Yo.WAL_ROW_THROUGHPUT]:Ti,[Yo.TABLE_WRITE_AMPLIFICATION]:ki,[Yo.TABLE_AVERAGE_TRANSACTION_SIZE]:Oi},Ii=(0,s.ZP)(fi.V.Description).withConfig({displayName:"add-metric-dialog__StyledDescription"})(["\n  display: grid;\n  gap: 2rem;\n"]),Ai=s.ZP.div.withConfig({displayName:"add-metric-dialog__Metrics"})(["\n  display: grid;\n  gap: 2rem;\n  grid-template-columns: repeat(2, 1fr);\n"]),Li=(0,s.ZP)(Pn.x).attrs({flexDirection:"column",gap:"0"}).withConfig({displayName:"add-metric-dialog__Metric"})(["\n  border-radius: 0.4rem;\n  cursor: pointer;\n  border: 1px solid transparent;\n  padding-bottom: 2rem;\n  background: ",";\n\n  &:hover {\n    border-color: ",";\n  }\n"],(({theme:e})=>e.color.backgroundLighter),(({theme:e})=>e.color.comment)),Pi=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"center"}).withConfig({displayName:"add-metric-dialog__Image"})(["\n  width: 100%;\n  height: 10rem;\n"]),Di=({open:e,onOpenChange:t})=>{const{activeBuffer:n,buffers:a,updateBuffer:o}=ma(),i=a.find((e=>e.id===n?.id)),l=i?.metricsViewState?.metrics??[],s=l.length>0?l[l.length-1]:void 0,c=s?((e,t)=>{const n=wi.filter((e=>e!==t)),r=n.filter((t=>!e.includes(t)));return r.length>0?r[Math.floor(Math.random()*r.length)]:n[Math.floor(Math.random()*n.length)]})(l.map((e=>e.color)),s.color):"#FF6B6B";return r.createElement(fi.V.Root,{open:e},r.createElement(fi.V.Trigger,{asChild:!0},r.createElement(Ce.A,null,r.createElement(Dn.z,{skin:"secondary",prefixIcon:r.createElement(hi.J,{size:"18px"}),onClick:()=>t(!e)},"Add widget"))),r.createElement(fi.V.Portal,null,r.createElement(Ce.A,null,r.createElement(ve.a,{primitive:fi.V.Overlay})),r.createElement(fi.V.Content,{onEscapeKeyDown:()=>t(!1),onInteractOutside:()=>t(!1)},r.createElement(fi.V.Title,null,r.createElement(Pn.x,null,r.createElement(hi.J,{size:22}),"Add widget")),r.createElement(Ii,null,r.createElement(Ai,null,Object.entries(Ri).map((([e,n])=>r.createElement(Li,{key:e,onClick:()=>(async e=>{if(i?.id){const n=Ei()(i,{metricsViewState:{metrics:[...i.metricsViewState?.metrics??[],{metricType:e,position:i.metricsViewState?.metrics?.length??0,color:c}]}});await o(i.id,n),t(!1)}})(e)},r.createElement(Pi,null,r.createElement("svg",{width:"64",height:"64",dangerouslySetInnerHTML:{__html:n.icon}})),r.createElement(Pe,{color:"foreground",weight:600},n.label)))))))))};var Mi=n(40015),Bi=n(35637),Fi=n(29943),Hi=n.n(Fi);const Vi=(e,t,n)=>({hooks:{setCursor:r=>{if(!e.current||!t.current)return;const{idx:a}=r.cursor,o=null!=a?r.data[0][a]:null,i=null!=a?r.data[1][a]:null;if([i,o].every((e=>null!==e))){try{e.current.textContent=b(o,Qo)}catch(t){e.current.textContent=o?.toString()??""}t.current.textContent=n(i)}else e.current.textContent=null,t.current.textContent=null}}}),zi=s.ZP.div.withConfig({displayName:"graph__Actions"})(["\n  margin-right: 0;\n"]),Zi=(0,s.ZP)(Pn.x).attrs({align:"center",flexDirection:"column",gap:0}).withConfig({displayName:"graph__Root"})(["\n  position: relative;\n  background-color: ",";\n  height: 25rem;\n"],(({theme:e})=>e.color.backgroundLighter)),Ui=s.ZP.div.withConfig({displayName:"graph__BeforeLabel"})(["\n  margin-left: 0;\n"]),qi=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"space-between"}).withConfig({displayName:"graph__Header"})(["\n  position: relative;\n  width: 100%;\n"]),Gi=s.ZP.span.withConfig({displayName:"graph__HeaderText"})(["\n  font-size: 1.4rem;\n  font-weight: 600;\n  padding: 0 0 0 1rem;\n"]),$i=(0,s.ZP)(Pn.x).attrs({flexDirection:"column",align:"center"}).withConfig({displayName:"graph__GraphWrapper"})(["\n  position: relative;\n  padding: 1rem 0;\n"]),ji=(0,s.ZP)(Pn.x).attrs({flexDirection:"column",align:"center",justifyContent:"center"}).withConfig({displayName:"graph__GraphOverlay"})(["\n  width: 100%;\n  height: 15rem;\n  position: absolute;\n  z-index: 1;\n"]),Wi=s.ZP.div.withConfig({displayName:"graph__Label"})(["\n  position: absolute;\n  bottom: 1rem;\n  display: flex;\n  gap: 0.5rem;\n  font-family: ",";\n"],(({theme:e})=>e.font)),Qi=s.ZP.span.withConfig({displayName:"graph__LabelValue"})(["\n  color: ",";\n"],(({theme:e})=>e.color.cyan)),Yi=(0,s.ZP)(Bi.j).withConfig({displayName:"graph__ErrorIcon"})(["\n  color: ",";\n"],(({theme:e})=>e.color.red)),Ki=({dateFrom:e,dateTo:t,tableId:n,tableName:a,beforeLabel:o,data:i,canZoomToData:l,colors:c,loading:d,actions:u,onZoomToData:m,widgetConfig:p,hasError:g})=>{const f=(0,r.useContext)(s.Ni),h=(0,r.useRef)(null),E=(0,r.useRef)(null),y=(0,r.useRef)(),{isTableMetric:w,mapYValue:C,chartTitle:v}=p,_=new Date(di(e)).getTime(),x=new Date(di(t)).getTime(),[S,k]=(0,r.useState)(d),[T,N]=(0,r.useState)(void 0),O=new ResizeObserver((e=>{y.current?.setSize({width:e[0].contentRect.width,height:200})})),R=di(e),I=di(t);(0,r.useEffect)((()=>{N((({data:e,startTime:t,endTime:n,colors:r,mapXValue:a,mapYValue:o=(e=>(+e).toFixed(4)),timeRef:i,valueRef:l,widgetConfig:s,theme:c})=>{const d={stroke:c.color.gray2,labelFont:`600 12px ${c.font}`,font:`14px ${c.font}`,ticks:{show:!1,stroke:c.color.gray1,width:.8,dash:[],size:10},grid:{stroke:c.color.selectionDarker}},u=[{...d,values:(e,t)=>{let n=[];return t.map(((e,r)=>{const o=a(e,r,t);return null===o||n.includes(o)?null:(n.push(o),o)}))}},{...d,values:(e,t)=>t.map((e=>o(e)))}],m={x:{},y:{label:"",points:{show:1===e[0].length},stroke:r[0],fill:(p=r[0],p.startsWith("#")?`${p}33`:p.startsWith("rgb")?p.replace("rgb","rgba").replace(")",", 0.2)"):p),width:.6,value:(e,t)=>3!==s.distribution?o(t):t}};var p;const g={x:{time:!0,range:[t,n]},y:{distr:s.distribution,range:(e,t,n)=>[(e.data[0].length>1&&3!==s.distribution&&t!==n?t:3!==s.distribution?0:1)||0,n||1]}};return{ms:1,padding:[10,20,0,20],cursor:{sync:{key:"metrics",setSeries:!0}},axes:Object.values(u),series:Object.values(m),scales:g,legend:{show:!1,markers:{show:!1}},plugins:[Vi(i,l,o)]}})({data:i,startTime:_,endTime:x,colors:c,timeRef:h,valueRef:E,mapXValue:e=>((e,t,n)=>{let r;const a=(n-t)/1e3;return r=a<60?"HH:mm:ss":a<3600||a<=86400||a<=86400?"HH:mm":"dd/MM",b(e,r)})(e,_,x),mapYValue:C,widgetConfig:p,theme:f}))}),[i]);const A=(0,r.useRef)(null);(0,r.useEffect)((()=>(A.current&&O.observe(A.current),()=>{O.disconnect()})),[A.current]),(0,r.useEffect)((()=>{if(d){const e=setTimeout((()=>{k(!0)}),1e3);return()=>clearTimeout(e)}k(!1)}),[d]);const L=i[1].length>0?C(Math.floor(i[1][i[1].length-1])):void 0;return r.createElement(Zi,{ref:A},r.createElement(qi,null,r.createElement(Pn.x,{gap:"0.5rem",align:"center"},r.createElement(Ui,null,o),r.createElement(Gi,null,v),r.createElement(Xe,{icon:r.createElement(It.d,{size:"16px"}),tooltip:p.getDescription({lastValue:L,sampleBySeconds:li(R,I)}),placement:"bottom"}),S&&r.createElement(Mi.a,{size:"18px",spin:!0}),g&&r.createElement(Xe,{icon:r.createElement(Yi,{size:"18px"}),tooltip:"Error fetching latest data, try refreshing manually",placement:"bottom"})),r.createElement(zi,null,u)),r.createElement($i,null,!si(i)&&r.createElement(ji,null,w&&!a?r.createElement(Pe,{color:"gray2"},n?"Table does not exist. Please select another one":"Select a table to see metrics"):r.createElement(Pe,{color:"gray2"},"No data available for this period"),l&&r.createElement(Dn.z,{skin:"secondary",onClick:m},"Zoom to data")),r.createElement("div",{ref:A},T&&r.createElement(Hi(),{options:{...T,height:200,width:A.current?.clientWidth??0},data:i,onCreate:e=>{y.current=e}})),r.createElement(Wi,null,r.createElement("span",{ref:h}),r.createElement(Qi,{ref:E}))))};var Xi=n(60407),Ji=n(85501),el=n(37355),tl=n(66145),nl=n.n(tl);function rl(){return rl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},rl.apply(this,arguments)}const al=s.ZP.div.withConfig({displayName:"table-selector__Root"})(["\n  display: flex;\n  position: relative;\n  margin-left: 1rem;\n"]),ol=(0,s.ZP)(el.i).withConfig({displayName:"table-selector__TableIcon"})(["\n  color: ",";\n"],(({theme:e})=>e.color.gray2)),il=(0,s.ZP)(Gn.I).withConfig({displayName:"table-selector__StyledInput"})(["\n  border: 1px solid transparent;\n  background: transparent;\n  font-weight: 600;\n  font-size: 1.6rem;\n  color: ",";\n  text-transform: uppercase;\n  width: 100%;\n\n  &:hover,\n  &:active,\n  &:focus {\n    background: transparent;\n    border-color: ",";\n  }\n"],(({theme:e})=>e.color.yellow),(({theme:e})=>e.color.comment)),ll=(0,s.ZP)(il).withConfig({displayName:"table-selector__ShadowInput"})(["\n  visibility: hidden;\n  width: max-content;\n  z-index: 1;\n  position: fixed;\n"]),sl=s.ZP.div.withConfig({displayName:"table-selector__Wrapper"})(["\n  position: absolute;\n  width: 30rem;\n  z-index: 100;\n  top: 100%;\n  overflow-y: auto;\n  max-height: calc(10 * 3rem);\n"]),cl=s.ZP.ul.withConfig({displayName:"table-selector__Options"})(["\n  list-style: none;\n  background: ",";\n  box-shadow: 0 5px 5px 0 ",";\n  margin: 0;\n  padding: 0.5rem;\n  border-radius: 0.4rem;\n"],(({theme:e})=>e.color.backgroundDarker),(({theme:e})=>e.color.black40)),dl=s.ZP.li.withConfig({displayName:"table-selector__Item"})(['\n  display: flex;\n  align-items: center;\n  height: 3rem;\n  padding: 0 1rem;\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",\n    "Courier New", monospace;\n  ',"\n\n  ","\n"],(({active:e,theme:t})=>`\n    background: ${e?t.color.selection:"transparent"};\n  `),(({disabled:e,theme:t})=>`\n    color: ${e?t.color.gray1:t.color.foreground};\n    cursor: ${e?"not-allowed":"pointer"};\n  `)),ul=({tableId:e,defaultValue:t,options:n,onSelect:a,placeholder:o,loading:i})=>{const[l,s]=(0,r.useState)(!1),c=(0,r.useRef)(null),d=(0,r.useRef)(null),[u,m]=(0,r.useState)(t??""),[p,g]=(0,r.useState)(-1),f=qe("ArrowDown"),h=qe("ArrowUp"),b=qe("Enter"),E=(0,r.useRef)(null),y=(0,r.useRef)(void 0),w=n.filter((e=>!u||e.label.toLowerCase().includes(u.toLowerCase()))).sort(((e,t)=>e.label.toLowerCase().localeCompare(t.label.toLowerCase())));return(0,r.useEffect)((()=>{if(c.current&&d.current&&c.current&&d.current&&!i){c.current.style.width=`${d.current.offsetWidth}px`;const n=t??"";y.current!==n&&(m(t??""),y.current=t??""),""!==t||e||c.current.focus();const r=e=>{c.current?.contains(e.target)||(e.target instanceof HTMLElement||e.target instanceof SVGElement)&&"table-selector-item"!==e.target.getAttribute("data-hook")&&(t&&m(t),s(!1))};return document.addEventListener("click",r),()=>document.removeEventListener("click",r)}}),[t,c,i]),(0,r.useEffect)((()=>{f?g(p<w.length-1?p+1:0):h&&g(p>0?p-1:w.length-1)}),[f,h]),(0,r.useEffect)((()=>{if(b&&w.length>0&&-1!==p){if(w[p].disabled)return;a(w[p].value),s(!1),m(w[p].label)}}),[b]),(0,r.useEffect)((()=>{1===w.length&&u?.toLowerCase()===w[0].label.toLowerCase()&&g(0)}),[u,w]),r.createElement(al,null,r.createElement(Pn.x,{align:"center",gap:"0.5rem"},r.createElement(ol,{size:"18px"}),r.createElement(il,{value:u,placeholder:""!==t?t:o,ref:c,onFocus:()=>{m(t),c.current?.select(),s(!0)},onKeyUp:e=>{"Backspace"===e.key?""===u&&(m(""),s(!0),g(-1)):"Escape"===e.key&&(m(t),s(!1))},onChange:e=>{m(e.target.value??"")}})),r.createElement(ll,{ref:d,as:"span"},""!==t?t:o),l&&r.createElement(sl,{ref:E},r.createElement(cl,null,w.sort(((e,t)=>e.disabled===t.disabled?0:e.disabled?1:-1)).map(((e,t)=>r.createElement(dl,rl({"data-hook":"table-selector-item",tabIndex:t,active:p===w.indexOf(e),key:e.value,onMouseEnter:()=>g(t),onMouseLeave:()=>g(-1),disabled:e.disabled},!e.disabled&&{onClick:()=>{c.current.value=e.label,a(e.value),m(e.label),s(!1)}}),r.createElement(nl(),{highlightClassName:"highlight",searchWords:[u??""],textToHighlight:e.label})))))))},ml=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"center"}).withConfig({displayName:"metric__MetricInfoRoot"})(["\n  background-color: ",";\n  height: 25rem;\n"],(({theme:e})=>e.color.backgroundLighter)),pl=(0,s.ZP)(Dn.z).withConfig({displayName:"metric__ActionButton"})(["\n  padding: 0;\n  width: 3rem;\n"]),gl=({dateFrom:e,dateTo:t,metric:n,onRemove:a,onTableChange:i,onColorChange:l})=>{const{quest:s}=(0,r.useContext)(qr),[c,d]=(0,r.useState)(void 0!==n.tableId),[u,m]=(0,r.useState)(),[p,g]=(0,r.useState)(!1),f=r.useRef(n.tableId),h=r.useRef(e),b=r.useRef(t),E=r.useRef([[],[]]),y=r.useRef(),[w,C]=(0,r.useState)(!1);h.current=e,b.current=t;const v=(0,o.v9)(Cn.query.getTables),_=v.find((e=>e.id===n.tableId))?.table_name;y.current=_;const x=Ri[n.metricType];if(!E.current&&!c&&n.tableId)return n.tableId=void 0,r.createElement(ml,null,r.createElement(Bi.j,{size:"18px"}),"Cannot load metric:"," ",x?x.label:n.metricType);const S=async e=>{x.querySupportsRollingAppend;let t=!0;d(!0),C(!1);try{const e=di(h.current),t=di(b.current),r=ri(e),a=ri(t),o=li(e,t),i=await Promise.all([s.query((n=x.getQuery({tableId:f.current,sampleBySeconds:o,from:r,to:a}),n?n.replace(/\/\*[\s\S]*?\*\//g,"").replace(/--.*/g,"").replace(/\s+(?=(?:[^'"]|'[^']*'|"[^"]*")*$)/g," ").replace(/\s*,\s*/g,",").replace(/\s*\(\s*/g,"(").replace(/\s*\)\s*/g,")").replace(/\s*([+\-*\/%])\s*/g,"$1").replace(/\s*(=|<>|!=|>=|<=|>|<)\s*/g,"$1").trim():""))]);if(i[0]&&i[0].type===T.DQL){const n=i[0].data;if(n.length>0||!e||!t||!o)E.current=x.alignData(n);else{const n=Date.parse(e),r=Date.parse(t),a=Math.floor((r-n)/1e3/o),i=Array.from({length:a},((e,t)=>1e3*(n/1e3+t*o))),l=new Array(a).fill(0);E.current=[i,l]}}if(i[1]&&i[1].type===T.DQL){const e=i[1].data[0];m(e?new Date(e.created).getTime():void 0)}}catch(e){t&&(console.error(e),C(!0))}finally{t&&d(!1)}var n;return()=>{t=!1}},k=e=>{y.current?S():(E.current=[[],[]],d(!1))};(0,r.useEffect)((()=>{_&&(y.current=_,f.current=n.tableId,E.current=[[],[]],S())}),[_,x]),(0,r.useEffect)((()=>(I.subscribe(A.METRICS_REFRESH_DATA,k),()=>{I.unsubscribe(A.METRICS_REFRESH_DATA,k)})),[_,x]);const N=(0,r.useMemo)((()=>v.map((e=>({label:e.table_name,value:e.id.toString(),disabled:!e.walEnabled})))),[v]);return r.createElement(Ki,{dateFrom:e,dateTo:t,data:n.tableId&&si(E.current)?E.current:[[],[]],canZoomToData:!1,onZoomToData:()=>{},colors:[n.color],loading:c,tableId:n.tableId,tableName:_,widgetConfig:x,beforeLabel:r.createElement(ul,{tableId:n.tableId,loading:c,options:N,placeholder:"Select table",onSelect:e=>i(n,parseInt(e)),defaultValue:y.current||""}),actions:r.createElement(Pn.x,{gap:"0.5rem",align:"center"},r.createElement(Xe,{icon:r.createElement(Ce.A,null,r.createElement(Xi.J,{open:p,onOpenChange:g,trigger:r.createElement(pl,{skin:"transparent"},r.createElement(Ji.Y,{size:"18px"})),align:"center"},r.createElement(xi,{onSelect:e=>l(n,e),selectedColor:n.color}))),tooltip:"Choose series color",placement:"top"}),r.createElement(Xe,{icon:r.createElement(pl,{skin:"transparent",onClick:()=>a(n)},r.createElement(Fo.r,{size:"18px"})),tooltip:"Remove metric",placement:"top"})),hasError:w})};var fl=n(55743),hl=n(16012),bl=n(93065),El=n(79540),yl=n(64788),wl=n(94014);const Cl=({className:e,min:t,max:n,value:a,onChange:o,selectRange:i})=>r.createElement(wl.ZP,{className:e,defaultValue:a,minDate:t,maxDate:n,minDetail:"month",maxDetail:"month",returnValue:"start",onChange:e=>o(e),selectRange:i});var vl=n(48232);function _l(){return _l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_l.apply(this,arguments)}const xl=(0,s.ZP)(Pn.x).attrs({gap:"1rem",flexDirection:"column",align:"flex-start"}).withConfig({displayName:"date-time-picker__Root"})(["\n  background: ",";\n  width: 50rem;\n  padding: 1rem 1rem 0 1rem;\n"],(({theme:e})=>e.color.backgroundDarker)),Sl=(0,s.ZP)(Pn.x).attrs({gap:0,align:"flex-start"}).withConfig({displayName:"date-time-picker__Cols"})(["\n  width: 100%;\n"]),kl=(0,s.ZP)(Dn.z).withConfig({displayName:"date-time-picker__Trigger"})(["\n  padding-right: 0;\n"]),Tl=(0,s.ZP)(Pn.x).attrs({flexDirection:"column",gap:"1rem",align:"flex-start"}).withConfig({displayName:"date-time-picker__DatePickers"})(["\n  width: 70%;\n  align-self: flex-start;\n  padding-right: 1rem;\n  padding-left: 1rem;\n"]),Nl=s.ZP.ul.withConfig({displayName:"date-time-picker__MetricDurations"})(["\n  width: 40%;\n  list-style: none;\n  margin: 0;\n  padding: 0 0 0 1rem;\n  border-left: 1px solid ",";\n"],(({theme:e})=>e.color.selection)),Ol=s.ZP.li.withConfig({displayName:"date-time-picker__MetricDurationItem"})(["\n  cursor: pointer;\n  height: 3rem;\n  padding: 0 1rem;\n  line-height: 3rem;\n\n  &:hover {\n    background: ",";\n  }\n\n  ","\n"],(({theme:e})=>e.color.selection),(({selected:e,theme:t})=>e&&`& { background: ${t.color.selection}; }`)),Rl=(0,s.ZP)(Pn.x).attrs({gap:0,align:"center"}).withConfig({displayName:"date-time-picker__Footer"})(["\n  width: 100%;\n  padding: 1rem 0;\n  border-top: 1px solid ",";\n"],(({theme:e})=>e.color.selection)),Il=({min:e,max:t,name:n,label:a,dateFrom:o,dateTo:i,onChange:l})=>{const{setValue:s}=(0,Fn.Gc)(),c=di(o),d=di(i);return r.createElement(ir.Item,{name:n,label:a},r.createElement(Pn.x,{gap:"0.5rem",align:"center"},r.createElement(ir.Input,{name:n,onChange:e=>{s(n,e.target.value);try{const t=di(e.target.value);if("Invalid date"===t)return;"dateFrom"===n?l([t,i]):"dateTo"===n&&l([o,t])}catch(e){console.error(e)}},placeholder:"now"}),r.createElement(Xi.J,{trigger:r.createElement(Dn.z,{skin:"secondary"}," ",r.createElement(fl.f,{size:"18px"})," "),align:"center"},r.createElement(Cl,{min:e,max:t,onChange:e=>{const t=e;["dateFrom","dateTo"].forEach(((n,r)=>{e&&t[r]&&s(n,b(new Date(t[r]).getTime()))}))},value:["Invalid date"!==c?new Date(di(o)):new Date,"Invalid date"==d?new Date(di(i)):new Date],selectRange:!0}))))},Al=({dateFrom:e,dateTo:t,onDateFromToChange:n})=>{const[a,o]=(0,r.useState)(!1),[i,l]=(0,r.useState)(e),[s,c]=(0,r.useState)(t),d=(0,vl.W)(new Date,12),u=new Date,m={"string.empty":"Please enter a date or duration","string.invalidDate":"Date format or duration is invalid","string.toIsBeforeFrom":"To date must be after From date","string.dateInFuture":"Please set a date in the past or use `now`","string.fromIsAfterTo":"From date must be before To date","string.sameValues":"From and To dates cannot be the same","any.custom":"One of the values is invalid","string.maxDateRange":"Date range cannot exceed 7 days"},p=sr().object({dateFrom:sr().any().required().custom(((e,t)=>{const n=di(e),r=new Date(n).getTime(),a=(new Date).getTime();try{const o=new Date(di(t.state.ancestors[0].dateTo)).getTime();return"Invalid date"===n?t.error("string.invalidDate"):r>=o?t.error("string.fromIsAfterTo"):r>a?t.error("string.dateInFuture"):r===a?t.error("string.sameValues"):o-r>6048e5?t.error("string.maxDateRange"):e}catch(e){return t.error("any.custom")}})).messages(m),dateTo:sr().any().required().custom(((e,t)=>{const n=di(e),r=new Date(n).getTime(),a=(new Date).getTime(),o=new Date(di(t.state.ancestors[0].dateFrom)).getTime();return"Invalid date"===n?t.error("string.invalidDate"):r<=o?t.error("string.toIsBeforeFrom"):r>a?t.error("string.dateInFuture"):r===a?t.error("string.sameValues"):r-o>6048e5?t.error("string.maxDateRange"):e})).messages(m)}),g={min:d,max:u,dateFrom:i,dateTo:s,onChange:([e,t])=>{l(e),c(t)}};return(0,r.useEffect)((()=>{l(e),c(t)}),[e,t]),r.createElement(Xi.J,{open:a,onOpenChange:o,trigger:r.createElement(kl,{skin:"secondary",prefixIcon:r.createElement(hl.q,{size:"18px"})},ui(e,t),a?r.createElement(El.D,{size:"28px"}):r.createElement(yl.D,{size:"28px"}))},r.createElement(xl,null,r.createElement(Sl,null,r.createElement(Tl,null,r.createElement(Pe,{weight:600,color:"foreground",size:"lg"},"Absolute time range"),r.createElement(ir,{name:"dateRanges",onSubmit:async e=>{e.dateFrom&&e.dateTo&&(n(ci(e.dateFrom)?e.dateFrom:(0,jo.c)(e.dateFrom),ci(e.dateTo)?e.dateTo:(0,jo.c)(e.dateTo)),o(!1))},defaultValues:{dateFrom:e,dateTo:t},validationSchema:p},r.createElement(Pn.x,{flexDirection:"column",gap:"1rem",align:"flex-start"},r.createElement(Il,_l({name:"dateFrom",label:"From"},g)),r.createElement(Il,_l({name:"dateTo",label:"To"},g)),r.createElement(ir.Submit,null,"Apply")))),r.createElement(Nl,null,Object.values(Ko).map((({label:a,dateFrom:i,dateTo:l})=>r.createElement(Ol,{key:a,selected:e===i&&t===l,onClick:()=>{n(i,l),o(!1)}},a))))),r.createElement(Rl,null,r.createElement(Pn.x,{gap:"0.5rem",align:"center"},r.createElement(bl.q,{size:"14px"}),r.createElement(Pe,{color:"foreground"},h()," (",(()=>{const e=(new Date).getTimezoneOffset(),t=Math.floor(Math.abs(e)/60),n=Math.abs(e)%60;return`GMT${e>0?"-":"+"}${String(t).padStart(2,"0")}:${String(n).padStart(2,"0")}`})(),")")))))};function Ll(){return Ll=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ll.apply(this,arguments)}const Pl=s.ZP.div.withConfig({displayName:"Metrics__Root"})(["\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  height: 100%;\n  background: #2c2e3d;\n  padding-bottom: calc(4.5rem);\n"]),Dl=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"space-between"}).withConfig({displayName:"Metrics__Toolbar"})(["\n  width: 100%;\n  height: 4.5rem;\n  padding: 0 2.5rem;\n  border-bottom: 1px solid\n    ",";\n  box-shadow: 0 2px 10px 0 rgba(23, 23, 23, 0.35);\n  white-space: nowrap;\n  flex-shrink: 0;\n"],(({theme:e})=>e.color.backgroundDarker)),Ml=(0,s.ZP)(Pe).withConfig({displayName:"Metrics__Header"})(["\n  font-size: 1.8rem;\n  font-weight: 600;\n  color: ",";\n  margin-bottom: 1rem;\n"],(({theme:e})=>e.color.foreground)),Bl=(0,s.ZP)(Pn.x).attrs({align:"flex-start",gap:"2.5rem"}).withConfig({displayName:"Metrics__Charts"})(["\n  align-content: ",";\n  padding: 2.5rem;\n  overflow-y: auto;\n  height: 100%;\n  width: 100%;\n  flex-wrap: wrap;\n\n  > div {\n    width: ",";\n    flex-shrink: 0;\n  }\n"],(({noMetrics:e})=>e?"center":"flex-start"),(({viewMode:e})=>e===Xo.GRID?"calc(50% - 1.25rem)":"100%")),Fl=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"center"}).withConfig({displayName:"Metrics__GlobalInfo"})(["\n  margin: auto;\n\n  code {\n    background: #505368;\n    color: ",";\n  }\n"],(({theme:e})=>e.color.foreground)),Hl=()=>r.createElement(Fl,null,r.createElement(Pn.x,{gap:"1.5rem",flexDirection:"column"},r.createElement(Ml,null,"Metrics unavailable"),r.createElement(Pe,{color:"foreground"},"Enable Telemetry to access WAL table metrics."),r.createElement(Pe,{color:"foreground"},"Set ",r.createElement("code",null,"telemetry.enabled=true")," in your server.conf file and restart the server."),r.createElement(Pe,{color:"foreground"},"Alternatively, set ",r.createElement("code",null,"QDB_TELEMETRY_ENABLED=true")," ENV var for the same effect."),r.createElement(ot,{color:"cyan",hoverColor:"cyan",href:"https://questdb.io/docs/configuration/#telemetry",rel:"noreferrer",target:"_blank"},r.createElement(Pn.x,{align:"center",gap:"0.25rem"},r.createElement(ha.d,{size:"16px"}),"Documentation")))),Vl=()=>{const{activeBuffer:e,updateBuffer:t,buffers:n}=ma(),[a,i]=(0,r.useState)(Xo.GRID),[l,s]=(0,r.useState)((0,jo.c)(new Date)),[c,d]=(0,r.useState)((0,jo.c)(new Date)),[u,m]=(0,r.useState)(),[p,g]=(0,r.useState)(!1),[f,h]=(0,r.useState)([]),b=(0,o.v9)(Cn.telemetry.getConfig),E=b&&b.enabled,y=r.useRef(!0),w=r.useRef(),C=r.useRef(),v=n.find((t=>t.id===e?.id)),_=u?u===Jo.AUTO?ei[((e,t)=>{const n=(new Date(di(t)).getTime()-new Date(di(e)).getTime())/1e3;return n<=300||n<=900?Jo.FIVE_SECONDS:n<=3600?Jo.TEN_SECONDS:n<=10800||n<=21600||n<=43200||n<=86400?Jo.THIRTY_SECONDS:Jo.ONE_MINUTE})(l,c)]:ei[u]:0,x=e=>{v?.id&&(t(v?.id,{metricsViewState:{...v?.metricsViewState,metrics:e}}),v?.metricsViewState?.metrics&&(v.metricsViewState.metrics=e))},S=e=>{I.publish(A.METRICS_REFRESH_DATA,{dateFrom:l,dateTo:c,overwrite:e})},k=e=>{e.removed=!0,v?.id&&v?.metricsViewState?.metrics&&x(v?.metricsViewState?.metrics.filter((t=>t.position!==e.position)).map(((e,t)=>({...e,position:t}))))},T=(e,t)=>{v?.id&&v?.metricsViewState?.metrics&&x(v?.metricsViewState?.metrics.map((n=>n.position===e.position?{...n,tableId:t}:n)))},N=(e,t)=>{v?.id&&v?.metricsViewState?.metrics&&x(v?.metricsViewState?.metrics.map((n=>n.position===e.position?{...n,color:t}:n)))},O=(0,r.useCallback)((()=>{y.current=!0,w.current!==Jo.OFF&&S()}),[w.current]),R=(0,r.useCallback)((()=>{y.current=!1}),[]),[L,P]=((e=1e3)=>{const[t,n]=(0,r.useState)(!0),a=(0,r.useRef)(null),o=(0,r.useRef)(null),i=(0,r.useRef)(null),l=(0,r.useRef)(Date.now()),s=(0,r.useCallback)((()=>{const r=Date.now(),a=r-l.current;l.current=r;const o=a>2*e;o!==!t&&n(!o),i.current=requestAnimationFrame(s)}),[e,t]);return(0,r.useEffect)((()=>{o.current=new IntersectionObserver((e=>{e.forEach((e=>{n(e.isIntersecting)}))}),{threshold:.1});const e=()=>{n(!document.hidden)};return a.current&&o.current.observe(a.current),document.addEventListener("visibilitychange",e),i.current=requestAnimationFrame(s),()=>{o.current&&o.current.disconnect(),document.removeEventListener("visibilitychange",e),i.current&&cancelAnimationFrame(i.current)}}),[s]),[a,t]})(1e3),D=(0,r.useRef)(P);(0,r.useEffect)((()=>{D.current=P}),[P]);const M=(0,r.useCallback)((()=>{C.current&&clearInterval(C.current),u&&u!==Jo.OFF?C.current=setInterval((()=>{D.current&&S()}),_>0?1e3*_:0):clearInterval(C.current)}),[u,_,S]);return(0,r.useEffect)((()=>{if(v){const e=v?.metricsViewState?.metrics,t=v?.metricsViewState?.refreshRate,n=v?.metricsViewState?.viewMode,r=v?.metricsViewState?.dateFrom,a=v?.metricsViewState?.dateTo;r&&s(r),a&&d(a),e&&h(e),t&&m(t),n&&i(n)}}),[v]),(0,r.useEffect)((()=>{if(v?.id){v?.metricsViewState?.metrics&&(v.metricsViewState.metrics=v.metricsViewState.metrics.filter((e=>Ri[e.metricType])).map((e=>({...e,removed:!1}))));const e=Ei()(v,{metricsViewState:{...a!==v?.metricsViewState?.viewMode&&{viewMode:a},...l!==v?.metricsViewState?.dateFrom&&{dateFrom:l},...c!==v?.metricsViewState?.dateTo&&{dateTo:c}}});l&&c&&u===Jo.AUTO&&M(),l&&c&&(t(v.id,e),S(!0))}}),[a,l,c]),(0,r.useEffect)((()=>{u&&(w.current=u,M(),v?.id&&t(v.id,{metricsViewState:{...v?.metricsViewState,refreshRate:u}}))}),[u]),(0,r.useEffect)((()=>(I.subscribe(A.TAB_FOCUS,O),I.subscribe(A.TAB_BLUR,R),()=>{I.unsubscribe(A.TAB_FOCUS,O),I.unsubscribe(A.TAB_BLUR,R),clearInterval(C.current)})),[]),r.createElement(Pl,Ll({"data-hook":"metrics-root"},E?{}:{ref:L}),E?r.createElement(r.Fragment,null,r.createElement(Dl,null,r.createElement(Di,{open:p,onOpenChange:g}),r.createElement(Pn.x,{align:"center",gap:"1rem"},r.createElement(Pn.x,{gap:"0.5rem",style:{flexShrink:0}},r.createElement(Xe,{icon:r.createElement(Dn.z,{skin:"secondary",onClick:()=>{S(!0)}},r.createElement(mi.h,{size:"20px"})),tooltip:"Refresh all widgets",placement:"bottom"}),r.createElement(Xe,{icon:r.createElement(er.P,{name:"refresh_rate",value:u,options:Object.values(Jo).map((e=>({label:`Refresh: ${e}`,value:e}))),onChange:e=>m(e.target.value)}),tooltip:"Widget refresh rate",placement:"bottom"})),r.createElement(Xe,{icon:r.createElement(Ce.A,null,r.createElement(Al,{dateFrom:l,dateTo:c,onDateFromToChange:(e,t)=>{s(e),d(t)}})),tooltip:"Time duration",placement:"bottom"}),r.createElement(Xe,{icon:r.createElement(Dn.z,{skin:"secondary",onClick:()=>i(a===Xo.GRID?Xo.LIST:Xo.GRID)},a===Xo.LIST?r.createElement(pi.B,{size:"18px"}):r.createElement(gi.v,{size:"18px"})),tooltip:r.createElement(r.Fragment,null,"Toggle view mode",r.createElement("br",null),"to ",a===Xo.GRID?"column":"grid"),placement:"bottom",textAlign:"center"}))),r.createElement(Bl,{noMetrics:0===f.length,viewMode:a},0===f.length&&r.createElement(Fl,null,r.createElement(Pn.x,{gap:"1.5rem",flexDirection:"column"},r.createElement(Ml,null,"Add your first widget to see metrics"),r.createElement(Dn.z,{skin:"secondary",onClick:()=>g(!0),prefixIcon:r.createElement(hi.J,{size:"18px"})},"Add widget"))),f&&f.sort(((e,t)=>e.position-t.position)).filter((e=>Ri[e.metricType]&&!e.removed)).map(((e,t)=>r.createElement(gl,{dateFrom:l,dateTo:c,key:t,metric:e,onRemove:k,onTableChange:T,onColorChange:N}))))):r.createElement(Hl,null))};var zl=n(99637),Zl=n(76410),Ul=n(52655);const ql=s.ZP.div.withConfig({displayName:"styles__Wrapper"})(["\n  display: flex;\n  align-items: center;\n  border-right: none;\n  height: ",";\n  border-bottom: ",";\n  padding: 0 1rem;\n  flex-shrink: 0;\n  width: 100%;\n  overflow-x: auto;\n  scrollbar-width: none;\n  -ms-overflow-style: none; \n  &::-webkit-scrollbar {\n    display: none;\n  }\n\n  ",";\n"],(({isMinimized:e})=>e?"auto":"4.5rem"),(({isMinimized:e,theme:t})=>e?"none":`1px solid ${t.color.backgroundDarker}`),ee),Gl=s.ZP.div.withConfig({displayName:"styles__Content"})(["\n  display: flex;\n  margin-left: 0.5rem;\n  white-space: nowrap;\n"]),$l=s.ZP.div.withConfig({displayName:"styles__SideContent"})(["\n  padding-left: 1rem;\n  text-overflow: ellipsis;\n"]);var jl=n(96035);const Wl=(0,s.ZP)(Pe).withConfig({displayName:"Timestamp__TimestampText"})(["\n  display: flex;\n  margin-right: 0.5rem;\n  white-space: nowrap;\n"]),Ql=({createdAt:e})=>{const t=(0,r.useMemo)(V,[]);return r.createElement(Wl,{color:"gray2"},"[",(0,g.WU)(e,"pppp",{locale:Q(t)}),"]")},Yl=(0,s.ZP)(At.W).withConfig({displayName:"SuccessNotification__CheckmarkOutlineIcon"})(["\n  color: ",";\n  flex-shrink: 0;\n"],M("green")),Kl=(0,s.ZP)(jl.i).withConfig({displayName:"SuccessNotification__ZapIcon"})(["\n  color: ",";\n  flex-shrink: 0;\n"],M("yellow")),Xl=e=>{const{createdAt:t,content:n,sideContent:a,jitCompiled:o,isMinimized:i}=e;return r.createElement(ql,{isMinimized:i},r.createElement(Ql,{createdAt:t}),o?r.createElement(Xe,{icon:r.createElement(Kl,{size:"16px"}),placement:"top",tooltip:"JIT Compiled"}):r.createElement(Yl,{size:"18px"}),r.createElement(Gl,null,n),r.createElement($l,null,a))};var Jl=n(2128);const es=(0,s.ZP)(Jl.e).withConfig({displayName:"ErrorNotification__CloseOutlineIcon"})(["\n  color: ",";\n  flex-shrink: 0;\n"],M("red")),ts=e=>{const{createdAt:t,content:n,sideContent:a,isMinimized:o}=e;return r.createElement(ql,{isMinimized:o},r.createElement(Ql,{createdAt:t}),r.createElement(es,{size:"18px"}),r.createElement(Gl,null,n),r.createElement($l,null,a))},ns=e=>{const{createdAt:t,content:n,sideContent:a,isMinimized:o}=e;return r.createElement(ql,{isMinimized:o},r.createElement(Ql,{createdAt:t}),r.createElement(Gl,null,n),r.createElement($l,null,a))};var rs=n(55122);const as=(0,s.ZP)(rs.E).withConfig({displayName:"NoticeNotification__InfoOutlineIcon"})(["\n  color: ",";\n  flex-shrink: 0;\n"],M("yellow")),os=e=>{const{createdAt:t,content:n,sideContent:a,isMinimized:o}=e;return r.createElement(ql,{isMinimized:o},r.createElement(Ql,{createdAt:t}),r.createElement(as,{size:"18px"}),r.createElement(Gl,null,n),r.createElement($l,null,a))},is=e=>{const{type:t}=e;return t===Zt.SUCCESS?r.createElement(Xl,e):t===Zt.ERROR?r.createElement(ts,e):t==Zt.NOTICE?r.createElement(os,e):r.createElement(ns,e)};function ls(){return ls=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ls.apply(this,arguments)}const ss=(0,s.ZP)(lt).withConfig({displayName:"Notifications__Wrapper"})(["\n  flex: ",";\n  overflow: auto;\n  max-height: 35rem;\n  background: ",";\n"],(e=>e.minimized?"initial":"1"),(({theme:e})=>e.color.backgroundLighter)),cs=(0,s.ZP)(Oe).withConfig({displayName:"Notifications__Menu"})(['\n  justify-content: space-between;\n  overflow: hidden;\n  border: 0;\n\n  ::before {\n    content: "";\n    position: absolute;\n    top: 0;\n    cursor: text;\n    left: 0;\n    width: 100%;\n    height: 2px;\n    background: ',";\n  }\n"],(({theme:e})=>e.color.backgroundDarker)),ds=(0,s.ZP)(it).withConfig({displayName:"Notifications__Content"})(["\n  overflow: ",";\n  overflow-x: hidden;\n  height: ",";\n"],(e=>e.minimized?"hidden":"auto"),(e=>e.minimized?"4rem":"100%")),us=(0,s.ZP)(Pe).withConfig({displayName:"Notifications__Header"})(["\n  display: flex;\n  align-items: center;\n"]),ms=s.ZP.div.withConfig({displayName:"Notifications__LatestNotification"})(["\n  margin-left: 1rem;\n  flex: 1;\n  height: 100%;\n  display: flex;\n  cursor: text;\n  align-items: stretch;\n  width: calc(100% - 10rem);\n"]),ps=(0,s.ZP)(zl.T).withConfig({displayName:"Notifications__TerminalBoxIcon"})(["\n  margin-right: 1rem;\n"]),gs=s.ZP.div.withConfig({displayName:"Notifications__ClearAllNotifications"})(["\n  display: flex;\n  width: 100%;\n  height: 4.5rem;\n  justify-content: center;\n  padding: 0.5rem 1rem;\n  margin-top: auto;\n  align-items: center;\n  flex-shrink: 0;\n"]),fs=()=>{const e=(0,o.v9)(Cn.query.getNotifications),{sm:t}=Ye(),[n,a]=(0,r.useState)(!0),i=(0,r.useRef)(null),l=(0,o.I0)(),s=()=>{i.current?.scrollTo({top:i.current?.scrollHeight})},c=(0,r.useCallback)((()=>{a(!n)}),[n]),d=(0,r.useCallback)((()=>{l(jt())}),[l]);(0,r.useLayoutEffect)((()=>{e.length>0&&s()}),[e]),(0,r.useLayoutEffect)((()=>{s()}),[n]),(0,r.useEffect)((()=>{t&&a(!0)}),[t]);const u=e[e.length-1];return r.createElement(ss,{minimized:n,"data-hook":"notifications-wrapper"},r.createElement(cs,null,r.createElement(us,{color:"foreground"},r.createElement(ps,{size:"18px"}),"Log"),r.createElement(ms,{"data-hook":"notifications-collapsed"},n&&u&&r.createElement(is,ls({isMinimized:!0},u))),r.createElement(Dn.z,{skin:n?"secondary":"transparent",onClick:c},n?r.createElement(Zl.f,{size:"18px"}):r.createElement(Ul.I,{size:"18px"}))),!n&&r.createElement(ds,{minimized:n,ref:i,"data-hook":"notifications-expanded"},e.filter((e=>e.type!==Zt.LOADING)).map((e=>r.createElement(is,ls({isMinimized:!1,key:e.createdAt?e.createdAt.getTime():0},e)))),!n&&r.createElement(gs,null,r.createElement(Dn.z,{skin:"secondary",disabled:0===e.length,onClick:d},"Clear all"))))};function hs(){return hs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},hs.apply(this,arguments)}const bs=(0,s.ZP)(lt).withConfig({displayName:"Editor__EditorPaneWrapper"})(["\n  height: 100%;\n  overflow: hidden;\n"]),Es=({innerRef:e,...t})=>{const{activeBuffer:n,addBuffer:a,setActiveBuffer:o}=ma();return(0,r.useEffect)((()=>{new URLSearchParams(window.location.search).get("query")&&n.metricsViewState&&a({label:"Query"}).then(o)}),[]),r.createElement(bs,hs({ref:e},t),r.createElement($o,null),n.editorViewState&&r.createElement(_o,null),n.metricsViewState&&r.createElement(Vl,null),n.editorViewState&&r.createElement(fs,null))},ys=(e,t)=>r.createElement(Es,hs({},e,{innerRef:t})),ws=(0,r.forwardRef)(ys);var Cs=n(23945),vs=n.n(Cs),_s=n(23401),xs=n(44609),Ss=n(14544),ks=n(22373),Ts=n(9449),Ns=n(4222);function Os(e,t,n){const r={gridID:"qdb-grid",minColumnWidth:60,rowHeight:30,divCacheSize:64,viewportHeight:400,yMaxThreshold:1e7,maxRowsToAnalyze:100,minVpHeight:120,minDivHeight:160,scrollerGirth:10,dragHandleWidth:20,dataPageSize:1e3,layoutStoreTimeout:1e3},a="qg-c-active",o=0,i=3,l=4,s=(n||r.gridID)+".columnLayout",c=e,d=t;let u,m,g,f,h,b,E,y,w,C,v,_,x,S,k,T,N,O,R,I,A,L=0,P=[],D=[],M=[],B=0,F=-1,H=-1,V=[],z=-1,Z=!1;const U=r.divCacheSize,q=U-1,G=r.dataPageSize,$=Math.floor(G/3),j=2*$;let W,Q,Y,K,X=[],J=0,ee=10,te=0,ne=0,re=0,ae=r.viewportHeight;const oe=r.rowHeight;let ie,le,se,ce,de,ue,me,pe,ge,fe,he=[],be=-1,Ee=-1;const ye={colLo:0,colHi:0,nextVisColumnLo:0,render:!1},we=r.scrollerGirth;let Ce,ve,_e,xe,Se,ke,Te,Ne,Oe,Re,Ie,Ae=0,Le=!0,Pe=!1,De={};function Me(e){return D[M[e]]}function Be(){const e=Ee;let t=0;L>0&&(t=L,L<=e&&(L=0,y.innerHTML="",sn()));const n=je(e);for(let t=e;t>0;t--)I[t]=I[t-1]+n;I[0]=0;const r=e+1;for(let t=e;t>=0;t--){const e=g.childNodes[t].querySelector(".qg-col-resize-hysteresis");e&&(e.columnIndex=(e.columnIndex+1)%r)}-1!==F&&F<r&&(F=(F+1)%r),g.childNodes[0].before(g.childNodes[e]);const a=M[e];for(let t=e;t>0;t--)M[t]=M[t-1];M[0]=a,t>0&&(t<=e?dn(t+1):(y.childNodes[0].before(y.childNodes[e]),_t(P,0,Math.min(L,B),J))),te=0,_t(he,0,ee,0),u.scrollLeft>0?u.scrollLeft=0:Mt(),St(e),function(e){const t=at();t.columnPositions=M,t.timestampIndex=F;for(let n=0;n<e;n++)t.deviants[Me(n).name]=je(n);rt()}(r)}function Fe(e,t,n,r){if(e.rowIndex!==t){const a=V[Math.floor(t/G)];if(a){const o=a[t%G];if(o){e.style.display="flex";for(let t=n;t<r;t++)wt(D[t],e.childNodes[t%ee],o[M[t]]);e.rowIndex=t}else e.style.display="none",e.rowIndex=-1}else{for(let t=n;t<r;t++)e.childNodes[t%ee].innerHTML="";e.rowIndex=-1}e.style.top=t*oe-me+"px"}}function He(e){u.scrollTop=e,b.scrollTop=e}function Ve(){const e=Math.max(J,L),t=Math.min(e+ee,B);_t(he,e,t,J),Mt()}function ze(e){return null===V[e]||void 0===V[e]||0===V[e].length}function Ze(e,t){e.style.minWidth=t+"px",e.style.maxWidth=t+"px"}function Ue(e,t){K&&clearTimeout(K),K=setTimeout((function(){!function(e,t){let n,r,a;if(function(){for(let e=0,t=V.length;e<t;e++)(e<W||e>Q)&&V[e]&&delete V[e]}(),e!==t&&ze(e)&&ze(t))n=e*G,r=n+G*(t-e+1),a=function(n){V[e]=n.dataset.splice(0,G),V[t]=n.dataset,Ve()};else if(!ze(e)||ze(t)&&e!==t){if(ze(e)&&e!==t||!ze(t))return void Ve();n=t*G,r=n+G,a=function(e){V[t]=e.dataset,Ve()}}else n=e*G,r=n+G,a=function(t){V[e]=t.dataset,Ve()};d&&d(Y,n+1,r,a)}(e,t)}),75)}function qe(){return{t:Math.max(0,Math.floor(se/oe)),b:Math.min(le/oe,Math.ceil((se+ae)/oe))}}function Ge(e){const t=qe();let n=t.t,r=t.b;0!==e&&function(e,t,n){if(t!=t||n!=n)return;let r,a,o,i;if(r=Math.floor(t/G),o=Math.floor(n/G),e>0){if(i=n%G,r>=W&&o<Q)return;if(o===Q)return void(i>j&&(Q=o+1,W=o,Ue(o,o+1)));r<o?(Ue(r,o),W=r,Q=o):i>j?(Ue(o,o+1),W=o,Q=o+1):(Q=r,W=r,Ue(r,r))}else{if(a=t%G,r>W&&o<=Q)return;if(r===W)return void(a<$&&W>0&&(W=Math.max(0,r-1),Q=r,Ue(r-1,r)));r<o?(Ue(r,o),W=r,Q=o):a<$&&r>0?(Ue(r-1,r),W=Math.max(0,r-1),Q=r):(W=r,Q=r,Ue(r,r))}}(e,n,r),0===n?r=U:r>ie-2&&(n=Math.max(0,r-U));for(let e=n;e<r;e++){const t=he[e&q];t&&(Fe(t,e,Math.min(B,Math.max(J,L)),ee),Fe(P[e&q],e,0,Math.min(L,B)))}}function $e(e){return I[e]}function je(e){return I[e+1]-I[e]}function We(e){const t=Me(e);if(t)switch(t.type){case"STRING":case"SYMBOL":case"VARCHAR":case"ARRAY":return!0;default:return!1}}function Qe(e){xe||un("header.click",{columnName:e.currentTarget.getAttribute("data-column-name")})}function Ye(){Te&&(clearTimeout(Te),Te=void 0)}function Ke(e,t){return ve+e-(t<L?0:u.scrollLeft)+"px"}function Xe(e){if(0===e.target.childNodes.length){e.preventDefault();const t=document.createElement("div");t.baguette=!0,ht(t,"qg-col-resize-hysteresis-enter"),e.target.append(t)}}function Je(e){e.preventDefault(),e.target.childNodes.length>0&&e.target.childNodes[0].remove()}function et(e){e.preventDefault(),Ye();const t=e.target.baguette?e.target.parentElement:e.target;e.target.childNodes.length>0&&e.target.childNodes[0].remove(),xe=t.columnIndex,Se=$e(xe),ke=je(xe);const n=t.parentElement;ve=n.offsetLeft+n.getBoundingClientRect().width,_e=e.clientX,document.onmousemove=tt,document.onmouseup=ot,R.style.top=0,R.style.left=Ke(0,xe),R.style.height=c.getBoundingClientRect().height-(Nt()?we:0)+"px",R.style.visibility="visible",Ae=ke}function tt(e){e.preventDefault();const t=e.clientX-_e,n=ke+t;n>r.minColumnWidth&&(R.style.left=Ke(t,xe),Ae=n)}function nt(){window.localStorage.setItem(s,JSON.stringify(De))}function rt(){Re&&clearTimeout(Re),Re=setTimeout(nt,r.layoutStoreTimeout)}function at(){let e=De[Oe];return void 0===e&&(e={key:Ne,deviants:{}},De[Oe]=e),e}function ot(e){var t,n;e.preventDefault(),document.onmousemove=null,document.onmouseup=null,function(e,t){!function(e,t){let n=I[e]+t;for(let t=e+1;t<=B;t++){const e=je(t);I[t]=n,n+=e}z=I[B]}(e,t),e<L&&(Ze(y.childNodes[e],t),_t(P,0,Math.min(L,B),J),Ht(),an()),Ze(g.childNodes[e],t),Ft(),_t(he,J,J+ee,J),Mt()}(xe,Ae),R.style.visibility="hidden",R.style.left=0,Te=setTimeout((()=>{xe=void 0}),500),t=Me(xe).name,n=Ae,at().deviants[t]=n,rt()}function it(e){return Math.max(r.minColumnWidth,Math.ceil(8*e*1.2))}function lt(e){k||(e.preventDefault(),_.style.left=h+"px",_.style.display="block",_.style.height=ae-(Nt()?we:0)+"px",x.style.height=_.style.height,N=e.offsetY-S.getBoundingClientRect().height/2,k=e.pageY,S.style.top=N+"px")}function st(e){const t=e.relatedTarget;t===S||t===_||t===v||T||(e.preventDefault(),_.style.display="none",k=void 0)}function ct(e){const t=e.pageX-T,n=h+t;_.style.left=n+"px",C=L;let r=n;for(let e=0;e<B;e++){const t=je(e);if(r-=t,r<0){C=-r/t<.5?e+1:e;break}}C>L&&u.scrollLeft>0&&(C=L),x.style.left=$e(C)+"px",x.style.display="block",mt(e)}function dt(){document.onmousemove=void 0,document.onmouseup=void 0,_.style.display="none",x.style.display="none",k=void 0,T=void 0,c.style.cursor=null,S.style.cursor=null,O.style.cursor=null,dn(C)}function ut(e){e.preventDefault(),T=e.pageX,document.onmousemove=ct,document.onmouseup=dt,c.style.cursor="grabbing",S.style.cursor="grabbing",O.style.cursor="grabbing"}function mt(e){if(k){e.preventDefault();const t=e.pageY-k;S.style.top=Math.min(ae-S.getBoundingClientRect().height-(Nt()?we:0),Math.max(0,N+t))+"px"}}function pt(e,t,n,r){for(let r=t,a=t+n;r<a;r++){const t=Me(r),n=document.createElement("div");ht(n,"qg-header"),Ze(n,je(r)),n.setAttribute("data-column-name",t.name),We(r)&&ht(n,"qg-header-l");const a=document.createElement("span");if(ht(a,"qg-header-type"),"ARRAY"!==t.type)a.innerHTML=t.type.toLowerCase();else if(t.dim>2)a.innerHTML=t.type.toUpperCase()+"("+t.elemType.toUpperCase()+","+t.dim+")";else{let e=t.elemType.toLowerCase()+"[]";t.dim>1&&(e+="[]"),a.innerHTML=e}const o=document.createElement("span");ht(o,"qg-header-name"),o.innerHTML=t.name;const i=document.createElement("div");ht(i,"qg-col-resize-hysteresis"),i.columnIndex=r,i.onmousedown=et,i.onmouseenter=Xe,i.onmouseleave=Je;const l=document.createElement("span");ht(l,"qg-header-border"),n.append(i,l),n.append(o,a),n.onclick=Qe,e.append(n)}if(r){const t=document.createElement("div");return t.className="qg-header qg-stub",t.style.width=we+"px",e.append(t),t}return null}function gt(){I=[],z=0;for(let e=0;e<B;e++){const t=Me(e),n=it(t.name.length+t.type.length);I.push(z),z+=n}I.push(z)}function ft(e,t){e.classList.remove(t)}function ht(e,t){e&&e.classList.add(t)}function bt(e){ft(e,a)}function Et(){ge&&ge.parentElement&&ge.parentElement.rowIndex===be?ht(ge,a):ge&&ft(ge,a)}function yt(e){!e||ge===e&&e.classList.contains(a)||(ge&&bt(ge),ge=e,Ee=e.columnIndex,Et())}function wt(e,t,n){null!==n?(t.innerHTML=function(e,t){const n=Array.isArray(t),r=["FLOAT","DOUBLE"];return(n?r.includes(e.elemType):r.includes(e.type))?n?"ARRAY"+JSON.stringify(t,((e,t)=>Number.isInteger(t)?t.toString()+".0":t)).replace(/"/g,""):Number.isInteger(t)?t.toString()+".0":t.toString():n?JSON.stringify(t):(e=>{const t={"<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#039;"};return e.replace(/[<>"']/g,(e=>t[e]))})(t.toString())}(e,n),t.classList.remove("qg-null"),"ARRAY"===e.type&&e.dim>1&&t.classList.add("qg-arr-multidim")):(t.innerHTML="null",t.classList.add("qg-null"))}function Ct(e,t,n){const r=e.childNodes[n%ee];Kt(r,n),wt(D[n],r,t[M[n]])}function vt(e){return Math.max(L,e)}function _t(e,t,n,r){if(e.length>0&&B>0&&t<n){ye.colLo=t,ye.colHi=n,ye.nextVisColumnLo=r,ye.render=!1;const a=qe();let o=a.t,i=a.b;o-=o&q,i=i-(i&q)+U;for(let r=o;r<i;r++){const a=e[r&q];a.style.width=z+"px";const o=Math.floor(r/G),i=r%G;let l,s;if(o<V.length&&(l=V[o])&&i<l.length&&(s=l[i]))for(let e=t;e<n;e++)Ct(a,s,e);else ye.render=!0}J=Math.max(0,Math.min(r,B-ee)),Jt()}}function xt(e){const t=u.scrollLeft;u.scrollLeft=e,g.scrollLeft=e,t===u.scrollLeft&&Et()}function St(e){if(-1!==e){Ee=e;const t=Ee<L?w.childNodes[Ee]:pe.childNodes[Ee%ee];t.columnIndex=Ee,yt(t)}}function kt(e){e===i&&J>0&&B>ee?_t(he,L,ee,0):e===l&&J+ee<B&&_t(he,vt(B-ee),B,B-ee);const t=$e(Ee),n=je(Ee);if(e!==o&&(Ee>=L||e===i)){const e=Math.max(0,t-h);if(e<u.scrollLeft)xt(e);else if(e>u.scrollLeft){const e=t+n;e>u.scrollLeft+u.clientWidth&&xt(e-u.clientWidth)}}St(Ee)}function Tt(e){if(be>0){be=Math.max(0,be-e),It(),Dt(),Rt(),kt(o);const t=be*oe-me-5;t<u.scrollTop&&He(Math.max(0,t))}}function Nt(){return u.scrollWidth>re}function Ot(e){const t=e[be&q];return t&&t.rowIndex===be?t.className="qg-r qg-r-active":t&&(t.className="qg-r"),t}function Rt(){pe=Ot(he),w=Ot(P)}function It(){pe&&(pe.className="qg-r"),w&&(w.className="qg-r")}function At(e){if(be>-1&&be<ie-1){be=Math.min(ie-1,be+e),It(),Dt(),Rt(),kt(o);const t=Math.min(be*oe+oe-me,u.scrollHeight)-ae,n=Nt()?we:0;t>u.scrollTop&&He(t+n)}}function Lt(){const e=te+ne,t=te,n=u.scrollLeft,r=u.getBoundingClientRect().width;if(I)if(n+r>e){let t=-1,r=te;for(let e=J,a=ee+J;e<a;e++)if(r+=je(e),r>n){t=e;break}if(t>J)_t(he,vt(ee+J),Math.min(ee+t,B),t);else if(-1===t){let t=e,r=ee+J;for(;t<n;)t+=je(++r);_t(he,vt(r),Math.min(ee+r,B),r)}}else if(t>n){let e=J,t=te;for(;t>n&&e>0;)t-=je(--e);const r=Math.min(ee,J-e);_t(he,vt(e),Math.min(r+e,B),e)}}function Pt(){Le||(ht(c,"qg-hover"),Le=!0)}function Dt(){Le&&(ft(c,"qg-hover"),Le=!1)}function Mt(e){const t=u.scrollLeft,n=u.scrollTop;if(g.scrollLeft!==t&&(g.scrollLeft=t),L>0&&(t>0?ht(f,"qg-panel-scrolled-left"):ft(f,"qg-panel-scrolled-left")),n>0?(ht(g,"qg-panel-scrolled-top"),ht(y,"qg-panel-scrolled-top")):(ft(g,"qg-panel-scrolled-top"),ft(y,"qg-panel-scrolled-top")),Lt(),n!==de||!e){const e=se;n>=ce-ae?(se=Math.max(0,le-ae),de=n,me=Math.max(0,se-de),At(ie-be)):(0===n&&de>0?(se=0,me=0,Tt(be)):se+=n-de,de=n),Ge(se-e)}Vt(),Et()}function Bt(){const e=u.getBoundingClientRect().width;if(Z=0===e,!Z)if(z<e)ee=B,J=0,Jt();else{let t=0,n=0,r=0,a=0;for(;n<B;){const o=je(n);r+o>e?(a<n-t+1&&(a=n-t+1),r-=je(t),t++):(r+=o,n++)}a=Math.max(a,n-t+1),ee=Math.min(a,B);const o=J+ee-B;o>0&&J>=o&&(J-=o,Jt())}}function Ft(){Ce&&(Nt()&&Math.abs(u.scrollHeight-u.getBoundingClientRect().height)>.8?ft(Ce,"qg-stub-transparent"):ht(Ce,"qg-stub-transparent"));const e=ee;Bt(),e<ee?function(e){for(let t=0,n=he.length;t<n;t++){const n=he[t];for(let t=0;t<e;t++){const e=document.createElement("div");e.className="qg-c",n.append(e)}for(let e=0;e<ee;e++)Kt(n.childNodes[e],J+e)}_t(he,vt(J),J+ee,J)}(ee-e):e>ee&&function(e){for(let t=0,n=he.length;t<n;t++){const n=he[t];for(let t=ee;t<e;t++)n.childNodes[ee].remove()}_t(he,vt(J),J+ee,J)}(e)}function Ht(){let e=0;const t=Math.min(L,B);for(let n=0;n<t;n++)e+=je(n);h=e}function Vt(){const e=u.clientHeight+g.clientHeight;f.style.height=e+"px",b.style.height=u.clientHeight+"px",b.scrollTop=u.scrollTop}function zt(){u.scrollTop=b.scrollTop}function Zt(){if(0===V.length||0===V[W].length)return Lt(),void Ge(0);if(Pe&&(Pe=!1,function(){en();for(let e=0;e<L;e++)Ze(y.childNodes[e],je(e));for(let e=0,t=B-L;e<t;e++)Ze(g.childNodes[e],je(e+L))}()),Z&&(rn(),on()),Vt(),"none"!==c.style.display){ae=Math.max(u.getBoundingClientRect().height,r.minVpHeight),fe=Math.floor(ae/oe);const e=u.getBoundingClientRect().width;re!==e&&(re=e,Ft()),Mt()}}function Ut(){It(),this.focus(),be=this.parentElement.rowIndex,Rt(),yt(this)}function qt(e){e.preventDefault();const t=e.target;if(t&&!T){const e=t.parentElement.rowIndex&q;ht(he[e],"qg-r-hover"),ht(P[e],"qg-r-hover")}}function Gt(e){e.preventDefault();const t=e.target;if(t&&!T){const e=t.parentElement.rowIndex&q;ft(he[e],"qg-r-hover"),ft(P[e],"qg-r-hover")}}function $t(){ge&&bt(ge)}function jt(e){delete X["which"in e?e.which:e.keyCode]}function Wt(){De[Oe]=void 0,rt(),cn(0),y.innerHTML="",sn(),nn(),F=H,g.innerHTML="",gt(),en(),h=0,Ce=pt(g,0,B,!0),Ft(),Ht(),an(),_t(he,vt(J),J+ee,J),_t(P,0,L,J),u.scrollLeft=0,tn()}function Qt(){return X[17]||X[91]||X[224]}function Yt(e){const t="which"in e?e.which:e.keyCode;let n=!0;switch(t){case 33:Tt(fe);break;case 38:X[91]?Tt(be):Tt(1);break;case 40:X[91]?At(ie-be):At(1);break;case 34:At(fe);break;case 39:Ee>-1&&Ee<B-1&&(Dt(),Ee++,kt(2));break;case 37:Ee>0&&(Dt(),Ee--,kt(1));break;case 35:X[17]?At(ie-be):Ee>-1&&Ee!==B-1&&(Dt(),Ee=B-1,kt(l));break;case 36:X[17]?Tt(be):(Ee>0||u.scrollLeft>0)&&(Dt(),Ee=0,kt(i));break;case 113:$t(),un("yield.focus");break;case 67:case 45:Qt()&&function(){if(ge){let e;if(Ie&&clearTimeout(Ie),ht(ge,"qg-c-active-pulse"),ge.classList.contains("qg-arr-multidim"))try{e="ARRAY"+JSON.stringify(JSON.parse(ge.innerHTML.slice(5)),((e,t)=>Number.isInteger(t)?t.toString()+".0":t),2).replace(/"/g,"")}catch(t){e=ge.innerHTML}else e=ge.innerHTML;p(e).then(void 0),Ie=setTimeout((()=>{ft(ge,"qg-c-active-pulse")}),1e3)}}();break;case 66:Qt()&&Wt();break;case 191:Be();break;default:X[t]=!0,n=!1}n&&e.preventDefault()}function Kt(e,t){const n=$e(t);e.style.left=n+"px",e.style.width=$e(t+1)-n+"px",e.style.height=r.rowHeight+"px",e.style.textAlign=We(t)?"left":"right",e.onclick=Ut,e.onmouseenter=qt,e.onmouseleave=Gt,e.classList.contains("qg-timestamp")&&ft(e,"qg-timestamp"),e.columnIndex===B-1&&ft(e,"qg-last-col"),e.columnIndex=t,t===F&&ht(e,"qg-timestamp"),e.columnIndex===B-1&&ht(e,"qg-last-col")}function Xt(e,t,n,r){for(let a=0;a<U;a++){const o=document.createElement("div");o.className="qg-r",o.tabIndex=a+a;for(let e=0;e<n;e++){const t=document.createElement("div");t.className="qg-c",Kt(t,e),o.append(t),0===a&&0===e&&yt(t)}o.style.top="-100",o.style.height=oe.toString()+"px",o.style.width=r+"px",t.push(o),e.append(o)}}function Jt(){if(Z)return;let e=0;for(let t=0;t<J;t++)e+=je(t);let t=0;for(let e=J,n=J+ee;e<n;e++)t+=je(e);te=e,ne=t}function en(){const e=.8*u.getBoundingClientRect().width;if(Pe=e<.1,!Pe&&V&&V.length>0){const t=De[Oe],n=void 0!==t?t.deviants:void 0,r=V[0],a=r.length;cn(void 0!==t?t.freezeLeft:0);let o=0;for(let t=0;t<B;t++){let i;if(n&&(i=n[Me(t).name]),void 0===i){i=je(t);for(let n=0;n<a;n++){I[t]=o;const a=r[n][t];let l;l=null===a?"null":"ARRAY"===Me(t).type?"ARRAY"+JSON.stringify(a):a.toString(),i=Math.min(e,Math.max(i,it(l.length)))}}else I[t]=o;o+=i}I[B]=o,z=o}}function tn(){St(0),pe.focus()}function nn(){M=[];for(let e=0;e<B;e++)M.push(e)}function rn(){Bt(),Jt()}function an(){f.style.width=h+"px"}function on(){if(Z)return;const e=De[Oe];e&&e.columnPositions&&(M=e.columnPositions,F=e.timestampIndex),en(),Ht(),Ce=pt(g,0,B,!0),L>0?(pt(y,0,Math.min(L,B),!1),ln()):sn(),an(),Xt(m,he,ee,z),Xt(E,P,Math.min(L,B),h),function(){ie=A,le=ie*oe,ce=le<r.yMaxThreshold?le:r.yMaxThreshold,ue=le/ce;const e=(0===ce?1:ce)+"px";m.style.height=e,E.style.height=e}(),He(0),Zt(),be=0,pe=he[be],w=P[be],tn(),Rt()}function ln(){f.style.display="block",O.style.display="none",b.scrollTop=u.scrollTop}function sn(){f.style.display="none",O.style.display="block";for(let e=0,t=P.length;e<t;e++)P[e].innerHTML=""}function cn(e){L=void 0!==e?e:0,un("freeze.state",{freezeLeft:L})}function dn(e){if(void 0!==e&&e!==L){if(e<L){for(let t=0,n=P.length;t<n;t++){const n=P[t];for(let t=e;t<L;t++)n.childNodes[e].remove()}for(let t=e;t<L;t++)y.childNodes[e].remove();0===e&&sn()}else{for(let t=0,n=P.length;t<n;t++){const n=P[t];for(let t=L;t<e;t++){const e=document.createElement("div");e.className="qg-c",Kt(e,t),n.append(e)}}pt(y,L,e-L,!1),ln()}cn(e),at().freezeLeft=L,rt(),_t(P,0,Math.min(L,B),J),Ht(),an(),_t(he,vt(J),J+ee,J)}}function un(e,t){c.dispatchEvent(new CustomEvent(e,{detail:t}))}return function(){g=document.createElement("div"),ht(g,"qg-header-row"),u=document.createElement("div"),u.onscroll=Mt,ht(u,"qg-viewport"),m=document.createElement("div"),m.className="qg-canvas",m.onkeydown=Yt,m.onkeyup=jt,document.addEventListener("click",(e=>{c.contains(e.target)||($t(),It(),be=-1,Ee=-1)})),R=document.createElement("div"),R.className="qg-col-resize-ghost",u.append(m,R),f=document.createElement("div"),ht(f,"qg-panel-left"),y=document.createElement("div"),ht(y,"qg-header-left-row"),b=document.createElement("div"),b.onscroll=zt,ht(b,"qg-viewport-left"),E=document.createElement("div"),E.className="qg-canvas",E.onkeydown=Yt,E.onkeyup=jt,b.append(E),v=document.createElement("div"),ht(v,"qg-panel-left-hysteresis"),v.onmouseenter=lt,v.onmouseleave=st,v.onmousemove=mt,v.onmousedown=ut,_=document.createElement("div"),ht(_,"qg-panel-left-ghost"),_.onmousemove=mt,_.onmouseleave=st,S=document.createElement("div"),ht(S,"qg-panel-left-ghost-handle"),S.onmouseleave=st,S.onmousedown=ut,_.append(S),f.append(y,b,v),x=document.createElement("div"),ht(x,"qg-panel-left-snap-ghost"),O=document.createElement("div"),ht(O,"qg-panel-left-initial-hysteresis"),O.onmouseenter=lt,O.onmouseleave=st,O.onmousemove=mt,O.onmousedown=ut,c.append(g,u,f,_,x,O),c.onmousemove=Pt,c.style.position="relative";const e=window.localStorage.getItem(s);e&&(De=JSON.parse(e)),new ResizeObserver((function(){c.getBoundingClientRect().height>0&&c.getBoundingClientRect().width>0&&Zt()})).observe(c)}(),Zt(),{clearCustomLayout:function(){Wt()},shuffleFocusedColumnToFront:function(){Be()},toggleFreezeLeft:function(){dn(L>0?0:1),St(Ee)},getResultAsMarkdown:function(){return"QUERY PLAN\nstring"===g.innerText?function(){let e=["```"];return he.forEach((t=>{if("flex"===t.style.display){const n=t.querySelector(".qg-c");n&&e.push(n.textContent)}})),e.push("```"),e.join("\n")}():function(){let e=g.innerText.split(/\n/).filter(((e,t)=>t%2==0)),t=Array(e.length).fill(0);for(const[n,r]of e.entries())t[n]=Math.max(t[n],r.length);for(const e of he){let n=e.innerText.split(/\n/);for(const[e,r]of n.entries())t[e]=Math.max(t[e],r.length)}let n=["|"];for(const[r,a]of e.entries())n.push(a.padEnd(t[r])),n.push("|");let r=n.join(" "),a=["|"];for(const[n,r]of e.entries())a.push("-".repeat(t[n])),a.push("|");let o=a.join(" "),i=[];for(const e of he){let n=e.innerText.split(/\n/);if(1===n.length&&""===n[0])continue;let r=["|"];for(const[e,a]of n.entries())r.push(a.padEnd(t[e])),r.push("|");i.push(r.join(" "))}return[r,o,i.join("\n")].join("\n")}()},show:function(){c.style.display="flex",Zt()},hide:function(){c.style.display="none"},focus:function(){ge&&pe&&(yt(ge),pe.focus())},setData:function(e){!function(e){setTimeout((()=>{(function(e){de=0,se=0,me=0,ie=0,g.innerHTML="",y.innerHTML="",m.innerHTML="",E.innerHTML="",he=[],P=[],V=[],Y=null,W=0,Q=0,X=[],B=0,A=0,pe=null,w=null,ge=null,Ee=-1,J=0,ee=10,re=0,F=-1,H=-1,be=-1,Pe=!1,xe=void 0,Ye(),Ne=void 0,Oe=void 0,h=0,Z=!1,cn(0),Pt(),Y=e.query,V.push(e.dataset),D=e.columns,B=D.length,nn(),H=e.timestamp,F=H,A=e.count,gt(),rn()})(e),function(e){const t=[];for(let e=0;e<B;e++){const n=Me(e);t.push({name:n.name,type:n.type})}Ne=JSON.stringify(t),Oe=(e=>{let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t&=t;return new Uint32Array([t])[0].toString(36)})(Ne),e()}(on)}),0)}(e)},getSQL:function(){return Y},render:function(){Zt()},addEventListener:function(e,t){!function(e,t,n){{const n=e=>{t.call(c,e)};c.addEventListener(e,n)}}(e,t)}}}n(60985),n(91499),n(21287),n(50591);var Rs=n(99400),Is=n(91074),As=n(37579),Ls=n(28670),Ps=n.n(Ls);const Ds="#eee",Ms=function(){return{axisLine:{lineStyle:{color:Ds}},axisTick:{lineStyle:{color:Ds}},axisLabel:{textStyle:{color:Ds}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:Ds}}}},Bs=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],Fs={color:Bs,backgroundColor:"#282a36",tooltip:{axisPointer:{lineStyle:{color:Ds},crossStyle:{color:Ds}}},legend:{textStyle:{color:Ds}},textStyle:{color:Ds},title:{textStyle:{color:Ds}},toolbox:{iconStyle:{normal:{borderColor:Ds}}},dataZoom:{textStyle:{color:Ds}},timeline:{lineStyle:{color:Ds},itemStyle:{normal:{color:Bs[1]}},label:{normal:{textStyle:{color:Ds}}},controlStyle:{normal:{color:Ds,borderColor:Ds}}},timeAxis:Ms(),logAxis:Ms(),valueAxis:Ms(),categoryAxis:Ms(),line:{symbol:"circle"},graph:{color:Bs},gauge:{title:{textStyle:{color:Ds}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}},textStyle:{fontFamily:'"Open Sans", -apple-system, BlinkMacSystemFont, Helvetica, Roboto, sans-serif'}};var Hs=n(94349);Rs.D$([Is.N,As.N]);const Vs=s.ZP.div.withConfig({displayName:"Result__Root"})(["\n  display: flex;\n  flex: 1;\n  width: 100%;\n"]),zs=(0,s.ZP)(lt).withConfig({displayName:"Result__Wrapper"})(["\n  overflow: hidden;\n"]),Zs=(0,s.ZP)(it).withConfig({displayName:"Result__Content"})(["\n  flex: 1 1 0;\n  color: ",";\n\n  *::selection {\n    background: ",";\n    color: ",";\n  }\n"],M("foreground"),M("red"),M("foreground")),Us=s.ZP.div.withConfig({displayName:"Result__Actions"})(["\n  display: grid;\n  grid-auto-flow: column;\n  grid-auto-columns: max-content;\n  gap: 0;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 0 1rem;\n  width: 100%;\n  height: 4.5rem;\n  border-bottom: 2px solid ",";\n  background: ",";\n"],(({theme:e})=>e.color.backgroundDarker),(({theme:e})=>e.color.backgroundLighter)),qs=(0,s.ZP)(ks.L).withConfig({displayName:"Result__TableFreezeColumnIcon"})(["\n  transform: scaleX(-1);\n"]),Gs=(0,s.ZP)(Pe).withConfig({displayName:"Result__RowCount"})(["\n  margin-right: 1rem;\n"]),$s=({viewMode:e})=>{const{quest:t}=(0,r.useContext)(qr),[n,a]=(0,r.useState)(),i=(0,o.v9)(Cn.query.getResult),l=(0,o.v9)(Cn.console.getActiveSidebar),s=(0,r.useRef)(),[c,d]=(0,r.useState)(0),u=(0,o.I0)();(0,r.useEffect)((()=>{const e=Os(document.getElementById("grid"),(async function(e,n,a,o){try{const r=await t.queryRaw(e,{limit:`${n},${a}`,nm:!0});r.type===T.DQL&&o(r)}catch(t){u(Qt()),u($t({content:r.createElement(Pe,{color:"red"},t.error),sideContent:r.createElement(yo,{query:e}),type:Zt.ERROR}))}}));s.current=e,function(e,t,n){let r=e;const a=vs()("#_qvis_frm_draw");let o,i,l,s,c,d,u;const m=new Set,p={title:{},tooltip:{},legend:{data:["Series"]},xAxis:{data:[]},yAxis:{},series:[{name:"Y-axis",type:"bar",data:[]}]};let g,f,h=!1;const b=new(Ps())({select:"#_qvis_frm_chart_type"}),E=new(Ps())({select:"#_qvis_frm_axis_x"}),y=new(Ps())({select:"#_qvis_frm_axis_y"});function w(){i.resize()}function C(){a.html('<i class="icon icon-play"></i><span>Draw</span>'),a.removeClass("js-chart-cancel").addClass("js-chart-draw")}function v(e){try{let t;const n=e.columns,r=e.dataset;if(n&&r){const e=new Map;for(t=0;t<n.length;t++)e.set(n[t].name,t);let a;if(null!=c){let n=e.get(c);const o=[];for(t=0;t<r.length;t++)o[t]=r[t][n];a={type:"category",name:c,data:o}}else a={};let o=[];if(d.length>0)for(t=0;t<d.length;t++){const n=e.get(d[t]);if(n){let e=[];for(let t=0;t<r.length;t++)e[t]=r[t][n];o[t]="area"===u?{type:"line",name:d[t],data:e,areaStyle:{},smooth:!0,symbol:"none"}:{name:d[t],type:u,data:e,large:!0}}}const l={tooltip:{trigger:"axis",axisPointer:{label:{show:!1}}},legend:{},xAxis:a,yAxis:{type:"value"},series:o};i.setOption(l,!0)}}finally{w(),C()}}async function _(){a.html('<i class="icon icon-stop"></i><span>Cancel</span>'),a.removeClass("js-chart-draw").addClass("js-chart-cancel"),h=!0,u=b.selected();const e=E.selected(),t=y.selected();if((0,Hs.arrayEquals)(e,c)&&(0,Hs.arrayEquals)(t,d)&&l===f)v(g);else{m.clear(),c=E.selected(),c&&m.add(c),d=y.selected(),function(e,t){for(let n=0;n<e.length;n++)t.add(e[n])}(y.selected(),m);let e="";m.forEach((function(t){""!==e&&(e+=","),e+=t})),s=(new Date).getTime();const t=await n.queryRaw(l,{count:!1,timings:!1,cols:e,src:"vis"});t.type===T.DQL?(r=t,h=!1,I.publish(A.MSG_QUERY_OK,{delta:(new Date).getTime()-s,count:r.count}),g=r,f=l,v(r)):function(e){h=!1,C(),I.publish(A.MSG_QUERY_ERROR,{query:f,r:e.responseJSON,status:e.status,statusText:e.statusText,delta:(new Date).getTime()-s})}(t),I.publish(A.MSG_QUERY_RUNNING)}var r}function x(){i.setOption(p,!0)}o=r.find(".quick-vis-canvas")[0],vs()(window).resize(w),I.subscribe(A.MSG_ACTIVE_SIDEBAR,w),i=Rs.S1(o,Fs),I.subscribe(A.MSG_QUERY_DATASET,(function(e){let t=[];const n=e.columns;for(let e=0;e<n.length;e++)t[e]={text:n[e].name,value:n[e].name};E.setData(t),y.setData(t),y.set(t.slice(1).map((e=>e.text))),l=e.query,x()})),a.click((function(){return h?n.abort():_(),_(),!1})),x(),w()}(vs()("#quick-vis"),window.bus,t),e.addEventListener("header.click",(function(e){I.publish(A.MSG_EDITOR_INSERT_COLUMN,e.detail.columnName)})),e.addEventListener("yield.focus",(function(){I.publish(A.MSG_EDITOR_FOCUS)})),e.addEventListener("freeze.state",(function(e){d(e.detail.freezeLeft)}))}),[]),(0,r.useEffect)((()=>{i?.type===T.DQL&&(a(i.count),s?.current?.setData(i))}),[i]),(0,r.useEffect)((()=>{const t=document.getElementById("grid"),n=document.getElementById("quick-vis");t&&n&&("grid"===e?(n.style.display="none",s?.current?.show()):(s?.current?.hide(),n.style.display="flex"))}),[e]),(0,r.useEffect)((()=>{s?.current?.render()}),[l]);const[m,g]=(0,r.useState)(!1),f=[{tooltipText:"Copy result to Markdown",trigger:r.createElement(Et,{onClick:()=>{p(s?.current?.getResultAsMarkdown()).then((()=>{g(!0),setTimeout((()=>g(!1)),1e3)}))}},m?r.createElement(Ns.J,{size:"18px"}):r.createElement(Ts.U,{size:"18px"}))},{tooltipText:"Freeze left column",trigger:r.createElement(Et,{onClick:()=>{s?.current?.toggleFreezeLeft(),s?.current?.focus()},selected:c>0},r.createElement(qs,{size:"18px"}))},{tooltipText:"Move selected column to the front",trigger:r.createElement(Dn.z,{skin:"transparent",onClick:s?.current?.shuffleFocusedColumnToFront},r.createElement(Ss.W,{size:"18px"}))},{tooltipText:"Reset grid layout",trigger:r.createElement(Dn.z,{skin:"transparent",onClick:s?.current?.clearCustomLayout},r.createElement(xs.A,{size:"18px"}))},{tooltipText:"Refresh",trigger:r.createElement(Dn.z,{skin:"transparent",onClick:()=>{const e=s?.current?.getSQL();e&&I.publish(A.MSG_QUERY_EXEC,{q:e})}},r.createElement(_r.h,{size:"18px"}))}];return(0,r.useEffect)((()=>{i?.type===T.DQL&&a(i.count)}),[i]),r.createElement(Vs,null,r.createElement(zs,null,r.createElement(Us,null,n&&r.createElement(Gs,{color:"foreground"},`${n.toLocaleString()} row${n>1?"s":""}`),"grid"===e&&f.map(((e,t)=>r.createElement(dt,{key:t,delay:350,placement:"bottom",trigger:e.trigger},r.createElement(_t,null,e.tooltipText)))),r.createElement(dt,{delay:350,placement:"bottom",trigger:r.createElement(Dn.z,{skin:"transparent",onClick:()=>{const e=s?.current?.getSQL();e&&t.exportQueryToCsv(e)}},r.createElement(_s.Q,{size:"18px"}))},r.createElement(_t,null,"Download result as a CSV file"))),r.createElement(Zs,null,r.createElement("div",{id:"grid"}),r.createElement("div",{id:"quick-vis"},r.createElement("div",{className:"quick-vis-controls"},r.createElement("form",{className:"v-fit",role:"form"},r.createElement("div",{className:"form-group"},r.createElement("label",null,"Chart type"),r.createElement("select",{id:"_qvis_frm_chart_type"},r.createElement("option",null,"bar"),r.createElement("option",null,"line"),r.createElement("option",null,"area"))),r.createElement("div",{className:"form-group"},r.createElement("label",null,"Labels"),r.createElement("select",{id:"_qvis_frm_axis_x","data-hook":"chart-panel-labels-select"})),r.createElement("div",{className:"form-group"},r.createElement("label",null,"Series"),r.createElement("select",{id:"_qvis_frm_axis_y","data-hook":"chart-panel-series-select",multiple:!0})),r.createElement("button",{className:"button-primary js-chart-draw",id:"_qvis_frm_draw","data-hook":"chart-panel-draw-button"},r.createElement("i",{className:"icon icon-play"}),r.createElement("span",null,"Draw")))),r.createElement("div",{className:"quick-vis-canvas"})))))};var js=n(83332),Ws=n(85506);const Qs=(0,r.createContext)({query:"",setQuery:()=>{},selectOpen:!1,setSelectOpen:()=>{},selectedTables:[],setSelectedTables:()=>{},handleSelectToggle:()=>{},selectedTablesMap:new Map,focusedIndex:null,setFocusedIndex:()=>{}}),Ys=()=>{const e=(0,r.useContext)(Qs);if(!e)throw new Error("useSchema must be used within SchemaProvider");return e},Ks=({children:e})=>{const[t,n]=(0,r.useState)(""),[a,o]=(0,r.useState)(!1),[i,l]=(0,r.useState)([]),[s,c]=(0,r.useState)(null),d=(0,r.useMemo)((()=>new Map(i.map((e=>[`${e.name}-${e.type}`,e])))),[i]);return r.createElement(Qs.Provider,{value:{query:t,setQuery:n,selectOpen:a,setSelectOpen:e=>{o(e),e||l([])},selectedTables:i,setSelectedTables:l,handleSelectToggle:({name:e,type:t})=>{const n=`${e}-${t}`;d.has(n)?l(i.filter((e=>`${e.name}-${e.type}`!==n))):l([...i,{name:e,type:t}])},selectedTablesMap:d,focusedIndex:s,setFocusedIndex:c}},e)},Xs=(0,s.ZP)(Pn.x).attrs({justifyContent:"space-between",gap:"1rem",alignItems:"center"}).withConfig({displayName:"toolbar__Root"})(["\n  width: 100%;\n  padding-right: 2rem;\n"]),Js=s.ZP.div.withConfig({displayName:"toolbar__Filter"})(["\n  position: relative;\n  display: flex;\n  width: 100%;\n"]),ec=(0,s.ZP)(Ws.P).withConfig({displayName:"toolbar__FilterIcon"})(["\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  left: 1rem;\n  color: ",";\n"],(({theme:e})=>e.color.gray2)),tc=(0,s.ZP)(_e.x).withConfig({displayName:"toolbar__CloseIcon"})(["\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  right: 0.5rem;\n"]),nc=(0,s.ZP)(Gn.I).withConfig({displayName:"toolbar__StyledInput"})(["\n  padding-left: 3.5rem;\n  padding-right: 3.5rem;\n  width: 100%;\n\n  &::placeholder {\n    color: ",";\n  }\n"],(({theme:e})=>e.color.foreground)),rc=(0,s.ZP)(Dn.z).withConfig({displayName:"toolbar__StyledButton"})(["\n  &:disabled {\n    border: none;\n  }\n"]),ac=(0,s.ZP)(Pn.x).attrs({gap:"0.5rem"}).withConfig({displayName:"toolbar__Error"})(["\n  &,\n  button {\n    color: ",";\n    cursor: ",";\n  }\n"],(({theme:e,suspendedTablesCount:t})=>e.color[t>0?"red":"gray1"]),(({suspendedTablesCount:e})=>e>0?"pointer":"default")),oc=({suspendedTablesCount:e,filterSuspendedOnly:t,setFilterSuspendedOnly:n})=>{const{setQuery:a}=Ys(),o=r.useRef(null);return r.createElement(Xs,null,r.createElement(Js,null,r.createElement(ec,{size:"20px"}),o.current?.value&&r.createElement(tc,{size:"20px",onClick:()=>{a(""),o.current?.value&&(o.current.value="")},"data-hook":"schema-search-clear-button"}),r.createElement(nc,{ref:o,name:"table_filter",placeholder:"Filter...",onChange:e=>a(e.target.value)})),e>0&&r.createElement(ac,{suspendedTablesCount:e},r.createElement(dt,{placement:"bottom",trigger:r.createElement(rc,{skin:"transparent",onClick:()=>n(!t),prefixIcon:r.createElement(Bi.j,{size:"18px"}),"data-hook":"schema-filter-suspended-button"},r.createElement("span",null,e))},r.createElement(_t,null,"Show suspended tables"))))};var ic=n(2590),lc=n(75013),sc=n(82885);const cc=s.ZP.div.withConfig({displayName:"LoadingError__Wrapper"})(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  width: 100%;\n  height: 100%;\n"]),dc=(0,s.ZP)(sc.N).withConfig({displayName:"LoadingError__CloudOffIcon"})(["\n  height: 3rem;\n"]),uc=(0,s.ZP)(Pe).withConfig({displayName:"LoadingError__ErrorTextLine"})(["\n  margin-top: 1rem;\n"]),mc=({error:e})=>r.createElement(cc,null,r.createElement(dc,null),r.createElement(uc,{color:"white"},"Cannot load tables"),e&&r.createElement(uc,{color:"gray2",size:"sm"},e.error));var pc=n(12619),gc=n(4583),fc=n(31704),hc=n(75134),bc=n(19663),Ec=n(75910),yc=n(26213),wc=n(4021),Cc=n(45167),vc=n(96977),_c=n(4637);const xc=s.ZP.div.withConfig({displayName:"table-icon__Root"})(["\n  display: flex;\n  align-items: center;\n  width: ",";\n  height: ",";\n  position: relative;\n  flex-shrink: 0;\n  svg {\n    color: ",";\n  }\n"],"1.4rem","1.4rem",M("cyan")),Sc=s.ZP.span.withConfig({displayName:"table-icon__Asterisk"})(["\n  position: absolute;\n  top: -0.6rem;\n  right: -0.3rem;\n  font-size: 1rem;\n  line-height: 1.8rem;\n  color: ",";\n"],M("orange")),kc=({height:e="14px",width:t="14px"})=>r.createElement("svg",{viewBox:"0 0 24 24",height:e,width:t,fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},r.createElement("path",{d:"M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zM4 8h16V5H4v3zM4 10h16v9H4v-9z",fillRule:"evenodd",clipRule:"evenodd"})),Tc=({height:e="14px",width:t="14px"})=>r.createElement("svg",{viewBox:"0 0 28 28",height:e,width:t,xmlns:"http://www.w3.org/2000/svg"},r.createElement("g",{stroke:"currentColor",strokeWidth:"2",fill:"none",transform:"translate(-2, -2)"},r.createElement("line",{x1:"3",y1:"4",x2:"22",y2:"4"}),r.createElement("line",{x1:"4",y1:"4",x2:"4",y2:"22"}),r.createElement("line",{x1:"21",y1:"4",x2:"21",y2:"11"}),r.createElement("line",{x1:"4",y1:"21",x2:"11",y2:"21"})),r.createElement("g",{transform:"translate(6,6)",fill:"currentColor"},r.createElement("path",{fill:"none",d:"M0 0h24v24H0z"}),r.createElement("path",{d:"M4 8h16V5H4v3zm10 11v-9h-4v9h4zm2 0h4v-9h-4v9zm-8 0v-9H4v9h4zM3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1z"}))),Nc=({walEnabled:e,isPartitioned:t,isMaterializedView:n})=>r.createElement(xc,null,n?r.createElement(Tc,{height:"14px",width:"14px"}):r.createElement(r.Fragment,null,!e&&r.createElement(Sc,null,"*"),t?r.createElement(el.i,{size:"14px"}):r.createElement(kc,{height:"14px"})));function Oc(){return Oc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Oc.apply(this,arguments)}const Rc=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"center"}).withConfig({displayName:"checkbox__Root"})(["\n  width: 16px;\n  height: 16px;\n  flex-shrink: 0;\n  opacity: ",";\n  transition: opacity 275ms ease-in-out;\n  pointer-events: ",";\n"],(({$visible:e})=>e?1:0),(({$visible:e})=>e?"auto":"none")),Ic=s.ZP.div.withConfig({displayName:"checkbox__Unchecked"})(["\n  width: 14px;\n  height: 14px;\n  border-radius: 50%;\n  border: 1px solid ",";\n  cursor: pointer;\n"],M("gray2")),Ac=({visible:e,checked:t,onClick:n})=>r.createElement(Rc,Oc({},n?{onClick:n}:{},{$visible:e}),t?r.createElement(po.e,{size:"16px"}):r.createElement(Ic,null)),Lc=e=>e.startsWith("GEOHASH"),Pc=e=>{const t=/\(([^)]+)\)/g.exec(e);return t&&t.length>1?t[1]:""},Dc=e=>Lc(e)?"GEOHASH":e.toUpperCase(),Mc=e=>"GEOHASH"===e.type?{...e,type:`GEOHASH(${e.precision})`}:e,Bc=()=>("00000000000000000"+(0x10000000000000000*Math.random()).toString(16)).slice(-16),Fc=()=>{const e=Bc(),t=Bc();return e.slice(0,8)+"-"+e.slice(8,12)+"-4"+e.slice(13)+"-a"+t.slice(1,4)+"-"+t.slice(4)},Hc="questdb:expanded:",Vc=`${Hc}tables`,zc=`${Hc}matviews`,Zc=(e,t)=>{try{if(localStorage.setItem(e,t?"true":"false"),!t){const t=Object.keys(localStorage).filter((t=>t.startsWith(e)&&t!==e));return t.forEach((e=>localStorage.removeItem(e))),[e,...t]}return[e]}catch(t){return console.warn("Failed to save to localStorage:",t),[e]}},Uc=e=>(e=>{try{const t=localStorage.getItem(e);return null===t?Vc===e&&(Zc(e,!0),!0):"true"===t}catch(e){return!1}})(e),qc=(0,s.ZP)(Pe).withConfig({displayName:"Row__Type"})(["\n  align-items: center;\n  display: inline-block;\n"]),Gc=(0,s.ZP)(Pe).withConfig({displayName:"Row__Title"})(["\n  .highlight {\n    background-color: #7c804f;\n    color: ",";\n  }\n"],(({theme:e})=>e.color.foreground)),$c=s.ZP.div.withConfig({displayName:"Row__Wrapper"})(["\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  padding: 0.5rem 0;\n  user-select: none;\n  border: 1px solid transparent;\n  border-radius: 0.4rem;\n  min-width: fit-content;\n  width: 100%;\n  flex-grow: 1;\n\n  cursor: ",";\n\n  ","\n\n  ","\n"],(({$selectOpen:e})=>e?"pointer":"default"),(({$level:e})=>e&&`\n    padding-left: ${1.5*e+1}rem;\n  `),(({$focused:e,theme:t})=>e&&`\n    outline: none;\n    background: ${t.color.tableSelection};\n    border: 1px solid ${t.color.cyan};\n  `)),jc=(0,s.ZP)(Gc).withConfig({displayName:"Row__StyledTitle"})(["\n  display: flex;\n  align-items: center;\n  gap: 0.8rem;\n  z-index: 1;\n  flex-shrink: 0;\n  margin-right: 1rem;\n\n  .highlight {\n    background-color: #45475a;\n    color: ",";\n  }\n\n  svg {\n    color: ",";\n  }\n"],(({theme:e})=>e.color.foreground),M("cyan")),Wc=s.ZP.span.withConfig({displayName:"Row__TableActions"})(["\n  z-index: 1;\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n"]),Qc=s.ZP.div.withConfig({displayName:"Row__FlexRow"})(["\n  display: flex;\n  align-items: center;\n  padding-right: 1rem;\n  transform: translateX(",");\n  transition: transform 275ms ease-in-out;\n"],(({$selectOpen:e,$isTableKind:t})=>e&&t?"1rem":"0")),Yc=s.ZP.span.withConfig({displayName:"Row__Spacer"})(["\n  flex: 1;\n"]),Kc=(0,s.ZP)(gc.l).withConfig({displayName:"Row__SortDownIcon"})(["\n  color: ",";\n  margin-right: 0.8rem;\n  flex-shrink: 0;\n"],M("green")),Xc=(0,s.ZP)(fc._).withConfig({displayName:"Row__ChevronRightIcon"})(["\n  color: ",";\n  margin-right: 1.5rem;\n  cursor: pointer;\n  flex-shrink: 0;\n  width: 1.5rem;\n  transform: rotateZ(",");\n  position: absolute;\n  left: -2rem;\n"],M("gray2"),(({$expanded:e})=>e?"90deg":"0deg")),Jc=(0,s.ZP)(hc.B).withConfig({displayName:"Row__DotIcon"})(["\n  color: ",";\n  margin-right: 1rem;\n"],M("gray2")),ed=(0,s.ZP)(bc.o).withConfig({displayName:"Row__Loader"})(["\n  margin-left: 1rem;\n  color: ",";\n  ",";\n"],M("orange"),m),td=s.ZP.div.withConfig({displayName:"Row__ErrorIconWrapper"})(["\n  display: inline-flex;\n  align-items: center;\n  align-self: center;\n\n  svg {\n    color: #f47474;\n  }\n"]),nd=s.ZP.div.withConfig({displayName:"Row__ErrorItem"})(["\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n"]),rd=s.ZP.div.withConfig({displayName:"Row__TypeIcon"})(["\n  margin-right: 0.8rem;\n  display: flex;\n  align-items: center;\n  color: ",";\n\n  svg {\n    color: ",";\n  }\n"],M("cyan"),(({$type:e})=>M("SYMBOL"===e?"yellow":"cyan"))),ad={number:{types:["BOOLEAN","BYTE","SHORT","INT","LONG","LONG256","DOUBLE","FLOAT","BINARY","UUID"],icon:Ec.G},date:{types:["DATE"],icon:yc.H},text:{types:["CHAR","VARCHAR","STRING"],icon:wc.D},symbol:{types:["SYMBOL"],icon:Cc.V},time:{types:["TIMESTAMP","INTERVAL"],icon:gc.l},network:{types:["IPV4"],icon:vc.T},geo:{types:["GEOHASH"],icon:_c.x}},od=({icon:e,size:t="14px",type:n})=>r.createElement(rd,{$type:n},r.createElement(e,{size:t})),id=({isDesignatedTimestamp:e,type:t})=>t?e?r.createElement(Xe,{icon:r.createElement(Kc,{size:"14px"}),placement:"top",tooltip:"Designated timestamp"}):(e=>{const t=Object.values(ad).find((({types:t})=>t.some((t=>t===Dc(e)))));return r.createElement(od,{icon:t?.icon??Jc,type:e})})(t):null,ld=({className:e,designatedTimestamp:t,expanded:n,kind:a,name:o,partitionBy:i,walEnabled:l,onExpandCollapse:s,navigateInTree:c,"data-hook":d,isLoading:u,type:m,errors:p,value:g,id:f,index:h})=>{const{query:b,selectOpen:E,selectedTables:y,handleSelectToggle:w,focusedIndex:C,setFocusedIndex:v}=Ys(),[_,x]=(0,r.useState)(!1),S=(0,r.useRef)(null),k=(0,r.useRef)(null),T=["folder","table","matview"].includes(a)||"column"===a&&"SYMBOL"===m,N=["table","matview"].includes(a),O=[zc,Vc].includes(f??""),R=!!y.find((e=>e.name===o&&e.type===a)),I=()=>{T&&s()};return(0,r.useEffect)((()=>(u?S.current=setTimeout((()=>{x(!0)}),500):x(!1),()=>{S.current&&(clearTimeout(S.current),S.current=null)})),[u]),(0,r.useLayoutEffect)((()=>{C===h&&k.current?.focus()}),[C]),!E||N||O?r.createElement($c,{$level:f?f.split(":").length-2:0,$selectOpen:E,$focused:C===h,ref:k,"data-hook":d??"schema-row","data-kind":a,"data-index":h,"data-id":f,className:e,tabIndex:100,onBlur:()=>{C===h&&v(null)},onContextMenu:e=>{N||e.preventDefault()},onDoubleClick:I,onClick:()=>{N&&E&&w&&w({name:o,type:a}),E&&!O||C===h||v(h)},onKeyDown:e=>{e.preventDefault(),T&&("Enter"===e.key||"ArrowRight"===e.key&&!n||"ArrowLeft"===e.key&&n)&&I();const t=!(T&&n||"ArrowLeft"!==e.key),r=(!T||n)&&"ArrowRight"===e.key||"ArrowDown"===e.key||"Tab"===e.key;t&&c({to:"parent",id:f}),r&&c({to:"next",id:f}),("ArrowUp"===e.key||e.shiftKey&&"Tab"===e.key)&&c({to:"previous",id:f}),"Home"===e.key&&c({to:"start"}),"End"===e.key&&c({to:"end"}),"PageUp"===e.key&&c({to:"pageUp"}),"PageDown"===e.key&&c({to:"pageDown"})}},r.createElement(Pn.x,{align:"center",justifyContent:"flex-start",gap:"2rem",style:{width:"100%",position:"relative",minWidth:"fit-content"}},N&&r.createElement("div",{style:{position:"absolute",left:"-2rem"}},r.createElement(Ac,{visible:E,checked:R})),r.createElement(Qc,{$selectOpen:E,$isTableKind:N},T&&(!E||!N)&&r.createElement(Xc,{size:"15px",$expanded:n,onClick:I}),"column"===a&&r.createElement(id,{isDesignatedTimestamp:o===t,type:m}),r.createElement(jc,{color:"foreground",ellipsis:!0,"data-hook":`schema-${a}-title`},N&&r.createElement(Nc,{isPartitioned:i&&"NONE"!==i,walEnabled:l,isMaterializedView:"matview"===a}),"detail"===a&&r.createElement(pc.Z,{size:"14px"}),r.createElement(nl(),{highlightClassName:"highlight",searchWords:[b??""],textToHighlight:o})),m&&r.createElement(qc,{color:"gray2",transform:"lowercase",ellipsis:!0},"(",m,")"),"detail"===a&&r.createElement(Pe,{color:"gray2"},g),_&&r.createElement(ed,{size:"18px"}),r.createElement(Yc,null),p&&p.length>0&&r.createElement(Wc,null,r.createElement(dt,{placement:"top",trigger:r.createElement(td,{"data-hook":"schema-row-error-icon"},r.createElement(Bi.j,{size:"18px"}))},r.createElement(_t,null,p.length>1?p.map((e=>r.createElement(nd,{key:e},r.createElement(td,null,r.createElement(Bi.j,{size:"18px"})),r.createElement(Pe,{color:"foreground"},e)))):r.createElement(Pe,{color:"foreground"},p[0]))))))):null},sd=(e,t,n)=>n.map((n=>{const r=`${t}:${n.column}`,a={id:r,kind:"column",name:n.column,column:n,parent:t,isExpanded:Uc(r),designatedTimestamp:e.designatedTimestamp,type:n.type,children:[]};return"SYMBOL"===n.type&&(a.children=[{id:`${r}:indexed`,kind:"detail",name:"Indexed",parent:r,value:n.indexed?"Yes":"No",children:[]},{id:`${r}:symbolCapacity`,kind:"detail",name:"Symbol capacity",parent:r,value:n.symbolCapacity.toString(),children:[]},{id:`${r}:symbolCached`,kind:"detail",name:"Cached",parent:r,value:n.symbolCached?"Yes":"No",children:[]}]),a})),cd=(e,t)=>[{id:`${t}:partitionBy`,kind:"detail",name:"Partitioning",parent:t,value:e.partitionBy&&"NONE"!==e.partitionBy?`By ${e.partitionBy.toLowerCase()}`:"None",children:[]},{id:`${t}:walEnabled`,kind:"detail",name:"WAL",parent:t,value:e.walEnabled?"Enabled":"Disabled",children:[]}],dd=(e,t,n=!1,r,a)=>{const o=`${t}:${e.table_name}`,i=n?r?.find((t=>t.view_name===e.table_name)):void 0,l=a?.find((t=>t.name===e.table_name)),s=`${o}:columns`,c=`${o}:baseTables`,d=`${o}:storageDetails`,u={id:o,kind:n?"matview":"table",name:e.table_name,table:e,matViewData:i,parent:t,isExpanded:Uc(o),partitionBy:e.partitionBy,walEnabled:e.walEnabled,walTableData:l,children:[{id:s,kind:"folder",name:"Columns",table:e,parent:o,isExpanded:Uc(s),children:[]},{id:d,kind:"folder",name:"Storage details",parent:o,isExpanded:Uc(d),children:cd(e,d)}]};return n&&i&&u.children.push({id:c,kind:"folder",name:"Base tables",parent:o,isExpanded:Uc(c),children:[{id:`${c}:${i.base_table_name}`,kind:"detail",name:i.base_table_name,parent:c,children:[]}]}),u},ud=(e,t)=>{const[n,r,a,...o]=t.split(":"),i=`${n}:${r}:${a}`;let l=e[i];if(!l)return;let s=i,c=e;for(const e of o){s=`${s}:${e}`;const t=l?.children?.find((e=>e.id===s));if(!t)return;c=l,l=t}return{node:l,parent:c}},md=(e,t,n)=>{const r=ud(e,t);if(!r)return e;const{node:a,parent:o}=r,i={...e};if(a){const e=n(a);o.id?o.children=o.children.map((n=>n.id===t?e:n)):i[t]=e}return i},pd=(e,t)=>e.findIndex((e=>e.id===t)),gd=e=>{const t=[e];return e.isExpanded&&e.children.forEach((e=>{t.push(...gd(e))})),t};var fd=n(71843);function hd(){return hd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},hd.apply(this,arguments)}const bd=(0,s.ZP)(fd.VY).withConfig({displayName:"ContextMenu__StyledContent"})(["\n  background-color: #343846; /* vscode-menu-background */\n  border-radius: 0.5rem;\n  padding: 0.4rem;\n  box-shadow: 0 0.2rem 0.8rem rgba(0, 0, 0, 0.36); /* vscode-widget-shadow */\n  z-index: 9999;\n  min-width: 160px;\n"]),Ed=(0,s.ZP)(fd.ck).withConfig({displayName:"ContextMenu__StyledItem"})(['\n  font-size: 1.3rem;\n  height: 2.6rem;\n  font-family: "system-ui", sans-serif;\n  cursor: pointer;\n  color: rgb(248, 248, 242); /* vscode-menu-foreground */\n  display: flex;\n  align-items: center;\n  padding: 0 1.2rem;\n  border-radius: 0.4rem;\n\n  &[data-highlighted] {\n    background-color: #45475a;\n    color: rgb(240, 240, 240);\n  }\n']),yd=s.ZP.span.withConfig({displayName:"ContextMenu__IconWrapper"})(["\n  margin-right: 10px;\n"]),wd=fd.fC,Cd=fd.xz,vd=r.forwardRef(((e,t)=>r.createElement(fd.Uv,{"data-test":"emre"},r.createElement(bd,hd({},e,{ref:t}))))),_d=r.forwardRef((({children:e,icon:t,...n},a)=>r.createElement(Ed,hd({},n,{ref:a}),t&&r.createElement(yd,null,t),e)));var xd=n(47223);const Sd={[N.TOO_MANY_OPEN_FILES]:{title:"System limit for open files",message:"Too many open files, please, increase the maximum number of open file handlers OS limit",link:"https://questdb.io/docs/deployment/capacity-planning/#maximum-open-files"},[N.DISK_FULL]:{title:"OS configuration",message:"No space left on device, please, extend the volume or free existing disk space up",link:"https://questdb.io/docs/deployment/capacity-planning/#os-configuration"},[N.OUT_OF_MMAP_AREAS]:{title:"Max virtual memory areas limit",message:"Out of virtual memory mapping areas, please, increase the maximum number of memory-mapped areas OS limit",link:"https://questdb.io/docs/deployment/capacity-planning/#max-virtual-memory-areas-limit"},[N.OUT_OF_MEMORY]:{title:"Out of memory",message:"Out of memory, please, analyze system metrics, and upgrade memory, if necessary",link:"https://questdb.io/docs/deployment/capacity-planning/#cpu-and-ram-configuration"},[N.UNSUPPORTED_FILE_SYSTEM]:{title:"Unsupported file system",message:"DB root is located on an unsupported file system, please, move the DB root to a volume with supported file system",link:"https://questdb.io/docs/operations/backup/#supported-filesystems"}},kd=(0,s.ZP)(fi.V.Content).withConfig({displayName:"SuspensionDialog__StyledDialogContent"})(["\n  border-color: #723131;\n"]),Td=(0,s.ZP)(fi.V.Description).withConfig({displayName:"SuspensionDialog__StyledDescription"})(["\n  display: grid;\n  gap: 2rem;\n"]),Nd=(0,s.ZP)(ke).attrs({align:"center",flexDirection:"column"}).withConfig({displayName:"SuspensionDialog__ContentBlockBox"})(["\n  width: 100%;\n"]),Od=s.ZP.div.withConfig({displayName:"SuspensionDialog__FormWrapper"})(["\n  width: 100%;\n  display: grid;\n  grid-template-columns: 100px auto;\n  gap: 1rem;\n"]),Rd=(0,s.ZP)(ir.Input).withConfig({displayName:"SuspensionDialog__TransactionInput"})(["\n  width: 10rem;\n"]),Id=(0,s.ZP)(ke).withConfig({displayName:"SuspensionDialog__Icon"})(["\n  height: 4.8rem;\n"]),Ad=(0,s.ZP)(Gn.I).withConfig({displayName:"SuspensionDialog__StyledInput"})(["\n  width: 100%;\n  font-family: ",";\n  background: #313340;\n  border-color: ",";\n"],(({theme:e})=>e.fontMonospace),(({theme:e})=>e.color.selection)),Ld="Error restarting transaction",Pd=({walTableData:e,open:t,onOpenChange:n})=>{const{quest:a}=(0,r.useContext)(qr),[o,i]=(0,r.useState)(!1),[l,s]=(0,r.useState)(!1),[c,d]=(0,r.useState)(),u=parseFloat(e.sequencerTxn)-parseFloat(e.writerTxn);return(0,r.useEffect)((()=>{t&&(d(void 0),s(!1))}),[t]),r.createElement(fi.V.Root,{open:t,onOpenChange:e=>{e||I.publish(A.MSG_QUERY_SCHEMA),n(e)}},r.createElement(fi.V.Portal,null,r.createElement(Ce.A,null,r.createElement(ve.a,{primitive:fi.V.Overlay})),r.createElement(kd,{"data-hook":"schema-suspension-dialog","data-table-name":e.name,onClick:e=>{e.stopPropagation()},onEscapeKeyDown:()=>n(!1),onPointerDownOutside:()=>n(!1)},r.createElement(fi.V.Title,null,r.createElement(ke,null,r.createElement(el.i,{size:20,color:"#FF5555"}),"Table is suspended: ",e.name)),r.createElement(Td,null,r.createElement(ke,{gap:"3rem",flexDirection:"column",align:"center"},c&&r.createElement(Pe,{color:"red"},c),l&&r.createElement(Pe,{color:"green"},"WAL resumed successfully!"),e.errorTag&&Sd[e.errorTag]&&r.createElement(Pe,{color:"red","data-hook":"schema-suspension-dialog-error-message",size:"lg",align:"center"},Sd[e.errorTag].message),r.createElement(ke,{gap:"2rem",align:"flex-start"},r.createElement(ke,{gap:"1rem",flexDirection:"column",align:"center"},r.createElement(Id,null,r.createElement("img",{src:"assets/icon-copy.svg",alt:"Copy icon",width:"48",height:"48"})),r.createElement(Pe,{color:"foreground",size:"lg"},e.sequencerTxn)),r.createElement(Id,null,r.createElement("img",{src:"assets/line.svg",alt:"Broken transation illustration",width:"108",height:"18"})),r.createElement(ke,{gap:"1rem",flexDirection:"column",align:"center"},r.createElement(Id,null,r.createElement("img",{src:"assets/icon-database.svg",alt:"Database icon",width:"36",height:"38"})),r.createElement(Pe,{color:"foreground",size:"lg"},e.writerTxn))),r.createElement(Pe,{color:"foreground",size:"lg"},u," transaction",1===u?"":"s"," behind"),e.errorMessage&&r.createElement(ke,{flexDirection:"column",gap:"1rem",style:{width:"100%"}},r.createElement(Pe,{color:"foreground"},"Server message:"),r.createElement(ke,{gap:"0.5rem",style:{width:"100%"}},r.createElement(Ad,{name:"server_message",disabled:!0,value:e.errorMessage}),r.createElement(Dn.z,{skin:"secondary",onClick:()=>{p(e.errorMessage??"")}},r.createElement(mo.f,{size:"18px"})))),e.errorTag&&Sd[e.errorTag]&&r.createElement(Nd,{gap:"0.5rem"},r.createElement(Pe,{color:"foreground"},"Workarounds and documentation:"),r.createElement(ot,{color:"cyan",hoverColor:"cyan",href:Sd[e.errorTag].link,rel:"noreferrer",target:"_blank","data-hook":"schema-suspension-dialog-error-link"},r.createElement(ke,{align:"center",gap:"0.25rem"},r.createElement(ha.d,{size:"16px"}),Sd[e.errorTag].title))),r.createElement(Nd,{gap:"2rem"},r.createElement(Pe,{color:"gray2"},"If you have addressed the issue, restart the process:"),r.createElement(ir,{name:"resume_transaction_form",onSubmit:async t=>{i(!0),d(void 0);try{const n=await a.query(`ALTER TABLE ${e.name} RESUME WAL${t.resume_transaction_id?` FROM TRANSACTION ${t.resume_transaction_id}`:""}`);n&&n.type===T.DDL?s(!0):d(Ld),i(!1)}catch(e){const t=e;i(!1),d(`${Ld}${t.error?`: ${t.error}`:""}`)}},defaultValues:{resume_transaction_id:void 0},validationSchema:sr().object({resume_transaction_id:sr().number().optional().allow("")})},r.createElement(Od,null,r.createElement(ir.Item,{name:"resume_transaction_id"},r.createElement(Rd,{name:"resume_transaction_id",placeholder:(parseInt(e.writerTxn)+1).toString()})),r.createElement(ir.Submit,{disabled:o,prefixIcon:r.createElement(lc.G,{size:"18px"}),variant:"secondary","data-hook":"schema-suspension-dialog-restart-transaction"},o?"Restarting...":"Resume WAL")))))),r.createElement(fi.V.ActionButtons,null,r.createElement(fi.V.Close,{asChild:!0},r.createElement(Dn.z,{prefixIcon:r.createElement(xd.W,{size:18}),skin:"secondary","data-hook":"schema-suspension-dialog-dismiss",onClick:()=>n(!1)},"Dismiss"))))))},Dd=(0,s.ZP)(ld).withConfig({displayName:"VirtualTables__SectionHeader"})(["\n  cursor: ",";\n  pointer-events: ",";\n\n  ","\n"],(({$disabled:e})=>e?"not-allowed":"default"),(({$disabled:e})=>e?"none":"auto"),(({$disabled:e})=>e&&"\n    opacity: 0.5;\n  ")),Md=(0,s.ZP)(ld).withConfig({displayName:"VirtualTables__TableRow"})(["\n  ","\n"],(({$contextMenuOpen:e,theme:t})=>e&&`\n    background: ${t.color.tableSelection};\n    border: 1px solid ${t.color.cyan};\n  `)),Bd=s.ZP.div.withConfig({displayName:"VirtualTables__FlexSpacer"})(["\n  flex: 1;\n"]),Fd=(0,s.ZP)(ic.G).withConfig({displayName:"VirtualTables__Loader"})(["\n  margin-left: 1rem;\n  align-self: center;\n  color: ",";\n  ",";\n"],M("foreground"),m),Hd=()=>{const[e,t]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{const e=setTimeout((()=>t(!0)),1e3);return()=>clearTimeout(e)}),[]),e?r.createElement(Fd,{size:"22px"}):null},Vd=({tables:e,walTables:t,materializedViews:n,filterSuspendedOnly:a,state:i,loadingError:l})=>{const s=(0,o.I0)(),{query:c,focusedIndex:d,setFocusedIndex:u}=Ys(),{quest:m}=(0,r.useContext)(qr),[g,f]=(0,r.useState)(!1),[h,b]=(0,r.useState)({}),[E,y]=(0,r.useState)(null),[w,C]=(0,r.useState)(null),v=(0,r.useRef)(null),_=(0,r.useRef)(null),x=(0,r.useRef)(null);(({virtuosoRef:e,focusedIndex:t,setFocusedIndex:n,wrapperRef:a})=>{(0,r.useEffect)((()=>{const n=n=>{null!==t&&a.current&&!a.current.contains(document.activeElement)&&("ArrowDown"!==n.key&&"ArrowUp"!==n.key&&"Home"!==n.key&&"End"!==n.key&&"PageUp"!==n.key&&"PageDown"!==n.key||(n.preventDefault(),n.stopPropagation(),e.current?.scrollIntoView({index:t})))};return window.addEventListener("keydown",n,!0),()=>window.removeEventListener("keydown",n,!0)}),[t,e,a]),(0,r.useEffect)((()=>{const e=e=>{a.current&&!a.current.contains(e.target)&&null!==t&&n(null)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)}),[t,n])})({virtuosoRef:v,focusedIndex:d,setFocusedIndex:u,wrapperRef:x});const[S,k]=(0,r.useMemo)((()=>e.filter((e=>{const n=e.table_name.toLowerCase(),r=c.toLowerCase();return n.includes(r)&&(!a||e.walEnabled&&t?.find((t=>t.name===e.table_name))?.suspended)})).reduce(((e,t)=>(e[t.matView?1:0].push(t),e)),[[],[]]).map((e=>e.sort(((e,t)=>e.table_name.toLowerCase().localeCompare(t.table_name.toLowerCase())))))),[e,c,a,t]),N=(0,r.useMemo)((()=>g?Object.values(h).reduce(((e,t)=>(e.push(...gd(t)),e)),[]):[]),[h,g]),O=async e=>{try{const t=await m.showColumns(e);if(t&&t.type===T.DQL)return t.data}catch(t){s($t({content:r.createElement(Pe,{color:"red"},"Cannot show columns from table '",e,"'"),type:Zt.ERROR}))}return[]},R=(0,r.useCallback)((async e=>{const t=!Uc(e);let n={...h};if(Zc(e,t).forEach((e=>{n=md(n,e,(e=>({...e,isExpanded:t})))})),e.endsWith(":columns")){const r=e.split(":"),a=r[r.length-2];if(t){const t=await O(a);t&&(n=md(n,e,(n=>({...n,children:sd(n.table,e,t)}))))}else n=md(n,e,(e=>({...e,children:[]})))}b(n)}),[h]),I=(0,r.useCallback)((e=>{if(!v.current)return;const{to:t}=e;switch(t){case"start":v.current.scrollIntoView({index:0,align:"start",done:()=>u(0)});break;case"end":v.current.scrollIntoView({index:N.length-1,align:"end",done:()=>u(N.length-1)});break;case"next":const{id:t}=e,n=Math.min(N.length-1,pd(N,t)+1);v.current.scrollIntoView({index:n,done:()=>u(n)});break;case"previous":const{id:r}=e,a=Math.max(0,pd(N,r)-1);v.current.scrollIntoView({index:a,done:()=>u(a)});break;case"parent":const{id:o}=e,i=o.split(":").slice(0,-1).join(":"),l=pd(N,i);-1!==l&&v.current.scrollIntoView({index:l,done:()=>u(l)});break;case"pageUp":if(!_.current)return;const{startIndex:s,endIndex:c}=_.current,m=c-s,p=-1!==d&&d!==s?s:Math.max(0,s-m);v.current.scrollIntoView({index:p,align:"start",done:()=>u(p)});break;case"pageDown":if(!_.current)return;const{startIndex:g,endIndex:f}=_.current,h=f-g,b=-1!==d&&d!==f?f:Math.min(N.length-1,f+h);v.current.scrollIntoView({index:b,align:"end",done:()=>u(b)})}}),[N,v,_,d,u]),A=(0,r.useCallback)((e=>{const t=N[e];if("detail"===t.kind)return r.createElement(ld,{kind:"detail",index:e,name:t.value?`${t.name}:`:t.name,value:t.value,id:t.id,onExpandCollapse:()=>{},navigateInTree:I});if(t.id===Vc||t.id===zc){const n=t.id===Vc;return r.createElement(Dd,{$disabled:n?0===S.length:0===k.length,name:t.name,kind:"folder",index:e,expanded:t.isExpanded,"data-hook":`${t.isExpanded?"collapse":"expand"}-${n?"tables":"materialized-views"}`,onExpandCollapse:()=>R(t.id),id:t.id,navigateInTree:I})}return"folder"===t.kind?r.createElement(ld,{kind:"folder",index:e,name:t.name,expanded:t.isExpanded,onExpandCollapse:()=>R(t.id),id:t.id,navigateInTree:I}):"table"===t.kind||"matview"===t.kind?r.createElement(r.Fragment,null,r.createElement(wd,{onOpenChange:e=>y(e?t.id:null)},r.createElement(Cd,null,r.createElement(r.Fragment,null,r.createElement(Md,{$contextMenuOpen:E===t.id,kind:t.kind,name:t.name,index:e,expanded:t.isExpanded,onExpandCollapse:()=>R(t.id),navigateInTree:I,partitionBy:t.partitionBy,walEnabled:t.walEnabled,id:t.id,errors:[..."invalid"===t.matViewData?.view_status?[`Materialized view is invalid${t.matViewData?.invalidation_reason&&`: ${t.matViewData?.invalidation_reason}`}`]:[],...t.walTableData?.suspended?["Suspended"]:[]]}),t.walTableData?.suspended&&r.createElement(Pd,{walTableData:t.walTableData,open:w===t.id,onOpenChange:e=>{C(e?t.id:null)}}))),r.createElement(vd,null,r.createElement(_d,{"data-hook":"table-context-menu-copy-schema",onClick:async()=>await(async(e,t)=>{try{let n;n=t?await m.showMatViewDDL(e):await m.showTableDDL(e),n?.type===T.DQL&&n.data?.[0]?.ddl&&(p(n.data[0].ddl),s($t({content:r.createElement(Pe,{color:"foreground"},"Schema copied to clipboard"),type:Zt.SUCCESS})))}catch(n){s($t({content:r.createElement(Pe,{color:"red"},"Cannot copy schema for ",t?"materialized view":"table"," '",e,"'"),type:Zt.ERROR}))}})(t.name,"matview"===t.kind),icon:r.createElement(mo.f,{size:14})},"Copy schema"),t.walTableData?.suspended&&r.createElement(_d,{"data-hook":"table-context-menu-resume-wal",onClick:()=>C(t.id),icon:r.createElement(lc.G,{size:14})},"Resume WAL")))):"column"===t.kind?r.createElement(ld,{kind:"column",name:t.name,index:e,expanded:t.isExpanded,onExpandCollapse:()=>R(t.id),designatedTimestamp:t.designatedTimestamp,type:t.type,id:t.id,navigateInTree:I}):null}),[N,S,k,R,E,w,I]);return(0,r.useEffect)((()=>{i.view===Kd.ready&&(async()=>{const e={[Vc]:{id:Vc,kind:"folder",name:`Tables (${S.length})`,isExpanded:0!==S.length&&Uc(Vc),children:S.map((e=>dd(e,Vc,!1,n,t)))},[zc]:{id:zc,kind:"folder",name:`Materialized views (${k.length})`,isExpanded:0!==k.length&&Uc(zc),children:k.map((e=>dd(e,zc,!0,n,t)))}},r=[...S,...k],a=[];for(const t of r){const n=`${t.matView?zc:Vc}:${t.table_name}:columns`;if(Uc(n)){const r=O(t.table_name).then((r=>{if(r){const a=ud(e,n);a&&(a.node.children=sd(t,n,r),a.node.isExpanded=!0)}}));a.push(r)}}await Promise.all(a),b(e),f(!0)})()}),[i.view,S,k,n]),i.view===Kd.loading||i.view===Kd.ready&&!g?r.createElement(Hd,null):i.view===Kd.error?l?r.createElement(mc,{error:l}):r.createElement(Bd,null):r.createElement("div",{ref:x,style:{height:"100%"}},r.createElement(xt.OO,{totalCount:N.length,ref:v,rangeChanged:e=>{_.current=e},data:N,itemContent:e=>A(e),style:{height:"100%"}}))},zd=(e,t)=>isNaN(parseInt(e))?t:parseInt(e),Zd={authPayload:"",editorCol:1,editorLine:1,editorSplitterBasis:350,resultsSplitterBasis:350,updateSettings:(e,t)=>{},exampleQueriesVisited:!1,autoRefreshTables:!0},Ud=(0,r.createContext)(Zd),qd=({children:e})=>{const[t,n]=(0,r.useState)(dn(L.AUTH_PAYLOAD)),[a,o]=(0,r.useState)(zd(dn(L.EDITOR_COL),10)),[i,l]=(0,r.useState)(zd(dn(L.EDITOR_LINE),10)),[s,c]=(0,r.useState)(zd(dn(L.EDITOR_SPLITTER_BASIS),350)),[d,u]=(0,r.useState)(zd(dn(L.RESULTS_SPLITTER_BASIS),350)),[m,p]=(0,r.useState)("true"===dn(L.EXAMPLE_QUERIES_VISITED)),[g,f]=(0,r.useState)(!dn(L.AUTO_REFRESH_TABLES)||"true"===dn(L.AUTO_REFRESH_TABLES));return r.createElement(Ud.Provider,{value:{authPayload:t,editorCol:a,editorLine:i,editorSplitterBasis:s,resultsSplitterBasis:d,updateSettings:(e,t)=>{un(e,t.toString()),(e=>{const t=dn(e);switch(e){case L.AUTH_PAYLOAD:n(t);break;case L.EDITOR_COL:o(zd(t,10));break;case L.EDITOR_LINE:l(zd(t,10));break;case L.EXAMPLE_QUERIES_VISITED:p("true"===t);break;case L.EDITOR_SPLITTER_BASIS:c(zd(t,350));break;case L.RESULTS_SPLITTER_BASIS:u(zd(t,350));break;case L.AUTO_REFRESH_TABLES:f("true"===t)}})(e)},exampleQueriesVisited:m,autoRefreshTables:g}},e)},Gd=()=>(0,r.useContext)(Ud);function $d(){return $d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$d.apply(this,arguments)}const jd=(0,s.iv)(["\n  display: flex;\n  justify-content: center;\n"]),Wd=(0,s.ZP)(lt).withConfig({displayName:"Schema__Wrapper"})(["\n  overflow-x: auto;\n  height: 100%;\n"]),Qd=(0,s.ZP)(it).withConfig({displayName:"Schema__Content"})(["\n  display: flex;\n  flex-direction: column;\n  overflow: auto;\n  ",";\n"],(({_loading:e})=>e&&jd)),Yd=(0,s.ZP)(Et).withConfig({displayName:"Schema__ToolbarToggleButton"})(["\n  &&:not(:disabled) {\n    width: auto;\n    padding: 0 1rem;\n    height: 3rem;\n  }\n"]);let Kd=function(e){return e[e.loading=0]="loading",e[e.error=1]="error",e[e.ready=2]="ready",e}({});const Xd={view:Kd.loading},Jd=(e,t)=>({...e,...t}),eu=({innerRef:e,...t})=>{const[n,a]=(0,r.useReducer)(Jd,Xd),{quest:i}=(0,r.useContext)(qr),[l,s]=(0,r.useState)(null),c=(0,r.useRef)(null),[d,u]=(0,r.useState)(),[m,g]=(0,r.useState)(),[f,h]=(0,r.useState)(),b=(0,o.I0)(),[E,y]=(0,r.useState)(!1),{autoRefreshTables:w,updateSettings:C}=Gd(),[v,_]=(0,r.useState)(!1),x=(0,r.useRef)(!1),{addBuffer:S}=ma(),{selectOpen:k,setSelectOpen:N,selectedTables:O,setSelectedTables:R}=Ys(),P=async()=>{try{const e=await i.showTables();if(e&&e.type===T.DQL){c.current=null,u(e.data),b(Kt(e.data));const t=await i.query("wal_tables()");t&&t.type===T.DQL&&g(t.data.filter((t=>e.data.map((e=>e.table_name)).includes(t.name)))),D(),a({view:Kd.ready})}else a({view:Kd.error})}catch(e){a({view:Kd.error})}},D=async()=>{try{const e=await i.query("materialized_views()");e&&e.type===T.DQL&&h(e.data)}catch(e){}},M=async()=>{const e=["information_schema.questdb_columns()","information_schema.columns()"];for(const t of e)try{const e=await i.query(t);if(e?.type===T.DQL)return void b(Xt(e.data))}catch{}a({view:Kd.error})};(0,r.useEffect)((()=>{P(),M(),I.subscribe(A.MSG_CONNECTION_ERROR,(e=>{e&&(c.current=e,s(e))})),I.subscribe(A.MSG_CONNECTION_OK,(()=>{null!==c.current&&(P(),M())}))}),[]);const B=(0,r.useCallback)((()=>{x.current&&(P(),M())}),[]);(0,r.useEffect)((()=>{w?(I.subscribe(A.MSG_QUERY_SCHEMA,(()=>{P(),M()})),window.addEventListener("focus",B),_(!0),x.current=!0):v&&(I.unsubscribe(A.MSG_QUERY_SCHEMA),window.removeEventListener("focus",B),_(!1),x.current=!1)}),[w]);const F=(0,r.useMemo)((()=>d?[...d.filter((e=>!f?.find((t=>t.view_name===e.table_name)))).map((e=>({name:e.table_name,type:"table"}))),...f?.map((e=>({name:e.view_name,type:"matview"})))??[]]:[]),[d,f]),H=(0,r.useMemo)((()=>m?.filter((e=>e.suspended)).length??0),[m]);return(0,r.useEffect)((()=>{0===H&&E&&y(!1)}),[H,E]),r.createElement(Wd,$d({ref:e},t),r.createElement(Be.Header,{afterTitle:r.createElement("div",{style:{display:"flex",marginRight:"1rem",justifyContent:"space-between",flex:1}},r.createElement(oc,{suspendedTablesCount:H,filterSuspendedOnly:E,setFilterSuspendedOnly:y}),d&&r.createElement(ke,{align:"center",gap:"0"},k&&r.createElement(dt,{delay:350,placement:"bottom",trigger:r.createElement(Dn.z,{skin:"transparent","data-hook":"schema-copy-to-clipboard-button",disabled:0===O.length,onClick:async()=>{if(!d)return;let e=[];const t=await Promise.all(O.map((async t=>{try{const e="table"===t.type?await i.showTableDDL(t.name):await i.showMatViewDDL(t.name);if(e&&e.type===T.DQL)return e.data[0].ddl}catch(n){e.push(t)}})));0===e.length?(p(t.join("\n\n")),b($t({content:r.createElement(Pe,{color:"foreground"},"Schemas copied to clipboard")}))):b($t({content:r.createElement(Pe,{color:"red"},"Cannot copy schemas from tables:"," ",e.sort().join(", ")),type:Zt.ERROR})),N(!1)}},r.createElement(mo.f,{size:"18px"}))},r.createElement(_t,null,"Copy schemas to clipboard")),k&&r.createElement(dt,{delay:350,placement:"bottom",trigger:r.createElement(Dn.z,{skin:"transparent","data-hook":"schema-select-all-button",onClick:()=>{O.length===F.length?R([]):R(F)}},r.createElement(Ac,{visible:!0,checked:O.length===F.length}))},r.createElement(_t,null,O.length===F.length?"Deselect":"Select"," ","all")),k&&r.createElement(dt,{delay:350,placement:"bottom",trigger:r.createElement(Dn.z,{"data-hook":"schema-cancel-select-button",skin:"transparent",onClick:()=>{R([]),N(!1)}},r.createElement(_e.x,{size:"18px"}))},r.createElement(_t,null,"Cancel")),!k&&r.createElement(dt,{delay:350,placement:"bottom",trigger:r.createElement(Dn.z,{"data-hook":"schema-add-metrics-button",skin:"transparent",onClick:async()=>{const e=Ko.find((e=>"now-1h"===e.dateFrom&&"now"===e.dateTo));await S({metricsViewState:{metrics:[],dateFrom:e.dateFrom,dateTo:e.dateTo,refreshRate:Jo.AUTO,viewMode:Xo.GRID}})}},r.createElement(hi.J,{size:"20px"}))},r.createElement(_t,null,"Add metrics")),!k&&r.createElement(dt,{delay:350,placement:"right",trigger:r.createElement(Yd,$d({"data-hook":"schema-select-button",onClick:()=>{k&&R([]),N(!k)}},k?{className:"selected"}:{},{selected:k,disabled:0===d?.length}),r.createElement(js.e,{size:"18px"}))},r.createElement(_t,null,"Select")),!k&&r.createElement(dt,{delay:350,placement:"right",trigger:r.createElement(Yd,{"data-hook":"schema-auto-refresh-button",onClick:()=>{C(L.AUTO_REFRESH_TABLES,!w),P(),M()},selected:w},r.createElement(_r.h,{size:"18px"}))},r.createElement(_t,null,"Auto refresh"," ",w?"enabled":"disabled"))))}),r.createElement(Qd,{_loading:n.view===Kd.loading},r.createElement(Vd,{tables:d??[],walTables:m,materializedViews:f,filterSuspendedOnly:E,state:n,loadingError:l})))},tu=(e,t)=>r.createElement(Ks,null,r.createElement(eu,$d({},e,{innerRef:t}))),nu=(0,r.forwardRef)(tu);var ru=n(83381);const au=s.ZP.div.withConfig({displayName:"start__Items"})(["\n  display: grid;\n  grid-auto-flow: row;\n  grid-auto-columns: max-content;\n  gap: 4rem;\n  text-align: center;\n  justify-items: center;\n"]),ou=(0,s.ZP)(ru.X).withConfig({displayName:"start__StyledHeading"})(["\n  color: ",";\n"],(({theme:e})=>e.color.foreground)),iu=s.ZP.div.withConfig({displayName:"start__Actions"})(["\n  display: grid;\n  gap: 2rem;\n  grid-template-columns: repeat(2, 1fr);\n"]),lu=(0,s.ZP)(Pn.x).attrs({flexDirection:"column",gap:"2rem"}).withConfig({displayName:"start__Action"})(["\n  padding: 2rem;\n  border-radius: ",";\n  background: #2c2e3d;\n  cursor: pointer;\n\n  &,\n  &:hover {\n    ",";\n  }\n\n  > * {\n    opacity: 0.8;\n  }\n\n  &:hover {\n    background: #3f4252;\n\n    > * {\n      opacity: 1;\n    }\n  }\n"],(({theme:e})=>e.borderRadius),ee),su=()=>{const e=(0,o.I0)();return r.createElement(au,null,r.createElement(ou,{level:3},"Enter a query and press ",r.createElement(Pe,{color:"green"},"Run")," to view results."),r.createElement(iu,null,r.createElement(lu,{onClick:()=>e(Gt.setActiveBottomPanel("import"))},r.createElement("img",{alt:"File upload icon",width:"60",height:"80",src:"assets/upload.svg"}),r.createElement(ru.X,{level:5},"Import CSV")),r.createElement(lu,{onClick:()=>e(Gt.setActiveSidebar("create"))},r.createElement("img",{alt:"Create table icon",width:"60",height:"80",src:"assets/create-table.svg"}),r.createElement(ru.X,{level:5},"Create table"))))},cu=(0,s.ZP)(it).withConfig({displayName:"zero-state__StyledPaneContent"})(["\n  align-items: center;\n  justify-content: center;\n"]),du=()=>{const{quest:e}=(0,r.useContext)(qr),[t,n]=(0,r.useState)(!0),[a,o]=(0,r.useState)([]);return(0,r.useEffect)((()=>{(async()=>{try{const t=await e.showTables();t&&t.type===T.DQL&&o(t.data)}catch(e){return}finally{n(!1)}})()}),[]),r.createElement(lt,null,r.createElement(cu,null,!t&&a.length<=2&&r.createElement(su,null)))},uu=(0,s.ZP)(ke).attrs({flexDirection:"column"}).withConfig({displayName:"Sidebar"})(["\n  padding-top: ",";\n  width: 4.5rem;\n  height: 100%;\n  background: ",";\n  gap: 1rem;\n  flex-shrink: 0;\n  justify-content: ",";\n  align-items: center;\n"],(({align:e})=>"top"===e?"0.5rem":"0"),(({theme:e})=>e.color.backgroundDarker),(({align:e})=>"top"===e?"flex-start":"flex-end")),mu=(0,s.ZP)(Et).withConfig({displayName:"navigation__Navigation"})(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n\n  & > span {\n    margin-left: 0 !important;\n  }\n\n  & > :not(:first-child) {\n    margin-top: 0.3rem;\n  }\n"]);var pu=n(61520),gu=n(29155),fu=n(92973),hu=n(16836);const bu=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],Eu=(e,t=0,n=1e3)=>e<n?`${((e,t=2)=>Math.round((e+Number.EPSILON)*10**t)/10**t)(e)} ${bu[t]}`:Eu(e/n,t+1);var yu=n(36607);const wu=(0,s.ZP)(po.e).withConfig({displayName:"file-status__CheckboxCircleIcon"})(["\n  color: ",";\n"],(({theme:e})=>e.color.green)),Cu=(0,s.ZP)(ke).withConfig({displayName:"file-status__StyledBox"})(["\n  gap: 0.5rem;\n  white-space: nowrap;\n"]),vu=({file:e})=>{const t=(e=>{if(!e.isUploading&&e.uploaded&&e.uploadResult)return{label:`Imported ${e.uploadResult.rowsImported.toLocaleString()} row${e.uploadResult.rowsImported>1||0===e.uploadResult.rowsImported?"s":""}`,type:Nt.SUCCESS,icon:r.createElement(wu,{size:"16px"})};if(e.error)return{label:"Upload error",type:Nt.ERROR};if(e.isUploading)return{label:`Uploading: ${e.uploadProgress.toFixed(2)}%`,type:Nt.WARNING};switch(e.status){case O.EXISTS:return{label:"Table already exists",type:Nt.WARNING};case O.RESERVED_NAME:return{label:"Reserved table name",type:Nt.ERROR};case O.DOES_NOT_EXIST:return{label:"Ready to upload",type:Nt.SUCCESS}}})(e);return t?r.createElement(ke,{gap:"1rem",align:"flex-start",flexDirection:"column"},r.createElement(yu.C,{type:t.type},r.createElement(Cu,null,t.icon," ",t.label))):r.createElement(r.Fragment,null)};var _u=n(90342),xu=n(54830),Su=n(30942),ku=n(7218);const Tu="yyyy-MM-ddTHH:mm:ss.SSSUUUz",Nu=5e5,Ou=(0,s.ZP)(ku.O).withConfig({displayName:"upload-settings-dialog__SettingsIcon"})(["\n  color: ",";\n"],(({theme:e})=>e.color.foreground)),Ru=(0,s.ZP)(ke).attrs({justifyContent:"space-between",gap:"2rem"}).withConfig({displayName:"upload-settings-dialog__Row"})(["\n  width: 100%;\n"]),Iu=(0,s.ZP)(ke).attrs({gap:"0",flexDirection:"column"}).withConfig({displayName:"upload-settings-dialog__Items"})(["\n  height: 100%;\n"]),Au=(0,s.ZP)(ke).attrs({gap:"0",flexDirection:"column"}).withConfig({displayName:"upload-settings-dialog__Inputs"})(["\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n"]),Lu=(0,s.ZP)(Ue.GroupItem).withConfig({displayName:"upload-settings-dialog__Option"})(["\n  width: 100%;\n\n  &:nth-child(even) {\n    background: #242531;\n  }\n"]),Pu=s.ZP.div.withConfig({displayName:"upload-settings-dialog__InputWrapper"})(["\n  flex-shrink: 0;\n  width: 30%;\n"]),Du=({file:e,open:t,onOpenChange:n,onSubmit:a})=>{const{delimiter:o,overwrite:i,forceHeader:l,skipLev:s,atomicity:c,maxUncommitedRows:d}=e.settings,u={delimiter:o,overwrite:i,forceHeader:l,skipLev:s,atomicity:c,maxUncommitedRows:d},[m,p]=r.useState(u),g=[{type:"input",name:"maxUncommitedRows",label:"Maximum number of uncommited rows",placeholder:Nu.toString(),description:r.createElement(r.Fragment,null,"Set this parameter to determine the size of the commit batch based on the RAM size of the machine to avoid running out of memory during an import."),defaultValue:m.maxUncommitedRows},{type:"input",name:"delimiter",label:"Delimiter",placeholder:"Automatic",description:r.createElement(r.Fragment,null,"Delimiter character. When not set, import will try to detect the delimiter automatically. You can define the parameter here to disable auto-detection and allow single-line file import."),defaultValue:m.delimiter},{type:"select",name:"atomicity",label:"Atomicity",description:r.createElement(r.Fragment,null,"Behavior when an error is detected in the data.",r.createElement("br",null),r.createElement("strong",null,"Abort"),": the entire file will be skipped.",r.createElement("br",null),r.createElement("strong",null,"Skip row"),": the row is skipped.",r.createElement("br",null),r.createElement("strong",null,"Skip column"),": the column is skipped."),defaultValue:m.atomicity,options:[{label:"Skip column",value:"skipCol"},{label:"Skip row",value:"skipRow"},{label:"Abort import",value:"abort"}]},{type:"switch",name:"forceHeader",label:"Force header",description:r.createElement(r.Fragment,null,"When set to ",r.createElement("strong",null,"false"),", QuestDB will try to infer if the first line of the file is the header line.",r.createElement("br",null),"When set to ",r.createElement("strong",null,"true"),", QuestDB will expect that line to be the header line."),defaultValue:m.forceHeader},{type:"switch",name:"skipLev",label:"Skip line extra values",description:r.createElement(r.Fragment,null,"When set to ",r.createElement("strong",null,"true"),", the parser will ignore extra values. When set to ",r.createElement("strong",null,"false"),", the parser will ignore the entire line. An extra value is something in addition to what is defined by the header."),defaultValue:m.skipLev}];return r.createElement(Ue,{open:t,title:r.createElement(ke,null,r.createElement(Ou,{size:"20px"}),r.createElement(Pe,{color:"foreground"},"Settings for ",e.fileObject.name)),trigger:r.createElement(Dn.z,{skin:"secondary",prefixIcon:r.createElement(ku.O,{size:"18px"}),onClick:()=>n(!0)},"Settings"),onDismiss:()=>{p(u),n(!1)},afterTitle:r.createElement(ke,{gap:"1rem"},r.createElement(Dn.z,{prefixIcon:r.createElement(xd.W,{size:18}),skin:"secondary",onClick:()=>{p(u),n(!1)},type:"button"},"Dismiss"),r.createElement(Dn.z,{prefixIcon:r.createElement(ku.O,{size:18}),skin:"success",onClick:()=>{a(m),n(!1)}},"Submit"))},r.createElement(Ue.ContentWrapper,null,r.createElement(Iu,null,r.createElement(Au,null,g.map((e=>r.createElement(Lu,{key:e.name},r.createElement(Ru,{key:e.name},r.createElement(ke,{gap:"1rem",flexDirection:"column",align:"flex-start",justifyContent:"flex-start"},r.createElement(Pe,{color:"foreground",weight:600},e.label),e.description&&r.createElement(Pe,{color:"gray2",size:"sm"},e.description)),r.createElement(Pu,null,"input"===e.type&&r.createElement(Gn.I,{name:e.name,defaultValue:e.defaultValue,placeholder:e.placeholder,onChange:t=>p({...m,[e.name]:t.target.value})}),"select"===e.type&&r.createElement(er.P,{name:e.name,defaultValue:e.defaultValue,onChange:t=>p({...m,[e.name]:t.target.value}),options:e.options}),"switch"===e.type&&r.createElement(Su.r,{checked:e.defaultValue,onChange:t=>p({...m,[e.name]:t})}))))))))))},Mu=({file:e,onUpload:t,onRemove:n,onSettingsChange:a})=>{const[o,i]=(0,r.useState)(!1);return r.createElement(ke,{gap:"1rem",align:"center"},r.createElement(Du,{open:o,onOpenChange:i,onSubmit:a,file:e}),r.createElement(Dn.z,{disabled:e.isUploading,skin:"primary",prefixIcon:r.createElement(xu.C,{size:"18px"}),onClick:()=>t(e.id)},e.isUploading?"Uploading...":"Upload"),r.createElement(dt,{placement:"top",trigger:r.createElement(Dn.z,{disabled:e.isUploading,skin:"secondary",onClick:()=>{n(e.id)}},r.createElement(_e.x,{size:"18px"}))},r.createElement(_t,null,"Remove file from queue")))};var Bu=n(3512);const Fu=e=>{const t=e.length;if(t>127)return!1;if(["telemetry","telemetry_config"].includes(e))return!1;for(let n=0;n<t;n++)switch(e.charAt(n)){case".":if(0==n||n==t-1||"."==e.charAt(n-1))return!1;break;case"?":case",":case"'":case'"':case"\\":case"/":case":":case")":case"(":case"+":case"*":case"%":case"~":case"\0":case"":case"":case"":case"":case"":case"":case"":case"\b":case"\t":case"\v":case"\f":case"\r":case"\n":case"":case"":case"":case"0xfeff":return!1}return e.length>0&&" "!=e.charAt(0)&&" "!=e.charAt(t-1)},Hu=s.ZP.ul.withConfig({displayName:"rename-table-dialog__List"})(["\n  list-style-position: inside;\n  padding-left: 0;\n\n  li {\n    margin-bottom: 1rem;\n  }\n"]),Vu=(0,s.ZP)(fi.V.Description).withConfig({displayName:"rename-table-dialog__StyledDescription"})(["\n  display: grid;\n  gap: 2rem;\n"]),zu=sr().object({name:sr().string().required().custom(((e,t)=>Fu(e)?e:t.error("string.validTableName"))).messages({"string.empty":"Please enter a name","string.validTableName":"Invalid table name"})}),Zu=({open:e,onOpenChange:t,onNameChange:n,file:a})=>{const o=a.table_name??a.fileObject.name;return r.createElement(fi.V.Root,{open:e},r.createElement(fi.V.Trigger,{asChild:!0},r.createElement(Ce.A,null,r.createElement(Dn.z,{skin:"transparent",prefixIcon:r.createElement(Bu.I,{size:"14px"}),onClick:()=>t(a)},D(o,20)))),r.createElement(fi.V.Portal,null,r.createElement(Ce.A,null,r.createElement(ve.a,{primitive:fi.V.Overlay})),r.createElement(fi.V.Content,{onEscapeKeyDown:()=>t(void 0),onInteractOutside:()=>t(void 0)},r.createElement(ir,{name:"rename-table",defaultValues:{name:o},onSubmit:e=>{n(e.name),t(void 0)},validationSchema:zu},r.createElement(fi.V.Title,null,r.createElement(ke,null,r.createElement(Bu.I,{size:20}),"Change table name")),r.createElement(Vu,null,r.createElement(ir.Item,{name:"name",label:"Table name"},r.createElement(ir.Input,{name:"name"})),r.createElement(Pe,{color:"foreground"},r.createElement(Hu,null,r.createElement("li",null,"Max 127 characters"),r.createElement("li",null,"Must not contain dot at the beginning"),r.createElement("li",null,"Must not contain the following characters:"," ",r.createElement("strong",null,"? , ' \" \\ / : ) ( + * & ~ \r \n")),r.createElement("li",null,"No control characters and UTF-8 BOM (Byte Order Mark)"),r.createElement("li",null,"Cannot be named ",r.createElement("strong",null,"telemetry")," or"," ",r.createElement("strong",null,"telemetry_config"))))),r.createElement(fi.V.ActionButtons,null,r.createElement(fi.V.Close,{asChild:!0},r.createElement(Dn.z,{prefixIcon:r.createElement(xd.W,{size:18}),skin:"secondary",onClick:()=>t(void 0)},"Dismiss")),r.createElement(fi.V.Close,{asChild:!0},r.createElement(Ce.A,null,r.createElement(ir.Submit,{prefixIcon:r.createElement(Bu.I,{size:18}),variant:"success"},"Change"))))))))},Uu="yyyy-MM-ddTHH:mm:ss.SSSUUUz",qu=s.ZP.div.withConfig({displayName:"controls__Controls"})(["\n  display: grid;\n  grid-template-columns: var(--columns);\n  gap: 1rem;\n  width: 100%;\n  align-items: flex-end;\n  justify-items: center;\n"]),Gu=({url:e,text:t,tooltipText:n="Documentation"})=>r.createElement(dt,{placement:"bottom",trigger:r.createElement("a",{href:e,target:"_blank",rel:"noopener noreferrer"},r.createElement(Dn.z,{skin:"transparent",type:"button"},r.createElement(It.d,{size:"14"}),t&&r.createElement("span",null,t)))},r.createElement(_t,null,n));function $u(){return $u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$u.apply(this,arguments)}const ju=[{label:"AUTO",value:""},{label:"BINARY",value:"BINARY"},{label:"BOOLEAN",value:"BOOLEAN"},{label:"BYTE",value:"BYTE"},{label:"CHAR",value:"CHAR"},{label:"DATE",value:"DATE"},{label:"DOUBLE",value:"DOUBLE"},{label:"FLOAT",value:"FLOAT"},{label:"GEOHASH",value:"GEOHASH"},{label:"INT",value:"INT"},{label:"IPV4",value:"IPV4"},{label:"LONG",value:"LONG"},{label:"LONG256",value:"LONG256"},{label:"SHORT",value:"SHORT"},{label:"STRING",value:"STRING"},{label:"VARCHAR",value:"VARCHAR"},{label:"SYMBOL",value:"SYMBOL"},{label:"TIMESTAMP",value:"TIMESTAMP"},{label:"UUID",value:"UUID"}],Wu=(0,s.ZP)(Pe).attrs({color:"foreground"}).withConfig({displayName:"column__IndexNumber"})([""]),Qu=s.ZP.div.withConfig({displayName:"column__Root"})(["\n  display: grid;\n  gap: 1rem;\n  grid-template-columns: 40px auto;\n  padding: 2rem;\n  ",";\n"],(({odd:e})=>e&&"background-color: #242531;")),Yu=(0,s.ZP)(ke).attrs({gap:"0",align:"center",justifyContent:"center"}).withConfig({displayName:"column__Index"})(["\n  width: 4rem;\n  height: 3rem;\n"]),Ku=(0,s.ZP)(qu).withConfig({displayName:"column__TimestampControls"})(["\n  justify-items: flex-start;\n"]),Xu=({action:e,disabled:t,column:n,index:a,onSetTimestamp:o,onFocus:i,timestamp:l,lastFocusedIndex:s})=>{const[c,d]=(0,r.useState)(n.name);return n?r.createElement(Qu,{key:n.name,odd:a%2!=0,disabled:t,onFocus:()=>i(a)},r.createElement(Yu,null,r.createElement(Wu,{color:"foreground"},a+1)),r.createElement(ke,{flexDirection:"column",gap:"1rem",align:"stretch"},r.createElement(qu,null,r.createElement(ir.Item,{name:`schemaColumns.${a}.name`},r.createElement(ir.Input,$u({placeholder:"Name",disabled:t,defaultValue:n.name,onChange:e=>d(e.target.value),name:`schemaColumns.${a}.name`,autoComplete:"off",required:!0},s===a&&{autoFocus:!0}))),r.createElement(ir.Item,{name:`schemaColumns.${a}.type`},r.createElement(ir.Select,{defaultValue:"import"===e?n.type:"VARCHAR",name:`schemaColumns.${a}.type`,options:ju.filter((t=>""!==t.value||"add"!==e))}))),"TIMESTAMP"===n.type&&r.createElement(ke,{flexDirection:"column",gap:"1rem"},r.createElement(Ku,null,"import"===e&&r.createElement(ir.Item,{name:`schemaColumns.${a}.pattern`},r.createElement(ir.Input,{name:`schemaColumns.${a}.pattern`,placeholder:"Pattern",defaultValue:""!==n.pattern?n.pattern:Uu,required:!0})),r.createElement(Xe,{icon:r.createElement(Dn.z,{disabled:""===c,skin:""!==l&&""!==n.name&&l===n.name?"success":"secondary",onClick:()=>o(n.name),type:"button",prefixIcon:r.createElement("input",{type:"checkbox",checked:""!==l&&""!==n.name&&l===n.name,onChange:()=>{}})},"Designated"),tooltip:"Mark this column as a designated timestamp",placement:"top"})),"import"===e&&r.createElement(Pe,{color:"gray2"},"Example: ",Uu,r.createElement("br",null),r.createElement(Gu,{url:"https://questdb.io/docs/reference/function/date-time/#timestamp-format",text:"Timestamp format docs",tooltipText:"Timestamp format documentation"}))),"GEOHASH"===n.type&&r.createElement(qu,null,r.createElement(ir.Item,{name:`schemaColumns.${a}.precision`},r.createElement(ir.Input,{name:`schemaColumns.${a}.precision`,placeholder:"Precision",required:!0})),r.createElement(Gu,{url:"https://questdb.io/docs/concept/geohashes/#specifying-geohash-precision",text:"Docs",tooltipText:"GEOHASH type documentation"})))):null},Ju=(0,s.ZP)(ke).attrs({align:"center",gap:"1.5rem"}).withConfig({displayName:"columns__Disclaimer"})(["\n  width: 100%;\n  padding: 2rem;\n  color: ",";\n"],(({theme:e,isEditLocked:t})=>e.color[t?"orange":"foreground"])),em=s.ZP.div.withConfig({displayName:"columns__SchemaRoot"})(["\n  width: 100%;\n  height: 100%;\n"]),tm=(0,s.ZP)(Ue.GroupItem).attrs({direction:"column"}).withConfig({displayName:"columns__Error"})(["\n  align-items: center;\n"]),nm=({action:e,isEditLocked:t,onColumnFocus:n,lastFocusedIndex:a})=>{const{formState:o,getValues:i,setValue:l,watch:s}=(0,Fn.Gc)(),c=s("timestamp"),d=i().schemaColumns,u=(0,r.useCallback)((o=>{const i=s(`schemaColumns.${o}`);return r.createElement(r.Fragment,null,r.createElement(Xu,{action:e,column:i,disabled:t,index:o,lastFocusedIndex:a,onFocus:n,onSetTimestamp:e=>{l("timestamp",c===e?"":e)},timestamp:c}))}),[c,d.length,d.map((e=>e.type)).join(",")]),m=o.errors.schemaColumns;return r.createElement(r.Fragment,null,"import"===e&&!t&&r.createElement(Ju,{isEditLocked:!1},r.createElement(It.d,{size:"20px"}),r.createElement(Pe,{color:"foreground"},"Column names have to match the CSV header.",r.createElement("br",null),"Order is not important.")),m&&(Array.isArray(m)?m:[m]).map(((e,t)=>r.createElement(tm,{key:t},r.createElement(Pe,{color:"red"},e.message)))),r.createElement(em,null,d.length>0&&r.createElement(kt,{itemContent:u,totalCount:d.length,followOutput:!0})),r.createElement(ir.Input,{name:"timestamp",hidden:!0}))};var rm=n(98501),am=n(72506),om=n(69968);const im=({action:e,ctaText:t,lastFocusedIndex:n,onAdded:a,isEditLocked:o})=>{const i={name:"",type:"import"===e?"":"VARCHAR",pattern:"",precision:""},{insert:l}=(0,Fn.Dq)({name:"schemaColumns"}),{getValues:s,setValue:c}=(0,Fn.Gc)(),d=s().schemaColumns;return r.createElement(ke,{gap:"1rem"},!o&&r.createElement(r.Fragment,null,r.createElement(dt,{trigger:r.createElement(Dn.z,{disabled:0===d.length||void 0===n,skin:"secondary",type:"button",onClick:()=>{c("schemaColumns",d.filter(((e,t)=>t!==n))),a(void 0)}},r.createElement(rm.X,{size:"18px"})),placement:"bottom"},r.createElement(_t,null,void 0!==n&&`Remove column ${n+1}`)),r.createElement(dt,{trigger:r.createElement(Dn.z,{disabled:0===d.length||void 0===n,skin:"secondary",type:"button",onClick:()=>{const e=void 0!==n?n:d.length;l(e,i),a(e)}},r.createElement(am.v,{size:"20px"})),placement:"bottom"},r.createElement(_t,null,void 0!==n?`Insert column above ${n+1}`:"Insert column")),r.createElement(dt,{trigger:r.createElement(Dn.z,{skin:"secondary",type:"button",onClick:()=>{const e=void 0!==n?n+1:d.length;l(e,i),a(e)}},r.createElement(om.D,{size:"20px"})),placement:"bottom"},r.createElement(_t,null,void 0!==n?`Insert column below ${n+1}`:"Insert column"))),r.createElement(ir.Submit,{prefixIcon:r.createElement(el.i,{size:18}),variant:"success"},t))},lm=(0,s.ZP)(Ue.ContentWrapper).withConfig({displayName:"dialog__StyledContentWrapper"})(["\n  --columns: auto 120px; /* magic numbers to fit input, type dropdown and remove button nicely */\n"]),sm=(0,s.ZP)(ke).attrs({gap:"0",flexDirection:"column"}).withConfig({displayName:"dialog__Items"})(["\n  height: 100%;\n"]),cm=(0,s.ZP)(ke).attrs({gap:"0",flexDirection:"column"}).withConfig({displayName:"dialog__Inputs"})(["\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n"]),dm=s.ZP.div.withConfig({displayName:"dialog__Controls"})(["\n  display: grid;\n  grid-template-columns: ",";\n  gap: 1rem;\n  align-items: flex-start;\n  width: 100%;\n"],(({action:e})=>"add"===e?"auto 120px 120px":"1fr")),um=["NONE","HOUR","DAY","MONTH","YEAR"],mm=({action:e,name:t,schema:n,partitionBy:a,timestamp:i,ttlValue:l,ttlUnit:s,open:c,isEditLocked:d,hasWalSetting:u,walEnabled:m,onOpenChange:p,onSchemaChange:g,trigger:f,tables:h,ctaText:b})=>{const E={name:t,schemaColumns:n,partitionBy:a,timestamp:i,ttlValue:l,ttlUnit:s,walEnabled:u?"false":void 0},[y,w]=(0,r.useState)(E),[C,v]=(0,r.useState)(E),[_,x]=(0,r.useState)(),S=(0,o.I0)(),k=()=>{w({name:t,schemaColumns:n,partitionBy:a,timestamp:i,ttlValue:l,ttlUnit:s,walEnabled:u&&void 0!==m?m.toString():void 0})},T=sr().object({name:sr().string().required().custom(((t,n)=>Fu(t)?"add"===e&&h?.find((e=>e.table_name.toLowerCase()===t.toLowerCase()))?n.error("string.uniqueTableName"):t:n.error("string.validTableName"))).messages({"string.empty":"Please enter a name","string.validTableName":"Invalid table name","string.uniqueTableName":"Table name must be unique"}),partitionBy:sr().string().required().custom(((e,t)=>"NONE"!==e&&""===C.timestamp?t.error("string.timestampRequired"):e)).messages({"string.timestampRequired":"Designated timestamp is required when partitioning is set to anything other than NONE"}),ttlValue:sr().number(),ttlUnit:sr().string(),walEnabled:sr().any().allow("true","false").empty(),timestamp:sr().string().allow(""),schemaColumns:sr().array().custom(((t,n)=>"add"===e&&0===t.length?n.error("array.required"):t)).unique(((e,t)=>e.name===t.name)).messages({"array.required":"Please add at least one column","array.unique":"Column names must be unique"})});(0,r.useEffect)((()=>{n&&k()}),[n]),(0,r.useEffect)((()=>{c&&x(void 0)}),[c]);const N=y.schemaColumns.length;return r.createElement(Ue,{mode:"add"===e?"side":"modal",open:c,trigger:f??r.createElement(Dn.z,{skin:N>0?"transparent":"secondary",prefixIcon:N>0?r.createElement(Bu.I,{size:"18px"}):r.createElement(el.i,{size:"18px"}),onClick:()=>p(t)},N>0?`${N} col${N>1?"s":""}`:"Add"),onDismiss:()=>{k(),p(void 0)},onOpenChange:t=>{t&&"add"===e&&S(Gt.setActiveSidebar(t?"create":void 0))}},r.createElement(lm,{mode:"add"===e?"side":"modal","data-hook":"create-table-panel"},r.createElement(ir,{name:"table-schema",defaultValues:y,onSubmit:e=>{g(e),p(void 0)},onChange:e=>v(e),validationSchema:T},r.createElement(Be.Header,{title:""!==t?`Table schema for ${t}`:"Create table",afterTitle:r.createElement(im,{ctaText:b,action:e,isEditLocked:d,lastFocusedIndex:_,onAdded:x})}),r.createElement(sm,null,r.createElement(cm,null,r.createElement(Ue.GroupItem,{direction:"column"},r.createElement(dm,{action:e},"add"===e&&r.createElement(ir.Item,{name:"name",label:"Table name"},r.createElement(ir.Input,{name:"name",autoComplete:"off",autoFocus:!0})),r.createElement(ke,{align:"flex-end"},r.createElement(ir.Item,{name:"partitionBy",label:r.createElement(dt,{trigger:r.createElement(ke,{align:"center",justifyContent:"center",gap:"0.5rem"},r.createElement(pc.Z,{size:"14"}),r.createElement("span",null,"Partition by")),placement:"bottom"},r.createElement(_t,null,"Splits data into smaller chunks by intervals of time in order to improve the performance and scalability of the database system."))},r.createElement(ir.Select,{name:"partitionBy",options:um.map((e=>({label:e,value:e})))}))),u&&r.createElement(ke,{align:"flex-end"},r.createElement(ir.Item,{name:"walEnabled",label:r.createElement(dt,{trigger:r.createElement(ke,{align:"center",justifyContent:"center",gap:"0.5rem"},r.createElement(pc.Z,{size:"14"}),r.createElement("span",null,"WAL")),placement:"bottom"},r.createElement(_t,null,"WAL (Write-Ahead Log) allows concurrent data ingestion and modifications via multiple interfaces as well as table schema changes.","NONE"===C.partitionBy&&r.createElement(r.Fragment,null,r.createElement("br",null),r.createElement("br",null),"To enable WAL, set `Partition by` to a value other than NONE.")))},r.createElement(ir.Select,{name:"walEnabled",disabled:"NONE"===C.partitionBy,options:[{label:"Enabled",value:"true"},{label:"Disabled",value:"false"}]}))),"import"===e&&r.createElement(Pe,{color:"gray2"},"If you're changing the partitioning strategy, you'll need to set `Write mode` to `Overwrite` in Settings."))),r.createElement(Ue.GroupHeader,null,r.createElement(Pe,{color:"foreground"},"Columns")),r.createElement(nm,{action:e,isEditLocked:d,onColumnFocus:x,lastFocusedIndex:_}))))))};var pm=n(82589);const gm=(0,s.ZP)(pm.o).withConfig({displayName:"upload-result-dialog__SearchIcon"})(["\n  color: ",";\n"],(({theme:e})=>e.color.foreground)),fm=(0,s.ZP)(hu.i).withConfig({displayName:"upload-result-dialog__StyledTable"})(["\n  width: 100%;\n  table-layout: fixed;\n  border-collapse: separate;\n  border-spacing: 0 2rem;\n  padding: 0 2rem;\n\n  th {\n    padding: 0 1.5rem;\n    color: ",";\n  }\n\n  td {\n    padding: 1.5rem;\n  }\n\n  tbody td {\n    background: #242531;\n\n    &:first-child {\n      border-top-left-radius: ",";\n      border-bottom-left-radius: ",";\n    }\n\n    &:last-child {\n      border-top-right-radius: ",";\n      border-bottom-right-radius: ",";\n    }\n  }\n"],(({theme:e})=>e.color.foreground),(({theme:e})=>e.borderRadius),(({theme:e})=>e.borderRadius),(({theme:e})=>e.borderRadius),(({theme:e})=>e.borderRadius)),hm=(0,s.ZP)(ke).attrs({justifyContent:"space-between",gap:"2rem"}).withConfig({displayName:"upload-result-dialog__Stat"})(["\n  width: 100%;\n"]),bm=(0,s.ZP)(Dn.z).withConfig({displayName:"upload-result-dialog__DetailsButton"})(["\n  position: relative;\n"]),Em=s.ZP.span.withConfig({displayName:"upload-result-dialog__NotificationCircle"})(["\n  position: absolute;\n  right: -0.4rem;\n  top: -0.4rem;\n  width: 0.8rem;\n  height: 0.8rem;\n  border-radius: 50%;\n  background-color: ",";\n"],(({theme:e})=>e.color.red)),ym=({file:e})=>{const t=e.table_name??e.fileObject.name,n=[{label:"Header forced",value:e.uploadResult?.header.toString()},{label:"Table name",value:e.uploadResult?.location},{label:"Imported rows",value:e.uploadResult?.rowsImported.toLocaleString()},{label:"Rejected rows",value:e.uploadResult?.rowsRejected.toLocaleString()}],a=e.uploadResult?.columns.reduce(((e,t)=>e+t.errors),0)??0;return r.createElement(Ue,{title:r.createElement(ke,null,r.createElement(gm,{size:20}),r.createElement(Pe,{color:"foreground"},"Import details for ",t)),trigger:r.createElement(bm,{skin:"success",prefixIcon:r.createElement(pm.o,{size:"14px"})},a>0&&r.createElement(Em,null),"Details"),withCloseButton:!0},r.createElement(ke,{flexDirection:"column",gap:"0"},n.map((e=>r.createElement(Ue.GroupItem,{key:e.label,direction:"column"},r.createElement(hm,null,r.createElement(Pe,{color:"gray2"},e.label),r.createElement(Pe,{color:"foreground"},e.value))))),r.createElement(Ue.GroupHeader,null,r.createElement(Pe,{color:"foreground"},"Table schema")),r.createElement(fm,{columns:[{header:"Name",render:({data:e})=>r.createElement(Pe,{color:"foreground"},e.name)},{header:"Type",align:"flex-end",render:({data:e})=>r.createElement(Pe,{color:"foreground"},e.type)},{header:"Size",width:"100px",align:"flex-end",render:({data:e})=>r.createElement(Pe,{color:"foreground"},e.size.toLocaleString())},{header:"Errors",width:"100px",align:"flex-end",render:({data:e})=>r.createElement(Pe,{color:e.errors>0?"red":"foreground"},e.errors.toLocaleString())}],rows:e.uploadResult?.columns??[]})))},wm=(0,s.ZP)(ke).attrs({flexDirection:"column"}).withConfig({displayName:"dropbox__Root"})(["\n  flex: 1;\n  width: 100%;\n  padding: 4rem 0 0;\n  gap: 2rem;\n  background: ",";\n  border: 3px dashed ",";\n  box-shadow: inset 0 0 10px 0 #1b1c23;\n  transition: all 0.15s ease-in-out;\n"],(({theme:e})=>e.color.backgroundLighter),(({isDragging:e})=>e?"#7f839b":"#333543")),Cm=({files:e,onFilesDropped:t,dialogOpen:n,render:a})=>{const[o,i]=(0,r.useState)(!1),[l,s]=(0,r.useState)([]),c=(0,r.useRef)(null),d=(0,r.useRef)(e.map((e=>e.table_name))),u=e=>{e.preventDefault(),e.stopPropagation(),i("dragenter"===e.type||"dragover"===e.type)},m=e=>{const n=((e,t)=>Array.from(e).filter((e=>t.includes(e.name))))(e,d.current);s(n),t(Array.from(e).filter((e=>!n.includes(e)))),c.current?.setAttribute("value","")},p=e=>{const t=e,n=t.clipboardData?.files;n&&m(n)};return(0,r.useEffect)((()=>()=>{window.removeEventListener("paste",p)}),[]),(0,r.useEffect)((()=>{n?window.removeEventListener("paste",p):window.addEventListener("paste",p)}),[n]),(0,r.useEffect)((()=>{d.current=e.map((e=>e.table_name))}),[e]),r.createElement(wm,{onDragEnter:u,onDragOver:u,onDragLeave:u,onDrop:e=>{e.preventDefault(),e.stopPropagation(),i(!1),m(e.dataTransfer.files)},isDragging:o,"data-hook":"import-dropbox"},a({duplicates:l,addToQueue:m}))},vm=(0,s.ZP)(ke).attrs({flexDirection:"column",gap:"2rem"}).withConfig({displayName:"files-to-upload__Root"})(["\n  padding: 2rem;\n"]),_m=(0,s.ZP)(hu.i).withConfig({displayName:"files-to-upload__StyledTable"})(["\n  width: 100%;\n  table-layout: fixed;\n  border-collapse: separate;\n  border-spacing: 0 2rem;\n\n  th {\n    padding: 0 1.5rem;\n  }\n\n  td {\n    padding: 1.5rem;\n  }\n\n  tbody td {\n    background: #242531;\n\n    &:first-child {\n      border-top-left-radius: ",";\n      border-bottom-left-radius: ",";\n    }\n\n    &:last-child {\n      border-top-right-radius: ",";\n      border-bottom-right-radius: ",";\n    }\n  }\n"],(({theme:e})=>e.borderRadius),(({theme:e})=>e.borderRadius),(({theme:e})=>e.borderRadius),(({theme:e})=>e.borderRadius)),xm=(0,s.ZP)(ke).attrs({justifyContent:"center"}).withConfig({displayName:"files-to-upload__EmptyState"})(["\n  width: 100%;\n  background: #242531;\n  border-radius: ",";\n  padding: 1rem;\n"],(({theme:e})=>e.borderRadius)),Sm=(0,s.ZP)(ke).withConfig({displayName:"files-to-upload__FileTextBox"})(["\n  padding: 0 1.1rem;\n"]),km=s.ZP.span.withConfig({displayName:"files-to-upload__BrowseTextLink"})(["\n  text-decoration: underline;\n  cursor: pointer;\n\n  &:hover {\n    text-decoration: none;\n  }\n"]),Tm=({files:e,onDialogToggle:t,onFileRemove:n,onFilePropertyChange:a,onFileUpload:o,onFilesDropped:i,onViewData:l,dialogOpen:s,ownedByList:c})=>{const d=(0,r.useRef)(null),[u,m]=r.useState(),[p,g]=r.useState();(0,r.useEffect)((()=>{t(void 0!==u||void 0!==p)}),[u,p]);const f=[];return f.push({header:"File",align:"flex-start",...e.length>0&&{width:"400px"},render:({data:e})=>{const t=r.createElement(Sm,{align:"center",gap:"1rem"},r.createElement(Pe,{color:"foreground"},D(e.fileObject.name,20)),r.createElement(Pe,{color:"gray2",size:"sm"},Eu(e.fileObject.size)));return r.createElement(ke,{align:"center",gap:"1rem"},r.createElement(_u.M,{size:"46px"}),r.createElement(ke,{gap:"1rem",align:"flex-4tart",flexDirection:"column"},e.fileObject.name.length>20&&r.createElement(dt,{placement:"top",trigger:t},r.createElement(_t,null,e.fileObject.name)),e.fileObject.name.length<=20&&t,r.createElement(ke,{gap:"1rem",align:"center"},r.createElement(vu,{file:e}),!e.isUploading&&void 0!==e.uploadResult&&r.createElement(r.Fragment,null,r.createElement(ym,{file:e}),r.createElement(Dn.z,{skin:"secondary",prefixIcon:r.createElement(pu.r,{size:"18px"}),onClick:()=>l(e.uploadResult)},"Result"))),e.uploadResult&&e.uploadResult.rowsRejected>0||e.error&&r.createElement(Sm,{flexDirection:"column",gap:"1rem",align:"flex-start"},e.uploadResult&&e.uploadResult.rowsRejected>0&&r.createElement(Pe,{color:"orange",size:"sm"},e.uploadResult.rowsRejected.toLocaleString()," ","row",e.uploadResult.rowsRejected>1?"s":""," ","rejected"),e.error&&r.createElement(Pe,{color:"red",size:"sm"},e.error))))}},{header:"Table name",align:"flex-end",width:"180px",render:({data:e})=>r.createElement(Zu,{open:u===e.id,onOpenChange:e=>m(e?.id),onNameChange:t=>{a(e.id,{table_name:t})},file:e})}),c&&c.length>0&&f.push({header:r.createElement(dt,{placement:"top",trigger:r.createElement(ke,{align:"center",gap:"0.5rem","data-hook":"import-table-column-owner"},"Table owner",r.createElement(It.d,{size:"16px"}))},r.createElement(_t,null,"Required for external (non-database) users.")),align:"center",width:"150px",render:({data:e})=>r.createElement(er.P,{name:"table_owner",defaultValue:c[0]??"",onChange:t=>a(e.id,{table_owner:t.target.value}),options:Object.values(c).map((e=>({label:e,value:e})))})}),f.push({header:r.createElement(dt,{placement:"top",trigger:r.createElement(ke,{align:"center",gap:"0.5rem","data-hook":"import-table-column-schema"},"Schema",r.createElement(It.d,{size:"16px"}))},r.createElement(_t,null,"Optional. By default, QuestDB will infer schema from the CSV file structure")),align:"center",width:"150px",render:({data:e})=>{const t=e.table_name??e.fileObject.name;return r.createElement(mm,{action:"import",open:p===e.id,onOpenChange:t=>g(t?e.id:void 0),onSchemaChange:t=>{a(e.id,{schema:t.schemaColumns,partitionBy:t.partitionBy,timestamp:t.timestamp})},name:t,schema:e.schema,partitionBy:e.partitionBy,ttlValue:e.ttlValue,ttlUnit:e.ttlUnit,timestamp:e.timestamp,isEditLocked:e.exists&&e.table_name===e.fileObject.name,hasWalSetting:!1,ctaText:"Save"})}},{header:r.createElement(dt,{placement:"top",trigger:r.createElement(ke,{align:"center",gap:"0.5rem"},"Write mode",r.createElement(It.d,{size:"16px"}))},r.createElement(_t,null,r.createElement("strong",null,"Append"),": data will be appended to the set.",r.createElement("br",null),r.createElement("strong",null,"Overwrite"),": any existing data or structure will be overwritten. Required for partitioning and timestamp related changes.")),align:"center",width:"150px",render:({data:e})=>r.createElement(er.P,{name:"overwrite",defaultValue:e.settings.overwrite?"true":"false",onChange:t=>a(e.id,{settings:{...e.settings,overwrite:"true"===t.target.value}}),options:[{label:"Append",value:"false"},{label:"Overwrite",value:"true"}]})},{align:"flex-end",width:"300px",render:({data:e})=>r.createElement(Mu,{file:e,onUpload:o,onRemove:n,onSettingsChange:t=>{a(e.id,{settings:t})}})}),r.createElement(Cm,{files:e,onFilesDropped:i,dialogOpen:s,render:({duplicates:t,addToQueue:n})=>r.createElement(vm,null,r.createElement(ru.X,{level:3},"Upload queue"),r.createElement("input",{type:"file",id:"file",onChange:e=>{null!==e.target.files&&n(e.target.files)},multiple:!0,ref:d,style:{display:"none"},value:""}),r.createElement(Pe,{color:"foreground"},"You can drag and drop more files or"," ",r.createElement(km,{onClick:()=>{d.current?.click()}},"browse from disk")),t.length>0&&r.createElement(Pe,{color:"red"},"File",t.length>1?"s":""," already added to queue:"," ",t.map((e=>e.name)).join(", "),". Change target table name and try again."),r.createElement(_m,{columns:f,rows:e}),0===e.length&&r.createElement(xm,null,r.createElement(Pe,{color:"gray2",align:"center"},"No files in queue")))})};var Nm=n(23167);const Om=(0,s.ZP)(ke).attrs({flexDirection:"column",gap:"2rem"}).withConfig({displayName:"upload__Actions"})(["\n  margin: auto;\n"]),Rm=s.ZP.div.withConfig({displayName:"upload__Info"})(["\n  margin-top: 1rem;\n  padding: 2rem;\n  background: ",";\n  border-radius: ",";\n  text-align: center;\n"],(({theme:e})=>e.color.backgroundDarker),(({theme:e})=>e.borderRadius)),Im=(0,s.ZP)(vr.x).withConfig({displayName:"upload__InfoText"})(["\n  line-height: 1.75;\n  color: #8b8fa7;\n\n  a {\n    color: ",";\n  }\n"],(({theme:e})=>e.color.foreground)),Am=()=>r.createElement("a",{href:"https://questdb.io/docs/guides/import-csv/#import-csv-via-copy-sql",target:"_blank",rel:"noopener noreferrer"},"COPY"),Lm=({files:e,onFilesDropped:t,dialogOpen:n})=>{const{quest:a}=(0,r.useContext)(qr),[o,i]=(0,r.useState)(!1),l=(0,r.useRef)(null);return(0,r.useEffect)((()=>{(async()=>{try{const e=await a.query("(show parameters) where property_path ilike 'cairo.sql.copy.root'");"dql"===e.type&&e.count>0&&i(null!==e.data[0].value&&"null"!==e.data[0].value)}catch(e){return}})()}),[]),r.createElement(Cm,{files:e,onFilesDropped:t,dialogOpen:n,render:({duplicates:e,addToQueue:t})=>r.createElement(r.Fragment,null,r.createElement(Om,null,r.createElement("img",{alt:"File upload icon",width:"60",height:"80",src:"assets/upload.svg"}),r.createElement(ru.X,{level:3},"Drag CSV files here or paste from clipboard"),r.createElement("input",{type:"file",id:"file",onChange:e=>{null!==e.target.files&&t(e.target.files)},multiple:!0,ref:l,style:{display:"none"},value:""}),r.createElement(Dn.z,{onClick:()=>{l.current?.click()},prefixIcon:r.createElement(Nm.L,{size:"18px"}),skin:"secondary","data-hook":"import-browse-from-disk"},"Browse from disk"),e.length>0&&r.createElement(vr.x,{color:"red"},"File",e.length>1?"s":""," already added to queue:"," ",e.map((e=>e.name)).join(", "),". Change target table name and try again."),r.createElement(Rm,null,o?r.createElement(Im,null,"Suitable for small batches of CSV file upload.",r.createElement("br",null),"For database migrations, we recommend the ",r.createElement(Am,null)," ","command."):r.createElement(Im,null,"Note: COPY SQL is not available for CSV Import on this database. Refer ",r.createElement(Am,null)," to enable it."))))})},Pm=(0,s.ZP)(ke).attrs({gap:"4rem",flexDirection:"column"}).withConfig({displayName:"ImportCSVFiles__Root"})(["\n  flex: 1;\n"]),Dm=({onViewData:e,onUpload:t})=>{const{quest:n}=(0,r.useContext)(qr),[a,i]=(0,r.useState)([]),[l,s]=(0,r.useState)(!1),c=(0,o.v9)(Cn.query.getTables),d=(0,r.useRef)(null),u=(e=>{const[t,n]=(0,r.useState)(!1);return(0,r.useEffect)((()=>{const t=new IntersectionObserver((([e])=>n(e.isIntersecting)));return e.current&&t.observe(e.current),()=>{t.disconnect()}}),[e]),t})(d),[m,p]=(0,r.useState)("upload"),[g,f]=(0,r.useState)([]);(0,r.useEffect)((()=>{(async()=>{if("EE"===dn(L.RELEASE_TYPE))try{let e=[];const t=await n.query("select current_user()");if("dql"===t.type&&t.count>0){const r=Object.values(t.data[0])[0];if(r){dn(L.AUTH_PAYLOAD)||e.push(r);const t=await n.query(`show groups ${r}`);if("dql"===t.type&&t.count>0){const n=t.data.map((e=>Object.values(e)[0]));e=e.concat(n)}}}f(e)}catch(e){return}})()}),[]);const h=(e,t)=>{i((n=>n.map((n=>n.id===e?{...n,...t}:n))))},b=(e,t)=>{h(e.id,{isUploading:t})},E=async e=>{const t=await(async e=>await Promise.all(e.map((async e=>{const t=await n.checkCSVFile(e.name),r=t.status===O.EXISTS?await(async()=>{const t=await n.showColumns(e.name);return t&&t.type===T.DQL?t.data.map((e=>({name:e.column,type:Dc(e.type),pattern:Tu,precision:Lc(e.type)?Pc(e.type):""}))):[]})():[],a=t.status===O.EXISTS&&c?await(async()=>{const t=c.find((t=>t.table_name===e.name));return t?.partitionBy??"NONE"})():"NONE",o=t.status===O.EXISTS&&c?await(async()=>{const t=c.find((t=>t.table_name===e.name));return t?.ttlValue??0})():0,i=t.status===O.EXISTS&&c?await(async()=>{const t=c.find((t=>t.table_name===e.name));return t?.ttlUnit??"HOURS"})():"HOURS",l=t.status===O.EXISTS&&c?await(async()=>{const t=c.find((t=>t.table_name===e.name));return t?.designatedTimestamp??""})():"";return{id:Fc(),fileObject:e,table_name:e.name,table_owner:g[0],status:t.status,exists:t.status===O.EXISTS,schema:r,partitionBy:a,ttlValue:o,ttlUnit:i,timestamp:l,settings:{forceHeader:!1,overwrite:!1,skipLev:!1,delimiter:"",atomicity:"skipCol",maxUncommitedRows:Nu},isUploading:!1,uploaded:!1,uploadResult:void 0,uploadProgress:0}}))))(e);i((e=>[...e,...t])),p("list")};return(0,r.useEffect)((()=>{u&&(async()=>{const e=await Promise.all(a.map((async e=>{const t=await n.checkCSVFile(e.table_name);return{...e,status:t.status}})));i(e)})()}),[u]),r.createElement(Pm,{ref:d},"upload"===m&&r.createElement(Lm,{files:a,onFilesDropped:E,dialogOpen:l}),"list"===m&&r.createElement(Tm,{dialogOpen:l,files:a,onViewData:e,onFilesDropped:E,onDialogToggle:s,ownedByList:g,onFileUpload:async e=>{const r=a.find((t=>t.id===e));if(!r.isUploading){b(r,!0);try{const e=await n.uploadCSVFile({file:r.fileObject,name:r.table_name,owner:r.table_owner,settings:r.settings,schema:r.schema.map(Mc),partitionBy:r.partitionBy,timestamp:r.timestamp,onProgress:e=>{h(r.id,{uploadProgress:e})}});h(r.id,{uploaded:"OK"===e.status,uploadResult:"OK"===e.status?e:void 0,schema:"OK"===e.status?e.columns.map((e=>{const t=r.schema.find((t=>t.name===e.name));return{...(n=e,a=["name"],a.reduce(((e,t)=>(t in n&&(e[t]=n[t]),e)),{})),type:Dc(e.type),pattern:"TIMESTAMP"===e.type?t?.pattern?t?.pattern:Tu:"",precision:Lc(e.type)?Pc(e.type):void 0};var n,a})):r.schema,error:"OK"===e.status?void 0:e.status}),b(r,!1),t(e)}catch(e){b(r,!1),h(r.id,{uploaded:!1,uploadResult:void 0,uploadProgress:0,error:"Upload error"})}}},onFileRemove:e=>{const t=a.find((t=>t.id===e));i(a.filter((e=>e.fileObject.name!==t.fileObject.name)))},onFilePropertyChange:async(e,t)=>{const r=await Promise.all(a.map((async r=>{if(r.id===e){const e=t.table_name?await n.checkCSVFile(t.table_name):await Promise.resolve({status:r.status});return{...r,...t,status:e.status,error:t.table_name?void 0:r.error}}return r})));i(r)}}))},Mm=()=>r.createElement(lt,null,r.createElement(it,null,r.createElement(Dm,{onUpload:e=>{I.publish(A.MSG_QUERY_SCHEMA)},onViewData:e=>{"OK"===e.status&&(I.publish(A.MSG_QUERY_SCHEMA),I.publish(A.MSG_QUERY_FIND_N_EXEC,{query:`"${e.location}"`,options:{appendAt:"end"}}))}})));var Bm=n(9077);const Fm=({size:e})=>r.createElement("svg",{width:e,height:e,viewBox:"0 0 26 26",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},r.createElement("path",{d:"M25 22.3704H1V24.1481H25V22.3704Z",fill:"currentColor"}),r.createElement("path",{d:"M1 19.2593V21.037H25V19.7037L21.8889 13.4815H16.1111V16.5926H9.88889V13.4815H4.11111L1 19.2593Z",fill:"currentColor"}),r.createElement("path",{d:"M13 1.92593L6.33337 8.59259H11.2223V14.8148H14.3334V8.59259H19.6667L13 1.92593Z",fill:"currentColor"}));function Hm(){return Hm=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hm.apply(this,arguments)}const Vm=s.ZP.div.withConfig({displayName:"Console__Root"})(["\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  max-height: 100%;\n"]),zm=s.ZP.div.withConfig({displayName:"Console__Top"})(["\n  display: flex;\n  height: 100%;\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n"]),Zm=s.ZP.div.withConfig({displayName:"Console__Bottom"})(["\n  display: flex;\n  height: 100%;\n  flex: 1;\n  min-height: 0px;\n"]),Um=s.ZP.div.withConfig({displayName:"Console__Tab"})(["\n  display: flex;\n  width: calc(100% - 4.5rem);\n  height: 100%;\n  overflow: auto;\n"]),qm=[{icon:r.createElement(pu.r,{size:w}),mode:"grid",tooltipText:"Grid"},{icon:r.createElement(gu.u,{size:w}),mode:"chart",tooltipText:"Chart"}],Gm=()=>{const e=(0,o.I0)(),{sm:t}=Ye(),{editorSplitterBasis:n,resultsSplitterBasis:a,updateSettings:i}=Gd(),[l,s]=(0,r.useState)(0!==n?n:300),c=(0,o.v9)(Cn.query.getResult),d=(0,o.v9)(Cn.console.getActiveBottomPanel),{consoleConfig:u}=Ir(),[m,p]=(0,r.useState)("grid"),g=r.useRef(null),f=r.useRef(null),h=r.useRef(null),b=r.useRef(null);return(0,r.useEffect)((()=>{g.current&&c?e(Gt.setActiveBottomPanel("result")):f.current&&e(Gt.setActiveBottomPanel("zeroState"))}),[c]),(0,r.useEffect)((()=>{var e;e=d,g.current&&(g.current.style.display="result"===e?"flex":"none"),f.current&&(f.current.style.display="zeroState"===e?"flex":"none"),h.current&&(h.current.style.display="import"===e?"flex":"none")}),[d]),r.createElement(Vm,null,r.createElement(Bm.oL,{vertical:!0,onDragEnd:e=>{i(L.RESULTS_SPLITTER_BASIS,e[0])}},r.createElement(Bm.oL.Pane,{minSize:100,preferredSize:a},r.createElement(zm,null,r.createElement(uu,{align:"top"},!t&&r.createElement(dt,{placement:"bottom",trigger:r.createElement(mu,{"data-hook":"tables-panel-button",direction:"left",onClick:()=>{e(Gt.setActiveTopPanel(0===n?"tables":void 0)),i(L.EDITOR_SPLITTER_BASIS,0===n?l:0)},selected:0!==n},r.createElement(fu.L,{size:w}))},r.createElement(_t,null,0===n?"Show":"Hide"," data sources"))),r.createElement(Bm.oL,{ref:b,onDragEnd:e=>{0!==e[0]&&s(e[0]),i(L.EDITOR_SPLITTER_BASIS,e[0])},snap:!0},r.createElement(Bm.oL.Pane,{preferredSize:n,visible:0!==n&&!t,minSize:250},r.createElement(nu,null)),r.createElement(Bm.oL.Pane,null,r.createElement(ws,null))))),r.createElement(Bm.oL.Pane,{minSize:100},r.createElement(Zm,null,r.createElement(uu,{align:"bottom"},c&&qm.map((({icon:t,mode:n,tooltipText:a})=>r.createElement(dt,{key:n,placement:"right",trigger:r.createElement(mu,{"data-hook":`${n}-panel-button`,direction:"left",onClick:()=>{e(Gt.setActiveBottomPanel("result")),p(n)},selected:"result"===d&&m===n},t)},r.createElement(_t,null,a)))),r.createElement(dt,{placement:"right",trigger:r.createElement(Et,Hm({readOnly:u.readOnly},!u.readOnly&&{onClick:()=>{e(Gt.setActiveBottomPanel("import"))}},{selected:"import"===d,"data-hook":"import-panel-button"}),r.createElement(Fm,{size:w}))},r.createElement(_t,null,u.readOnly?"To use this feature, turn off read-only mode in the configuration file":"Import files from CSV"))),r.createElement(Um,{ref:g},c&&r.createElement($s,{viewMode:m})),r.createElement(Um,{ref:f},r.createElement(du,null)),r.createElement(Um,{ref:h},r.createElement(Mm,null))))))},$m=s.ZP.div.withConfig({displayName:"SideMenu__Backdrop"})(["\n  position: fixed;\n  top: 4rem;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 24;\n\n  &:hover {\n    cursor: pointer;\n  }\n"]),jm=s.ZP.div.withConfig({displayName:"SideMenu__Wrapper"})(["\n  position: fixed;\n  top: 4rem;\n  right: 0;\n  bottom: 0;\n  width: ","px;\n  background: ",";\n  border-left: 1px solid ",";\n  z-index: 25;\n\n  &.side-menu-slide-enter {\n    width: 0;\n  }\n\n  &.side-menu-slide-enter-active {\n    width: ","px;\n    transition: width ","ms;\n  }\n\n  &.side-menu-slide-exit {\n    width: ","px;\n  }\n\n  &.side-menu-slide-exit-active {\n    width: 0;\n    transition: width ","ms;\n  }\n"],280,M("backgroundDarker"),M("black"),280,K.REG,280,K.REG),Wm=()=>{const e=(0,o.v9)(Cn.console.getSideMenuOpened),t=(0,o.I0)(),n=(0,r.useCallback)((()=>{t(Gt.toggleSideMenu())}),[t]);return r.createElement(r.Fragment,null,r.createElement(ct.Z,{classNames:"fade-reg",in:e,timeout:K.REG,unmountOnExit:!0},r.createElement($m,{onClick:n})),r.createElement(ct.Z,{classNames:"side-menu-slide",in:e,timeout:K.REG,unmountOnExit:!0},r.createElement(jm,null,r.createElement(nu,null))))};var Qm=n(70775),Ym=n(79038),Km=n(60782),Xm=n(23737),Jm=n(44752),ep=n(78613),tp=n(4888);const np=(0,s.iv)(["\n  background: ",";\n"],M("selection")),rp=s.ZP.div.withConfig({displayName:"Row__Wrapper"})(["\n  display: flex;\n  height: 2.4rem;\n  padding: 0 0.6rem;\n  line-height: 2.4rem;\n  align-items: center;\n  transition: background ","ms;\n  cursor: pointer;\n  user-select: none;\n\n  ",";\n\n  > span:not(:last-child) {\n    margin-right: 0.6rem;\n  }\n"],K.FAST,(({active:e})=>e&&np)),ap=(0,s.ZP)(Pe).withConfig({displayName:"Row__Value"})(["\n  flex: 1 1 auto;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  opacity: 0.7;\n"]),op=(0,s.ZP)(tp.E).withConfig({displayName:"Row__FileIcon"})(["\n  height: 2.2rem;\n  flex: 0 0 12px;\n  margin: 0 0.6rem;\n  color: ",";\n"],M("orange")),ip=(0,s.ZP)(Pe).withConfig({displayName:"Row__Name"})(["\n  flex: 0 0 auto;\n"]),lp=({active:e,onMouseEnter:t,onMouseLeave:n,onClick:a,query:o})=>r.createElement(rp,{active:e,onClick:a,onMouseEnter:t,onMouseLeave:n},r.createElement(op,{size:"12px"}),o.name&&r.createElement(ip,{color:"foreground",size:"sm"},o.name),r.createElement(ap,{color:"foreground",size:"sm"},o.value));function sp(){return sp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},sp.apply(this,arguments)}const cp=s.ZP.div.withConfig({displayName:"QueryPicker__Wrapper"})(["\n  display: flex;\n  max-height: 650px;\n  width: 600px;\n  max-width: 100vw;\n  padding: 0.6rem 0;\n  flex-direction: column;\n  background: ",";\n  box-shadow: "," 0px 5px 8px;\n  border: 1px solid ",";\n  border-radius: 4px;\n  overflow: auto;\n"],M("backgroundDarker"),M("black"),M("black")),dp=(0,s.ZP)(Pe).withConfig({displayName:"QueryPicker__Helper"})(["\n  padding: 1rem;\n  text-align: center;\n  opacity: 0.5;\n"]),up=(0,s.ZP)(Pe).withConfig({displayName:"QueryPicker__Esc"})(["\n  padding: 0 2px;\n  background: ",";\n  border-radius: 2px;\n"],M("foreground")),mp=({type:e})=>"query"===e,pp=(0,s.ZP)(Pe).withConfig({displayName:"QueryPicker__Title"})(["\n  padding: 0.6rem 1.2rem 0.4rem;\n  margin-top: 0.6rem;\n  border-top: 1px solid ",";\n  background: ",";\n"],(({theme:e})=>e.color.selection),(({theme:e})=>e.color.black40)),gp=(0,s.ZP)(Pe).withConfig({displayName:"QueryPicker__Description"})(["\n  padding: 0 1.2rem 1rem;\n  color: ",";\n  opacity: 0.7;\n  background: ",";\n"],(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.black40)),fp=({hidePicker:e,queries:t,ref:n})=>{const a=qe("ArrowDown"),o=qe("ArrowUp"),i=qe("Enter"),[l,s]=(0,r.useState)(-1),[c,d]=(0,r.useState)([]),{appendQuery:u}=ma();(0,r.useEffect)((()=>{d((e=>{const t=[...e],n=[];let r=0;for(;t.length;){const e=t.shift();if(void 0===e.queries)n.push({type:"query",id:r,data:e});else{const a=e;n.push({type:"descriptor",id:r,data:a}),t.unshift(...a.queries)}r++}return n})(t))}),[t]);const m=(0,r.useCallback)((t=>{e(),u(t.value,{appendAt:"end"})}),[e]);return(0,r.useEffect)((()=>{if(c.length){if(a){const e=c.find(mp)?.id??0,t=c.slice(l+1).find(mp)?.id??e;s(t)}if(o){const e=[...c].reverse(),t=e.find(mp)?.id??c.length-1,n=[...c].slice(0,l).reverse().find(mp)?.id??t;s(n)}}}),[o,a,c]),(0,r.useEffect)((()=>{if(i){const e=c.find((({id:e,type:t})=>e===l&&"query"===t));e&&m(e.data)}}),[l,i,e,t,m]),r.createElement(cp,{ref:n},r.createElement(dp,{_style:"italic",color:"foreground",size:"xs"},"Navigate the list with ",r.createElement(Jm.j,{size:"16px"}),r.createElement(ep.o,{size:"16px"})," keys, exit with ",r.createElement(up,{_style:"normal",size:"ms",weight:700},"Esc")),c.map((t=>{if("query"===t.type){const n=t.data;return r.createElement(lp,{active:t.id===l,hidePicker:e,key:t.id,onClick:()=>m(n),onMouseEnter:()=>s(t.id),onMouseLeave:()=>s(-1),query:n})}const{title:n,description:a}=t.data;return r.createElement(r.Fragment,{key:t.id},r.createElement(pp,{color:"foreground",size:"md"},n),r.createElement(gp,{color:"foreground"},a))})))},hp=(e,t)=>r.createElement(fp,sp({},e,{ref:t})),bp=(0,r.forwardRef)(hp);var Ep=n(36636);const yp=(0,s.ZP)(Oe).withConfig({displayName:"Menu__Wrapper"})(["\n  background: transparent;\n  z-index: 15;\n  padding-right: 1rem;\n  flex-shrink: 0;\n\n  .algolia-autocomplete {\n    display: "," !important;\n    flex: 0 1 168px;\n  }\n"],(({_display:e})=>e)),wp=s.ZP.div.withConfig({displayName:"Menu__Separator"})(["\n  flex: 1;\n"]),Cp=(0,s.ZP)(Dn.z).withConfig({displayName:"Menu__QueryPickerButton"})(["\n  position: relative;\n  margin: 0 1rem;\n  flex: 0 0 auto;\n\n  ","\n"],(({$firstTimeVisitor:e})=>e&&'&:after {\n    border-radius: 50%;\n    content: "";\n    background: #dc4949;\n    width: 8px;\n    height: 8px;\n    position: absolute;\n    top: -3px;\n    right: -3px;\n  }')),vp=(0,s.ZP)(Km.v).withConfig({displayName:"Menu__MenuIcon"})(["\n  color: ",";\n"],M("foreground")),_p=(0,s.ZP)(_e.x).withConfig({displayName:"Menu__CloseIcon"})(["\n  color: ",";\n"],M("foreground")),xp=(0,s.ZP)(be).withConfig({displayName:"Menu__SideMenuMenuButton"})(["\n  padding: 0;\n\n  .fade-enter {\n    opacity: 0;\n  }\n\n  .fade-enter-active {\n    opacity: 1;\n    transition: opacity ","ms;\n  }\n\n  .fade-exit {\n    opacity: 0;\n  }\n\n  .fade-exit-active {\n    opacity: 1;\n    transition: opacity ","ms;\n  }\n"],K.REG,K.REG),Sp=(0,s.ZP)(Pn.x).attrs({alignItems:"center"}).withConfig({displayName:"Menu__Key"})(["\n  padding: 0 4px;\n  background: #61647a;\n  border-radius: 2px;\n  font-size: 1.2rem;\n  height: 1.8rem;\n\n  &:not(:last-child) {\n    margin-right: 0.25rem;\n  }\n"]),kp=(0,s.ZP)(Pn.x).attrs({alignItems:"center",gap:"0"}).withConfig({displayName:"Menu__RunShortcut"})(["\n  margin-left: 1.5rem;\n"]),Tp=s.ZP.div.withConfig({displayName:"Menu__MenuItems"})(["\n  display: grid;\n  grid-auto-flow: column;\n  align-items: center;\n"]),Np=H.isMacintosh||H.isIOS?"⌘":"Ctrl",Op=()=>{const e=(0,o.I0)(),[t,n]=(0,r.useState)(),a=qe("Escape"),{consoleConfig:i}=Ir(),l=(0,o.v9)(Cn.query.getRunning),s=(0,o.v9)(Cn.console.getSideMenuOpened),{sm:c}=Ye(),{exampleQueriesVisited:d,updateSettings:u}=Gd(),m=(0,r.useCallback)((()=>{e(Yt())}),[e]),p=(0,r.useCallback)((e=>{!d&&e&&u(L.EXAMPLE_QUERIES_VISITED,!0),n(e)}),[]),g=(0,r.useCallback)((()=>{n(!1)}),[]),f=(0,r.useCallback)((()=>{e(Gt.toggleSideMenu())}),[e]);return(0,r.useEffect)((()=>{n(!1)}),[a]),(0,r.useEffect)((()=>{!c&&s&&e(Gt.toggleSideMenu())}),[e,s,c]),r.createElement(yp,{_display:c?"none":"inline"},r.createElement(wp,null),i.savedQueries&&i.savedQueries.length>0&&r.createElement(ut,{active:t,onToggle:p,trigger:r.createElement(Cp,{skin:"secondary",$firstTimeVisitor:!d},r.createElement(Qm.m,{size:"18px"}),r.createElement("span",null,"Example queries"))},r.createElement(bp,{hidePicker:g,queries:i.savedQueries??[]})),r.createElement(wp,null),l.value&&r.createElement(Dn.z,{skin:"error",onClick:m,prefixIcon:r.createElement(Ba.d,{size:"18px"})},"Cancel"),!l.value&&r.createElement(Dn.z,{skin:"success",title:"Ctrl+Enter",onClick:m,prefixIcon:r.createElement(Ym.s,{size:"18px"})},"Run",r.createElement(kp,null,r.createElement(Sp,null,Np),r.createElement(Sp,null,r.createElement(Xm.i,{size:"16px"})))),r.createElement(Tp,null,r.createElement(Ep.F,{appId:"QL9L2YL7AQ",apiKey:"2f67aeacbe73ad08a49efb9214ea27f3",indexName:"questdb",placeholder:"Search docs",translations:{button:{buttonText:"Search docs"}},hitComponent:({hit:e,children:t})=>r.createElement("a",{href:e.url,target:"_blank",rel:"noreferrer"},t),navigator:{navigate:({itemUrl:e})=>window.open(e,"_blank")}})),c&&r.createElement(xp,{onClick:f},r.createElement(ct.Z,{classNames:"fade",in:s,timeout:K.REG},s?r.createElement(_p,{size:"26px"}):r.createElement(vp,{size:"26px"}))))};var Rp=n(26882),Ip=n(18008),Ap=n(22998),Lp=n(62810);const Pp=s.ZP.div.withConfig({displayName:"InstanceSettingsPopper__Wrapper"})(["\n  position: absolute;\n  margin-top: 0.5rem;\n  background: ",";\n  border: 1px solid ",";\n  border-radius: 0.4rem;\n  padding: 1.5rem;\n  width: 32rem;\n  z-index: 1000;\n"],(({theme:e})=>e.color.backgroundDarker),(({theme:e})=>e.color.gray1)),Dp=s.ZP.div.withConfig({displayName:"InstanceSettingsPopper__ColorSelector"})(["\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n  align-self: center;\n"]),Mp=s.ZP.button.withConfig({displayName:"InstanceSettingsPopper__ColorOption"})(["\n  width: 3rem;\n  height: 3rem;\n  border-radius: 0.4rem;\n  cursor: pointer;\n  border: 2px solid ",";\n  padding: 0;\n  background: ",";\n  \n  &:focus-visible {\n    outline: 1px solid ",";\n  }\n"],(({theme:e,selected:t})=>t?e.color.foreground:"transparent"),(({colorValue:e,theme:t,customColor:n})=>{if(n)return n;switch(e){case"r":return"#c7072d";case"g":return"#00aa3b";case"b":return"#007aff";case"default":return t.color.backgroundLighter;default:return"transparent"}}),(({theme:e})=>e.color.cyan)),Bp=s.ZP.button.withConfig({displayName:"InstanceSettingsPopper__ColorWheelOption"})(["\n  position: relative;\n  width: 3rem;\n  height: 3rem;\n  border-radius: 0.4rem;\n  cursor: pointer;\n  border: ",";\n  padding: 0;\n  overflow: hidden;\n\n  &:focus-visible {\n    outline: 1px solid ",';\n  }\n\n  &::before {\n    content: "";\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: conic-gradient(\n    #ff0000,\n    #ffff00,\n    #00ff00,\n    #00ffff,\n    #0000ff,\n    #ff00ff,\n    #ff0000\n    );\n  }\n'],(({selected:e,theme:t})=>e?`2px solid ${t.color.foreground}`:"0"),(({theme:e})=>e.color.cyan)),Fp=s.ZP.div.withConfig({displayName:"InstanceSettingsPopper__ColorPickerContainer"})(["\n  margin-top: 1rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  align-self: center;\n"]),Hp=s.ZP.div.withConfig({displayName:"InstanceSettingsPopper__ColorInputRow"})(["\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n"]),Vp=s.ZP.input.attrs({type:"range",min:0,max:255}).withConfig({displayName:"InstanceSettingsPopper__ColorSlider"})(["\n  flex: 1;\n  height: 1rem;\n  appearance: none;\n  background: linear-gradient(to right, rgb(0,0,0), rgb(255,0,0));\n  border-radius: 0.5rem;\n  \n  &::-webkit-slider-thumb {\n    appearance: none;\n    width: 1.8rem;\n    height: 1.8rem;\n    border-radius: 50%;\n    background: white;\n    cursor: pointer;\n    border: 1px solid ",";\n  }\n  \n  &.red {\n    background: linear-gradient(to right, rgb(0,0,0), rgb(255,0,0));\n  }\n  \n  &.green {\n    background: linear-gradient(to right, rgb(0,0,0), rgb(0,255,0));\n  }\n  \n  &.blue {\n    background: linear-gradient(to right, rgb(0,0,0), rgb(0,0,255));\n  }\n"],(({theme:e})=>e.color.gray1)),zp=s.ZP.input.attrs({type:"number",min:0,max:255}).withConfig({displayName:"InstanceSettingsPopper__ColorValueInput"})(["\n  width: 6rem;\n  padding: 0.5rem;\n  background: ",";\n  border: 1px solid ",";\n  border-radius: 0.4rem;\n  color: ",";\n  \n  &:focus {\n    outline: none;\n    border-color: ",";\n  }\n"],(({theme:e})=>e.color.selection),(({theme:e})=>e.color.gray1),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.cyan)),Zp=s.ZP.form.withConfig({displayName:"InstanceSettingsPopper__StyledForm"})(["\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n"]),Up=(0,s.ZP)(Dn.z).withConfig({displayName:"InstanceSettingsPopper__StyledButton"})(["\n  font-size: 1.6rem;\n  &:focus-visible {\n    outline: 2px solid ",";\n  }\n"],(({theme:e})=>e.color.cyan)),qp=(0,s.ZP)(er.P).withConfig({displayName:"InstanceSettingsPopper__StyledSelect"})(["\n  &:focus-visible {\n    border: 1px solid ",";\n  }\n"],(({theme:e})=>e.color.cyan)),Gp=(0,s.ZP)(Pn.x).withConfig({displayName:"InstanceSettingsPopper__Buttons"})(["\n  margin-top: 1.5rem;\n  gap: 1rem;\n  justify-content: flex-end;\n  flex-direction: row-reverse;\n"]),$p=(0,s.ZP)(Pn.x).attrs({flexDirection:"column",gap:"0.5rem"}).withConfig({displayName:"InstanceSettingsPopper__FormGroup"})(["\n  width: 100%;\n  align-items: flex-start;\n"]),jp=(0,s.ZP)(Gn.I).withConfig({displayName:"InstanceSettingsPopper__StyledInput"})(["\n  width: 100%;\n  background: ",";\n  border: 1px solid ",";\n  border-radius: 0.4rem;\n  color: ",";\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n  }\n\n  &::placeholder {\n    color: ",";\n  }\n"],(({theme:e})=>e.color.selection),(({theme:e})=>e.color.gray1),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.cyan),(({theme:e})=>e.color.gray2)),Wp=s.ZP.textarea.withConfig({displayName:"InstanceSettingsPopper__TextArea"})(["\n  width: 100%;\n  min-height: 8rem;\n  padding: 0.8rem;\n  background: ",";\n  border: 1px solid ",";\n  border-radius: 0.4rem;\n  color: ",";\n  font-family: inherit;\n  font-size: 1.4rem;\n  line-height: 1.5;\n  resize: vertical;\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n  }\n\n  &::placeholder {\n    color: ",";\n  }\n"],(({theme:e})=>e.color.selection),(({theme:e})=>e.color.gray1),(({theme:e})=>e.color.foreground),(({theme:e})=>e.color.cyan),(({theme:e})=>e.color.gray2)),Qp=s.ZP.label.withConfig({displayName:"InstanceSettingsPopper__FormLabel"})(["\n  text-align: ",";\n  width: 100%;\n  font-size: 1.6rem;\n"],(e=>e.align||"left")),Yp=(0,s.ZP)(Pe).withConfig({displayName:"InstanceSettingsPopper__ErrorText"})(["\n  color: ",";\n  font-size: 1.2rem;\n  margin-top: 0.2rem;\n"],(({theme:e})=>e.color.red)),Kp=({active:e,onToggle:t,values:n,onSave:a,onValuesChange:o,trigger:i})=>{const[l,s]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),[u,m]=(0,r.useState)(!1),[p,g]=(0,r.useState)({r:0,g:0,b:0}),f=(0,r.useRef)(null);(0,r.useEffect)((()=>{if(n.instance_rgb&&n.instance_rgb.startsWith("rgb")){m(!0);const e=n.instance_rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);e&&g({r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)})}else m(!1)}),[n.instance_rgb]);const h=e=>{o({...n,instance_rgb:e}),"custom"!==e&&m(!1)},b=(e,t)=>{const r={...p,[e]:t};g(r);const a=`rgb(${r.r}, ${r.g}, ${r.b})`;o({...n,instance_rgb:a})};(0,r.useEffect)((()=>{n.instance_type||o({...n,instance_type:"development"}),e&&setTimeout((()=>{f.current?.focus()}),100)}),[e]);const E=`rgb(${p.r}, ${p.g}, ${p.b})`;return r.createElement(ut,{active:e,onToggle:t,trigger:i,placement:"bottom-start"},r.createElement(Pp,null,r.createElement(Zp,{onSubmit:async e=>{e.preventDefault(),n?.instance_name?.trim()?(d(null),s(!0),await a(n),s(!1)):d("Instance name is required")}},r.createElement($p,null,r.createElement(Qp,{htmlFor:"instance-name-input"},"Instance Name"),r.createElement(jp,{id:"instance-name-input","data-hook":"topbar-instance-name-input",value:n.instance_name,onChange:e=>{const t={...n,instance_name:e.target.value};o(t)},placeholder:"Enter instance name",ref:f}),c&&r.createElement(Yp,null,c)),r.createElement($p,null,r.createElement(Qp,{htmlFor:"instance-type-select"},"Instance Type"),r.createElement(qp,{id:"instance-type-select","data-hook":"topbar-instance-type-select",name:"instance-type",options:[{label:"Development",value:"development"},{label:"Production",value:"production"},{label:"Testing",value:"testing"}],required:!0,value:n.instance_type,onChange:e=>o({...n,instance_type:e.target.value})})),r.createElement($p,null,r.createElement(Qp,{htmlFor:"instance-description-input"},"Description"),r.createElement(Wp,{id:"instance-description-input","data-hook":"topbar-instance-description-input",value:n.instance_description,onChange:e=>o({...n,instance_description:e.target.value}),placeholder:"Enter instance description"})),r.createElement($p,null,r.createElement(Qp,{color:"foreground"},"Color"),r.createElement(Dp,null,r.createElement(Mp,{type:"button",colorValue:"default",selected:!n.instance_rgb||""===n.instance_rgb,onClick:()=>h(""),"data-hook":"topbar-instance-color-option-default"}),r.createElement(Mp,{type:"button",colorValue:"r",selected:"r"===n.instance_rgb,onClick:()=>h("r"),"data-hook":"topbar-instance-color-option-r"}),r.createElement(Mp,{type:"button",colorValue:"g",selected:"g"===n.instance_rgb,onClick:()=>h("g"),"data-hook":"topbar-instance-color-option-g"}),r.createElement(Mp,{type:"button",colorValue:"b",selected:"b"===n.instance_rgb,onClick:()=>h("b"),"data-hook":"topbar-instance-color-option-b"}),r.createElement(Bp,{type:"button",selected:Boolean(n.instance_rgb?.startsWith("rgb")),onClick:()=>{m(!0),o({...n,instance_rgb:E})},"data-hook":"topbar-instance-color-option-custom"})),u&&r.createElement(Fp,null,r.createElement(Hp,null,r.createElement(Pe,{color:"foreground"},"R"),r.createElement(Vp,{className:"red",value:p.r,onChange:e=>b("r",parseInt(e.target.value,10)),"data-hook":"topbar-instance-color-slider-r"}),r.createElement(zp,{value:p.r,onChange:e=>b("r",parseInt(e.target.value,10)),"data-hook":"topbar-instance-color-input-r"})),r.createElement(Hp,null,r.createElement(Pe,{color:"foreground"},"G"),r.createElement(Vp,{className:"green",value:p.g,onChange:e=>b("g",parseInt(e.target.value,10)),"data-hook":"topbar-instance-color-slider-g"}),r.createElement(zp,{value:p.g,onChange:e=>b("g",parseInt(e.target.value,10)),"data-hook":"topbar-instance-color-input-g"})),r.createElement(Hp,null,r.createElement(Pe,{color:"foreground"},"B"),r.createElement(Vp,{className:"blue",value:p.b,onChange:e=>b("b",parseInt(e.target.value,10)),"data-hook":"topbar-instance-color-slider-b"}),r.createElement(zp,{value:p.b,onChange:e=>b("b",parseInt(e.target.value,10)),"data-hook":"topbar-instance-color-input-b"})))),r.createElement(Gp,null,r.createElement(Up,{type:"submit",prefixIcon:l?r.createElement(Mi.a,null):void 0,"data-hook":"topbar-instance-save-button"},"Save"),r.createElement(Up,{type:"button",onClick:()=>t(!1),skin:"secondary","data-hook":"topbar-instance-cancel-button"},"Cancel")))))},Xp=s.ZP.div.withConfig({displayName:"toolbar__EnvIconWrapper"})(["\n  display: flex;\n  align-items: center;\n  padding: 0.3rem;\n  background: ",";\n  border-radius: 0.4rem;\n"],(({$background:e})=>e??"inherit")),Jp=(0,s.ZP)(Pn.x).attrs({align:"center"}).withConfig({displayName:"toolbar__Root"})(["\n  gap: 1.5rem;\n  padding-left: 1.5rem;\n  white-space: nowrap;\n  display: flex;\n  overflow: hidden;\n"]),eg=s.ZP.div.withConfig({displayName:"toolbar__CustomTooltipWrapper"})(["\n  display: flex;\n  flex-direction: column;\n  padding: 1rem 0;\n  background: ",";\n  font-size: 1.4rem;\n  border-radius: 0.8rem;\n  border: 1px solid ",";\n"],(({theme:e})=>e.color.background),(({$badgeColors:e})=>e.primary)),tg=s.ZP.div.withConfig({displayName:"toolbar__FlexRow"})(["\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n"]),ng=s.ZP.h4.withConfig({displayName:"toolbar__Title"})(["\n  display: flex;\n  align-items: center;\n  border-bottom: 1px solid ",";\n  padding: 0 1rem 1rem;\n  gap: 0.8rem;\n  font-size: 1.4rem;\n  margin-bottom: 0;\n"],(({theme:e})=>e.color.gray1)),rg=s.ZP.div.withConfig({displayName:"toolbar__FlexCol"})(["\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n"]),ag=s.ZP.div.withConfig({displayName:"toolbar__Info"})(["\n  display: flex;\n  flex-direction: column;\n  padding: 0 1rem;\n  gap: 1rem;\n"]),og=(0,s.ZP)(Pn.x).withConfig({displayName:"toolbar__Badge"})(["\n  display: flex;\n  align-items: center;\n  padding: 0 1rem;\n  padding-left: 0.3rem;\n  height: 3rem;\n  border-radius: 0.4rem;\n  flex-shrink: 1;\n  min-width: 0;\n  gap: 0;\n  transition: opacity 0.1s ease;\n\n  ","\n\n  .instance-name {\n    font-size: 1.6rem;\n    display: inline-flex;\n    gap: 0;\n    align-items: center;\n    vertical-align: middle;\n    overflow: hidden;\n    flex-shrink: 1;\n    margin-left: 0.3rem;\n\n    &-text {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n      flex-shrink: 1;\n      min-width: 0;\n      color: inherit;\n    }\n\n    &-type {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n      flex-shrink: 0;\n      color: inherit;\n    }\n\n    &.placeholder {\n      color: ",";\n    }\n  }\n\n  .edit-icon {\n    cursor: pointer;\n    display: inline;\n    width: 2.2rem;\n    margin-left: 1rem;\n    padding: 0.1rem;\n    background: inherit;\n    border-radius: 0.4rem;\n    flex-shrink: 0;\n\n    &.placeholder {\n      color: ",";\n\n      &:hover {\n        color: ",";\n        background: ",";\n      }\n    }\n  }\n"],(({$badgeColors:e})=>`\n    background: ${e.primary};\n\n    .instance-name {\n      color: ${e.secondary};\n    }\n\n    .edit-icon {\n      color: ${e.secondary};\n\n      &:hover {\n        color: ${e.primary};\n        background: ${e.secondary};\n      }\n    }\n  `),(({theme:e})=>e.color.orange),(({theme:e})=>e.color.orange),(({theme:e})=>e.color.backgroundLighter),(({theme:e})=>e.color.orange)),ig=(0,s.ZP)(Pn.x).attrs({gap:"0.5rem"}).withConfig({displayName:"toolbar__User"})(["\n  background: ",";\n  border-radius: 0.4rem;\n  height: 3rem;\n  padding: 0 1rem;\n  font-weight: 600;\n"],(({theme:e})=>e.color.backgroundLighter)),lg=s.ZP.span.withConfig({displayName:"toolbar__EnterpriseBadge"})(["\n  padding: 0 4px;\n  background: ",";\n  border-radius: 2px;\n  color: ",";\n\n  &:not(:last-child) {\n    margin-right: 0.25rem;\n  }\n"],(({theme:e})=>e.color.pinkDarker),(({theme:e})=>e.color.foreground)),sg=s.ZP.span.withConfig({displayName:"toolbar__Separator"})(["\n  display: inline-block;\n  flex-shrink: 0;\n  width: 0.15rem;\n  margin: 0 1rem;\n  height: 1.8rem;\n  background: ",";\n\n"],(({$color:e})=>e)),cg=(e,t)=>{if(!e||!e.startsWith("rgb"))return t?.color.foreground||"inherit";const n=e.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);if(n){const e=parseInt(n[1],10)/255,r=parseInt(n[2],10)/255,a=parseInt(n[3],10)/255,o=.2126*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))+.7152*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.0722*(a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4));return o<.25?t?.color.foreground:o<.5?t?.color.gray2:o<.75?t?.color.gray1:t?.color.background}return t?.color.foreground||"inherit"},dg=e=>{const t=(0,s.Fg)();return e?e.startsWith("rgb")?{primary:e,secondary:cg(e,t)}:"r"===e?{primary:"rgb(199, 7, 45)",secondary:t.color.foreground}:"g"===e?{primary:"rgb(0, 170, 59)",secondary:t.color.foreground}:"b"===e?{primary:"rgb(0, 122, 255)",secondary:t.color.foreground}:{primary:t.color.backgroundLighter,secondary:t.color.foreground}:{primary:t.color.backgroundLighter,secondary:t.color.foreground}},ug=({instanceType:e,color:t})=>{switch(e){case"development":return r.createElement(Ip.w,{size:"18px",color:t});case"production":return r.createElement(Ap.K,{size:"18px",color:t});case"testing":return r.createElement(Lp.G,{size:"18px",color:t,style:{transform:"scale(1.2)"}});default:return r.createElement(pc.Z,{size:"18px",style:{transform:"translateY(-0.2rem)"},color:t})}},mg=({icon:e,placement:t,shownValues:n})=>{const a=dg(n?.instance_rgb??null);return r.createElement(dt,{placement:t,trigger:e},r.createElement(eg,{$badgeColors:a},r.createElement(rg,null,n?.instance_type&&r.createElement(ng,null,r.createElement(Xp,{$background:a.primary},r.createElement(ug,{color:a.secondary,instanceType:n?.instance_type})),r.createElement(Pe,{color:"foreground",weight:400},"You are connected to a QuestDB instance for ",n?.instance_type)),r.createElement(ag,null,r.createElement(tg,null,r.createElement(Pe,{color:"foreground",weight:600},"Instance Name:"),r.createElement(Pe,{color:"foreground",size:"md"},n?.instance_name)),n?.instance_description&&r.createElement(tg,null,r.createElement(Pe,{color:"foreground",weight:600},"Description:"),r.createElement(Pe,{color:"foreground",size:"md"},n?.instance_description))))))},pg=()=>{const{quest:e}=(0,r.useContext)(qr),{settings:t,preferences:n,refreshSettingsAndPreferences:a}=Ir(),{logout:i}=Hr(),l=(0,o.v9)(Cn.query.getResult),[c,d]=(0,r.useState)(null),[u,m]=(0,r.useState)(!1),[p,g]=(0,r.useState)(null),[f,h]=(0,r.useState)(!1),b=u?p:n,y=b?.instance_type?b.instance_type.charAt(0).toUpperCase()+b.instance_type.slice(1):"",w=dg(b?.instance_rgb??null),C=(0,s.Fg)(),v=async()=>{try{const a=await e.query("SELECT current_user",{limit:"0,1"});if(a.type===T.DQL&&1===a.count){const e=a.data[0].current_user;return d(e),dn(L.AUTH_PAYLOAD)&&e&&t["acl.oidc.client.id"]&&(n=t["acl.oidc.client.id"],r=e,localStorage.setItem(`${L.SSO_USERNAME}.${n}`,r)),e}return null}catch(e){return null}var n,r},_=async n=>{if(t["acl.enabled"]&&"OSS"!==t["release.type"])if(n)try{const t=await e.showPermissions(n),r=t.type===T.DQL&&(0===t.count||t.data.some((e=>"SETTINGS"===e.permission)));h(r)}catch(e){h(!1)}else h(!1);else h(!0)};(0,r.useEffect)((()=>{v().then(_),a()}),[]),(0,r.useEffect)((()=>{l&&l.type===T.DDL&&(v(),a())}),[l]);const x=(0,r.useCallback)((async(e=!0)=>{const t=n?.version,{preferences:r}=await a();if(t!==r.version&&e){Mt("Instance information is updated with the latest changes from the server.",{autoClose:5e3});const e=document.querySelector('[data-hook="topbar-instance-badge"]');e&&(e=>{e.style.opacity="0",setTimeout((()=>{e.style.opacity="1"}),200),setTimeout((()=>{e.style.opacity="0"}),400),setTimeout((()=>{e.style.opacity="1"}),600),setTimeout((()=>{e.style.opacity="0"}),800),setTimeout((()=>{e.style.opacity="1"}),1e3)})(e)}return r}),[a,n]),S=(0,r.useCallback)((async()=>{const e=await x(!0);u&&p?.version!==e.version&&g(e)}),[x,u,p]),k=(0,r.useCallback)((async e=>{const t=await x(e);g(e?t:null),m(e)}),[x]);return(0,r.useEffect)((()=>(window.addEventListener("focus",S),()=>{window.removeEventListener("focus",S)})),[S]),r.createElement(Jp,null,r.createElement(Pn.x,{gap:"0.5rem"},r.createElement(Pe,{color:"foreground"},"Web Console"),"EE"===t["release.type"]&&r.createElement(Xe,{icon:r.createElement(lg,null,"EE"),tooltip:"QuestDB Enterprise Edition",placement:"bottom"})),n&&r.createElement(og,{$badgeColors:w,"data-hook":"topbar-instance-badge"},r.createElement(Pn.x,null,b?.instance_type?r.createElement(mg,{icon:r.createElement("div",{"data-hook":"topbar-instance-icon",style:{padding:"0.7rem"}},r.createElement(ug,{instanceType:b?.instance_type,color:w.secondary})),placement:"bottom",shownValues:b}):r.createElement(Bi.j,{size:"18px",color:C.color.orange})),b?.instance_name?r.createElement(Pn.x,{"data-hook":"topbar-instance-name",className:"instance-name"},r.createElement(Pe,{className:"instance-name-type"},y),r.createElement(sg,{$color:w.secondary}),r.createElement(Pe,{className:"instance-name-text"},b?.instance_name)):r.createElement(Pe,{"data-hook":"topbar-instance-name",className:"instance-name placeholder"},"Instance name is not set"),f&&r.createElement(Kp,{active:u,onToggle:k,values:p??n,onSave:async t=>{try{const n=await e.savePreferences(t);if(n.success){await k(!1),Bt("Instance information updated successfully.");const t=await e.query(`${E.CONFIG} limit -1`);if(t.type===T.DQL&&1===t.count){const e=t.data[0];pn(e)}return}const{preferences:r}=await a();if(g(r),409===n.status)return void Ft("Instance information is updated with the latest changes from the server. Please try updating it again.",{autoClose:5e3});throw new Error(n.message)}catch(e){Ft("Failed to update instance information: "+e,{autoClose:5e3})}},onValuesChange:g,trigger:r.createElement(Bu.I,{"data-hook":"topbar-instance-edit-icon",size:"18px",className:"edit-icon "+(b?.instance_name?"":"placeholder")})})),r.createElement(Pn.x,{gap:"0.5rem"},t["acl.enabled"]&&c&&r.createElement(ig,null,r.createElement(Mn.n,{size:"18px"}),r.createElement(Pe,{color:"foreground"},c)),In(t)&&r.createElement(Dn.z,{onClick:()=>i(),prefixIcon:r.createElement(Rp.R,{size:"18px"}),skin:"secondary","data-hook":"button-logout"},"Logout")))};var gg=n(6086);const fg=(0,s.ZP)(Et).withConfig({displayName:"back-button__Root"})(["\n  margin-left: 0.5rem;\n"]),hg=({onClick:e,label:t})=>r.createElement(dt,{placement:"right",modifiers:[{name:"offset",options:{offset:[5,15]}}],trigger:r.createElement(fg,{onClick:e},r.createElement(gg.X,{size:w}))},r.createElement(_t,null,t||"Back to the Cloud")),bg=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"space-between"}).withConfig({displayName:"TopBar__Root"})(["\n  width: 100%;\n  height: 4.5rem;\n  background: ",";\n"],(({theme:e})=>e.color.backgroundDarker)),Eg=()=>{const e=(0,sa.useLiveQuery)((async()=>({returnTo:(await ta.editor_settings.where("key").equals("returnTo").first())?.value??"",returnToLabel:(await ta.editor_settings.where("key").equals("returnToLabel").first())?.value??""})),[]);return r.createElement(bg,null,e?.returnTo&&r.createElement(hg,{label:e?.returnToLabel,onClick:()=>{window.location.href=e.returnTo}}),r.createElement(pg,null),r.createElement(Op,null))};var yg=n(44560);const wg=s.ZP.span.withConfig({displayName:"UnreadItemsIcon__Root"})(["\n  position: relative;\n"]),Cg=s.ZP.span.withConfig({displayName:"UnreadItemsIcon__Tick"})(["\n  position: absolute;\n  right: 0;\n  top: 0;\n  width: 1.3rem;\n  height: 1.3rem;\n  border-radius: 50%;\n  background-color: ",";\n"],(({theme:e})=>e.color.red)),vg=s.ZP.span.withConfig({displayName:"UnreadItemsIcon__Count"})(["\n  position: absolute;\n  left: 100%;\n  top: -50%;\n  font-size: ",";\n  border-radius: 6px;\n  background-color: #c64242;\n  color: ",";\n  padding: 0.2rem 0.4rem;\n"],(({theme:e})=>e.fontSize.ms),(({theme:e})=>e.color.white)),_g=({icon:e,label:t,tick:n,count:a})=>r.createElement(wg,null,"number"==typeof a&&a>0&&r.createElement(vg,null,a),e,n&&r.createElement(Cg,null),t&&r.createElement("span",null,t)),xg=s.ZP.div.withConfig({displayName:"thumbnail__Root"})(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  max-width: 100%;\n  margin: 2rem 0;\n  border-radius: ",";\n  box-shadow: 0 7px 30px -10px ",";\n  overflow: hidden;\n  background: ",";\n\n  svg {\n    position: absolute;\n  }\n"],(({theme:e})=>e.borderRadius),(({theme:e})=>e.color.black),(({theme:e})=>e.color.backgroundLighter)),Sg=s.ZP.img.withConfig({displayName:"thumbnail__ThumbImg"})(["\n  height: auto;\n\n  ","\n"],(({loaded:e,fadeIn:t})=>`\n    opacity: ${e?1:0};\n    ${t&&"transition: opacity 0.2s ease-in-out;"}\n  `)),kg=({src:e,alt:t,width:n,height:a,containerWidth:o,containerHeight:i,fadeIn:l,...s})=>{const[c,d]=(0,r.useState)(!1);let u=o,m=u/n*a;if(m>i){const e=i/m;m=i,u*=e}return(0,r.useEffect)((()=>{const t=new Image;t.src=e,t.onload=()=>{d(!0)}}),[e]),r.createElement(xg,s,!c&&r.createElement(Mi.a,null),r.createElement(Sg,{src:e,alt:t,width:u,height:m,loaded:c,fadeIn:l}))};var Tg=n(84991);const Ng="10deg",Og=(0,s.F4)(["\n  0%, 50%, 100% {\n    transform: rotate(0);\n  }\n\n  10%, 20%, 30% {\n    transform: rotate(-",");\n  }\n\n  15%, 25%, 40% {\n    transform: rotate(",");\n  }\n"],Ng,Ng),Rg=(0,s.iv)(["\n  animation: "," 2s infinite ease-in-out;\n  transform-origin: 50% 0;\n"],Og),Ig=(0,s.ZP)(Tg.d).withConfig({displayName:"bell__Bell"})(["\n  color: ",";\n\n  ","\n"],(({theme:e,$unread:t})=>e.color[t?"foreground":"inherit"]),(({$unread:e})=>e&&Rg));function Ag(){return Ag=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ag.apply(this,arguments)}const Lg=s.ZP.div.withConfig({displayName:"News__Loading"})(["\n  display: grid;\n  grid-auto-flow: column;\n  gap: 1rem;\n  justify-self: center;\n  margin-top: 2rem;\n"]),Pg=s.ZP.div.withConfig({displayName:"News__Items"})(["\n  display: grid;\n  width: 100%;\n  overflow: auto;\n"]),Dg=s.ZP.div.withConfig({displayName:"News__Item"})(["\n  display: grid;\n  gap: 1rem;\n  padding: 2rem;\n\n  &:not(:last-child) {\n    border-bottom: 1px solid ",";\n  }\n"],(({theme:e})=>e.color.backgroundLighter)),Mg=s.ZP.h2.withConfig({displayName:"News__Title"})(["\n  margin: 0;\n  font-size: 2rem;\n"]),Bg=(0,s.ZP)(Pe).attrs({color:"foreground"}).withConfig({displayName:"News__NewsText"})(["\n  a {\n    color: ",";\n  }\n\n  h2 {\n    font-size: 1.8rem;\n  }\n\n  h3 {\n    font-size: 1.6rem;\n  }\n\n  p,\n  li {\n    font-size: ",";\n    line-height: 1.75;\n  }\n\n  code {\n    background-color: ",";\n    color: ",";\n    padding: 0.2rem 0.4rem;\n    border-radius: 0.2rem;\n  }\n\n  li:not(:last-child) {\n    margin-bottom: 0.5rem;\n  }\n"],(({theme:e})=>e.color.cyan),(({theme:e})=>e.fontSize.lg),(({theme:e})=>e.color.selection),(({theme:e})=>e.color.pink)),Fg=()=>{const e=(0,o.I0)(),{quest:t}=(0,r.useContext)(qr),n=(0,o.v9)(Cn.telemetry.getConfig),[a,i]=(0,r.useState)(!1),[l,s]=(0,r.useState)(!1),[c,d]=(0,r.useState)(void 0),[u,m]=(0,r.useState)(!1),[p,g]=(0,r.useState)([]),[f,h]=(0,r.useState)(!1),b=(0,o.v9)(Cn.console.getActiveSidebar);let E;const y=async()=>{i(!0),s(!1);try{const e=await t.getNews({category:"enterprise",telemetryConfig:n});d(e)}catch(e){s(!0)}finally{i(!1)}};return(0,r.useEffect)((()=>(y(),window.addEventListener("focus",y),()=>window.removeEventListener("focus",y))),[]),(0,r.useEffect)((()=>{c&&(async()=>{if(c){const e=await ta.read_notifications.toArray(),t=c.map((e=>e.id)).filter((t=>!e.find((e=>e.newsId===t))));g(t),h(t?.length>0)}})()}),[c]),(0,r.useEffect)((()=>{c&&(u?(async()=>{if(c){const e=c.map((e=>e.id)),t=(await ta.read_notifications.toArray()).map((e=>e.newsId)),n=e.filter((e=>!t.includes(e)));await ta.read_notifications.bulkPut(n.map((e=>({newsId:e}))),{allKeys:!0}),h(!1)}})():g([]))}),[u,c]),(0,r.useEffect)((()=>{m("news"===b)}),[b]),r.createElement(Ue,{mode:"side",title:"QuestDB News",open:u,onOpenChange:async t=>{e(Gt.setActiveSidebar(t?"news":void 0))},trigger:r.createElement(Xe,{icon:r.createElement(Et,{"data-hook":"news-panel-button",onClick:()=>m(!u),selected:u},r.createElement(_g,{icon:r.createElement(Ig,{size:w,$unread:f}),tick:f})),placement:"left",tooltip:"QuestDB News"})},r.createElement(Ue.ContentWrapper,{mode:"side"},r.createElement(Pg,null,a&&!c&&r.createElement(Lg,null,r.createElement(Pe,{color:"foreground"},"Loading news..."),r.createElement(Mi.a,null)),l&&r.createElement(Lg,null,r.createElement(Pe,{color:"red"},"Error loading news. Please try again shortly.")),(!a||c)&&!l&&c&&c.map(((t,n)=>r.createElement(Dg,{key:`${n}-${t.title}`,unread:void 0!==p.find((e=>t.id===e))},r.createElement(Mg,null,t.title),r.createElement(Pe,{color:"gray2"},t.date),t.thumbnail&&t.thumbnail.length>0&&t.thumbnail[0].thumbnails.large&&r.createElement(kg,Ag({containerWidth:460,containerHeight:460,src:t.thumbnail[0].thumbnails.large.url,alt:`${t.title} thumbnail`,width:t.thumbnail[0].thumbnails.large.width,height:t.thumbnail[0].thumbnails.large.height,fadeIn:!0},t&&t.thumbnail?{onMouseOver:()=>{t.thumbnail&&(E=setTimeout((()=>{t&&t.thumbnail&&e(Gt.setImageToZoom({src:t.thumbnail[0].thumbnails.large.url,width:t.thumbnail[0].thumbnails.large.width,height:t.thumbnail[0].thumbnails.large.height,alt:t.title}))}),500))},onMouseOut:()=>{clearTimeout(E),setTimeout((()=>{e(Gt.setImageToZoom(void 0))}),250)}}:{})),r.createElement(Bg,null,r.createElement(yg.D,{components:{a:({node:e,children:t,...n})=>r.createElement("a",Ag({},n.href?.startsWith("http")?{target:"_blank",rel:"noopener noreferrer"}:{},n),t)}},t.body))))))))};var Hg=n(78882);const Vg=(0,s.ZP)(el.i).withConfig({displayName:"CreateTableDialog__DisabledTableIcon"})(["\n  opacity: 0.3;\n"]),zg=()=>{const[e,t]=(0,r.useState)(void 0),n=(0,o.I0)(),a=(0,o.v9)(Cn.query.getTables),i=(0,o.v9)(Cn.console.getActiveSidebar),{consoleConfig:l}=Ir(),{appendQuery:s}=ma();return(0,r.useEffect)((()=>{t("create"===i?"add":void 0)}),[i]),(0,r.useEffect)((()=>{void 0!==e&&n(Gt.setActiveSidebar("create"))}),[e]),l.readOnly?r.createElement(Xe,{icon:r.createElement(Vg,{size:w,"data-hook":"create-table-panel-button"}),tooltip:"To use this feature, turn off read-only mode in the configuration file",placement:"left"}):r.createElement(mm,{action:"add",isEditLocked:!1,hasWalSetting:!0,walEnabled:!1,name:"",partitionBy:"NONE",ttlValue:0,ttlUnit:"HOURS",schema:[],tables:a,timestamp:"",onOpenChange:e=>t(e),open:void 0!==e,onSchemaChange:e=>{const{name:t,partitionBy:r,timestamp:a,ttlValue:o,ttlUnit:i,schemaColumns:l,walEnabled:c}=e,d=(({name:e,partitionBy:t,timestamp:n,walEnabled:r,dedup:a,ttlValue:o,ttlUnit:i,schemaColumns:l})=>{const s=n&&l.find((e=>e.column===n&&"TIMESTAMP"===e.type));let c=`CREATE TABLE '${e}' (`;for(let e=0;e<l.length;e++){const{column:t,type:n,indexed:r,indexBlockCapacity:a,symbolCached:o,symbolCapacity:i}=l[e];c+=`${t} ${n} `,"SYMBOL"===n&&(c+=i?`capacity ${i} `:"",o&&(c+="CACHE ")),r&&(c+="index ",a&&(c+=`capacity ${a} `)),c=(0,Hg.Z)(c),e!==l.length-1&&(c+=", ")}if(c+=")",s&&(c+=` timestamp (${n})`),"NONE"!==t&&(c+=` PARTITION BY ${t}`,0!==(o??0)&&(c+=` TTL ${o} ${i}`),c+=" "+(r?"WAL":"BYPASS WAL")),r&&a&&s){const e=l.filter((e=>e.upsertKey));e.length>0&&(c+=` DEDUP UPSERT KEYS(${(e.find((e=>e.column===n))?e:[...e,{column:n}]).map((e=>e.column)).join(",")})`)}return`${F(c)};`})({name:t,partitionBy:r,timestamp:a,walEnabled:"true"===c,ttlValue:o,ttlUnit:i,schemaColumns:l.map((e=>({column:e.name,type:e.type}))),dedup:!1});s(d,{appendAt:"end"}),n(Yt())},trigger:r.createElement(Xe,{icon:r.createElement(Et,{"data-hook":"create-table-panel-button",selected:void 0!==e,onClick:()=>{n(Gt.setActiveSidebar(e?void 0:"create"))}},r.createElement(el.i,{size:w})),placement:"left",tooltip:"Create table"}),ctaText:"Create"})};var Zg=n(85998),Ug=n(50364),qg=n(52368),Gg=n(22709),$g=n(52067),jg=n(47910),Wg=n(27545),Qg=n(43167);const Yg=s.ZP.div.withConfig({displayName:"Shortcuts__Wrapper"})(["\n  display: flex;\n  max-height: 650px;\n  width: 300px;\n  max-width: 100vw;\n  flex-direction: column;\n  background: ",";\n  box-shadow: "," 0px 5px 8px;\n  border: 1px solid ",";\n  border-radius: 4px;\n  overflow: auto;\n"],M("backgroundDarker"),M("black"),M("black")),Kg=s.ZP.div.withConfig({displayName:"Shortcuts__List"})(["\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n"]),Xg=(0,s.ZP)(Pe).withConfig({displayName:"Shortcuts__ListTitle"})(["\n  padding: 0.6rem 1.2rem;\n  margin-bottom: 0.6rem;\n  border-top: 1px solid ",";\n  background: ",";\n  width: 100%;\n"],(({theme:e})=>e.color.selection),(({theme:e})=>e.color.black40)),Jg=s.ZP.div.withConfig({displayName:"Shortcuts__Item"})(["\n  display: flex;\n  align-items: center;\n  padding: 0.6rem 1.2rem;\n\n  &:not(:last-child) {\n    border-bottom: 1px solid ",";\n  }\n"],M("background")),ef=s.ZP.div.withConfig({displayName:"Shortcuts__ItemKeys"})(["\n  margin-left: auto;\n"]),tf=s.ZP.span.withConfig({displayName:"Shortcuts__KeyGroup"})(["\n  color: ",';\n\n  &:not(:last-child):after {\n    content: "or";\n    padding: 0 0.5rem;\n  }\n'],M("gray2")),nf=s.ZP.span.withConfig({displayName:"Shortcuts__Key"})(["\n  padding: 0 4px;\n  background: ",";\n  border-radius: 2px;\n  color: ",";\n\n  &:not(:last-child) {\n    margin-right: 0.25rem;\n  }\n"],M("gray2"),M("black")),rf=H.isMacintosh||H.isIOS?"⌘":"Ctrl",af=H.isMacintosh||H.isIOS?"⌥":"Alt",of=[{keys:[[af,"T"]],title:"Add new tab"},{keys:[[af,rf,"↑"]],title:"Add cursor above"},{keys:[[af,rf,"↓"]],title:"Add cursor below"},{keys:[["⇧",af,"↑"]],title:"Copy line up"},{keys:[["⇧",af,"↓"]],title:"Copy line down"},{keys:[[af,"↑"]],title:"Move line up"},{keys:[[af,"↓"]],title:"Move line down"},{keys:[["⇧",af,"F"]],title:"Format document"},{keys:[["F1"]],title:"Command palette"}],lf=[{keys:[["F9"],[rf,"Enter"]],title:"Run query"},{keys:[["F2"]],title:"Focus results grid"},{keys:[[rf,"K"]],title:"Clear all notifications"}],sf=({list:e,title:t})=>r.createElement(Kg,null,r.createElement(Xg,null,r.createElement(Pe,{color:"gray2"},t)),e.map(((e,t)=>r.createElement(Jg,{key:`shortcut-${t}`},r.createElement(Pe,{color:"white"},e.title),r.createElement(ef,null,e.keys.map(((e,t)=>r.createElement(tf,{key:`keyGroup-${t}`},e.map(((e,t)=>r.createElement(nf,{key:`shortcutItem-key-${e}-${t}`},r.createElement(Pe,{color:"black",size:"xs",weight:600},e)))))))))))),cf=()=>r.createElement(Yg,null,r.createElement(sf,{list:lf,title:"Global shortcuts"}),r.createElement(sf,{list:of,title:"SQL editor shortcuts"}));function df(){return df=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},df.apply(this,arguments)}const uf=(0,s.ZP)(Et).withConfig({displayName:"help__HelpButton"})(["\n  padding: 0;\n"]),mf=(0,s.ZP)(Ho.h.Content).withConfig({displayName:"help__DropdownMenuContent"})(["\n  background: ",";\n"],(({theme:e})=>e.color.backgroundDarker)),pf=(0,s.ZP)(Ho.h.Item).withConfig({displayName:"help__DropdownMenuItem"})(["\n  color: ",";\n  ","\n"],(({theme:e})=>e.color.foreground),(({withlink:e})=>e&&"padding: 0;")),gf=(0,s.ZP)(Pn.x).attrs({justifyContent:"center"}).withConfig({displayName:"help__TooltipWrapper"})(["\n  width: 100%;\n  height: 100%;\n"]),ff=s.ZP.div.withConfig({displayName:"help__ShortcutsWrapper"})(["\n  position: fixed;\n  right: 0;\n  margin-top: 4.5rem;\n"]),hf=(0,s.ZP)(ot).withConfig({displayName:"help__StyledLink"})(["\n  padding: 0.5rem 1rem;\n  width: 100%;\n"]),bf=({href:e,text:t,icon:n,...a})=>r.createElement(hf,df({color:"foreground",hoverColor:"foreground",href:e,rel:"noreferrer",target:"_blank"},a),r.createElement(Pn.x,{align:"center",gap:"1.5rem"},n,r.createElement(Pn.x,{align:"center",gap:"0.75rem"},t,r.createElement(ha.d,{size:"14px"})))),Ef=()=>{const{quest:e}=(0,r.useContext)(qr),t=(0,o.v9)(Cn.telemetry.getConfig),[n,a]=(0,r.useState)(!1),i=(0,r.useCallback)((e=>{a(e)}),[]),[l,s]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),u=qe("Escape");return(0,r.useEffect)((()=>{u&&n&&a(!1)}),[u]),r.createElement(r.Fragment,null,r.createElement(Qg.o,{open:c,onOpenChange:d,withEmailInput:!0,title:"Contact us",subtitle:"Let us know your thoughts",onSubmit:async({email:n,message:r})=>{try{await e.sendFeedback({email:n,message:r,telemetryConfig:t}),Bt("Thank you for your feedback! Our team will review it shortly.")}catch(e){throw Ft("Something went wrong. Please try again later."),e}}}),r.createElement(Ho.h.Root,{modal:!1,onOpenChange:s},r.createElement(Ho.h.Trigger,{asChild:!0},r.createElement(uf,df({},l&&{selected:!0},{"data-hook":"help-panel-button"}),r.createElement(Xe,{icon:r.createElement(gf,null,r.createElement(Zg.H,{size:w})),placement:"left",tooltip:"Help"}))),r.createElement(Ho.h.Portal,null,r.createElement(mf,null,r.createElement(pf,{onSelect:e=>e.preventDefault(),onClick:()=>d(!0),"data-hook":"help-link-contact-us"},r.createElement(Ug.f,{size:"18px"}),r.createElement(Pe,{color:"foreground"},"Contact us")),r.createElement(pf,{asChild:!0,withlink:"true"},r.createElement(Ce.A,null,r.createElement(bf,{"data-hook":"help-link-slack",href:"https://slack.questdb.io/",text:"Slack community",icon:r.createElement(Gg.j,{size:"18px"})}))),r.createElement(pf,{asChild:!0,withlink:"true"},r.createElement(Ce.A,null,r.createElement(bf,{"data-hook":"help-link-community",href:"https://community.questdb.io/",text:"Public forum",icon:r.createElement($g.o,{size:"18px"})}))),r.createElement(pf,{asChild:!0,withlink:"true"},r.createElement(Ce.A,null,r.createElement(bf,{"data-hook":"help-link-stackoverflow",href:"https://stackoverflow.com/tags/questdb",text:"Stack Overflow",icon:r.createElement(jg.r,{size:"18px"})}))),r.createElement(pf,{asChild:!0,withlink:"true"},r.createElement(Ce.A,null,r.createElement(bf,{"data-hook":"help-link-web-console-docs",href:"https://questdb.io/docs/develop/web-console/",text:"Web Console Docs",icon:r.createElement(Zg.H,{size:"18px"})}))),r.createElement(pf,{onClick:()=>i(!0)},r.createElement(qg.m,{size:"18px"}),r.createElement(Pe,{color:"foreground"},"Shortcuts")),r.createElement(pf,{asChild:!0,withlink:"true"},r.createElement(Ce.A,null,r.createElement(bf,{href:"https://github.com/questdb/ui/commit/7ef883c",text:"Commit id: 7ef883c",icon:r.createElement(Wg.E,{size:"18px"})})))))),r.createElement(ut,{active:n,onToggle:i,trigger:r.createElement(r.Fragment,null)},r.createElement(ff,null,r.createElement(cf,null))))},yf=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"space-between"}).withConfig({displayName:"warning__WarningRoot"})(["\n  width: 100%;\n  height: 4rem;\n  background: #f8a24d;\n  color: #000;\n"]),wf=(0,s.ZP)(Pn.x).attrs({gap:"0.5rem"}).withConfig({displayName:"warning__Content"})(["\n  padding: 0 1.5rem;\n"]),Cf=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"center"}).withConfig({displayName:"warning__CloseButton"})(["\n  border: 0;\n  cursor: pointer;\n  width: 4.5rem;\n  height: 4.5rem;\n"]),vf=s.ZP.span.withConfig({displayName:"warning__WarningText"})(["\n  font-weight: 600;\n"]),_f=s.ZP.a.withConfig({displayName:"warning__WorkaroundLink"})(["\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  margin-left: 0.5rem;\n  color: #000;\n\n  &:hover {\n    text-decoration: none;\n  }\n"]),xf=(0,s.ZP)(_e.x).withConfig({displayName:"warning__CloseIcon"})(["\n  color: #000;\n"]),Sf=()=>{const{warnings:e}=Ir(),[t,n]=(0,r.useState)([]);return(0,r.useEffect)((()=>{e&&e.length>0&&n(e.map((e=>e.tag)))}),[e]),0===t.length?null:r.createElement(Pn.x,{flexDirection:"column",gap:"0.1rem",style:{width:"100%"},"data-hook":"warnings"},e.filter((e=>t.includes(e.tag))).map(((e,a)=>r.createElement(yf,{key:a,"data-hook":"warning"},r.createElement(wf,null,r.createElement(Ot.u,{size:"20px"}),"Warning:"," ",r.createElement(vf,{"data-hook":"warning-text"},e.warning),Sd[e.tag]&&r.createElement(_f,{href:Sd[e.tag].link,rel:"noreferrer noopener",target:"_blank","data-hook":"warning-workaround-link"},r.createElement(ha.d,{size:"16px"}),Sd[e.tag].title)),r.createElement(Cf,{"data-hook":"warning-close-button",onClick:()=>n(t.filter((t=>t!==e.tag)))},r.createElement(xf,{size:"20px"}))))))};function kf(){return kf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},kf.apply(this,arguments)}const Tf=(0,s.ZP)(Pn.x).attrs({align:"center",justifyContent:"center"}).withConfig({displayName:"image-zoom__Root"})(["\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  z-index: 1000;\n  opacity: ",";\n  pointer-events: ",";\n"],(({visible:e})=>e?1:0),(({visible:e})=>e?"auto":"none")),Nf=s.ZP.div.withConfig({displayName:"image-zoom__Overlay"})(["\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(33, 34, 44, 0.9);\n  opacity: ",";\n  pointer-events: ",";\n  transition: opacity 0.2s ease-in-out;\n"],(({visible:e})=>e?1:0),(({visible:e})=>e?"auto":"none")),Of=s.ZP.div.withConfig({displayName:"image-zoom__Wrapper"})(["\n  z-index: 1001;\n\n  img {\n    border: 1px solid ",";\n    border-radius: ",";\n  }\n"],(({theme:e})=>e.color.offWhite),(({theme:e})=>e.borderRadius)),Rf=()=>{const e=(0,o.v9)(Cn.console.getImageToZoom),t=(0,o.I0)(),n=(0,r.useRef)(null),[a,i]=(0,r.useState)(0),[l,s]=(0,r.useState)(0),c=(0,o.v9)(Cn.console.getActiveSidebar),d=e=>{"Escape"===e.key&&t(Gt.setImageToZoom(void 0))};return(0,r.useEffect)((()=>{n.current&&(i(n.current.offsetWidth),s(n.current.offsetHeight))}),[e]),(0,r.useEffect)((()=>{"news"===c?document.addEventListener("keydown",d):document.removeEventListener("keydown",d)}),[c]),"news"!==c?null:r.createElement(Tf,{ref:n,visible:void 0!==e},r.createElement(Nf,{visible:void 0!==e}),e&&r.createElement(Of,null,r.createElement(kg,kf({},e,{containerWidth:a?.9*a:460,containerHeight:l?.75*l:460}))))},If=s.ZP.div.withConfig({displayName:"Layout__Page"})(["\n  display: flex;\n  width: 100%;\n  height: 100%;\n  flex-direction: column;\n  flex: 1;\n  overflow: hidden;\n  font-size: 1.4rem;\n  background: #21222c;\n\n  ::selection {\n    background: #44475a;\n  }\n"]),Af=s.ZP.div.withConfig({displayName:"Layout__Root"})(["\n  display: flex;\n  width: 100%;\n  flex: 1;\n  overflow-y: auto;\n"]),Lf=s.ZP.div.withConfig({displayName:"Layout__Main"})(["\n  position: relative;\n  flex: 1;\n  display: flex;\n  width: ",";\n"],(({sideOpened:e})=>e?"calc(100% - 50rem - 4.5rem)":"calc(100% - 4.5rem)")),Pf=s.ZP.div.withConfig({displayName:"Layout__Drawer"})(["\n  background: ",";\n"],(({theme:e})=>e.color.backgroundDarker)),Df=()=>{const e=(0,o.v9)(Cn.console.getActiveSidebar),t=(0,r.useCallback)((()=>{I.publish(A.TAB_FOCUS)}),[]),n=(0,r.useCallback)((()=>{I.publish(A.TAB_BLUR)}),[]);return(0,r.useEffect)((()=>(window.addEventListener("focus",t),window.addEventListener("blur",n),()=>{window.removeEventListener("focus",t),window.removeEventListener("blur",n)})),[]),r.createElement(ua,null,r.createElement(Eg,null),r.createElement(Sf,null),r.createElement(Af,null,r.createElement(Lf,{sideOpened:void 0!==e},r.createElement(Rf,null),r.createElement(If,null,r.createElement(Gm,null))),r.createElement(Pf,{id:"side-panel-right"}),r.createElement(uu,{align:"top"},r.createElement(Ef,null),r.createElement(Fg,null),r.createElement(zg,null))),r.createElement(Wm,null),r.createElement(Da,null))},Mf=(0,l.k)(),Bf=(0,i.MT)(wn,(0,i.qC)((0,i.md)(Mf)));Mf.run(hn);const Ff=X("fade-reg",K.REG),Hf=X("fade-slow",K.SLOW);a.render(r.createElement(s.f6,{theme:Lt},r.createElement(Qe,null,r.createElement(o.zt,{store:Bf},r.createElement(Rr,null,r.createElement(ga,null,r.createElement(Fr,null,r.createElement(Gr,null,r.createElement(d,null),a.createPortal(r.createElement(Ht,null),document.body),r.createElement(qd,null,r.createElement(Hf,null),r.createElement(Ff,null),r.createElement(Df,null))))))))),document.getElementById("root"))},94349:e=>{e.exports={arrayEquals:function(e,t){if(!e||!t)return!1;if(e.length!==t.length)return!1;for(var n=0,r=e.length;n<r;n++)if(e[n]instanceof Array&&t[n]instanceof Array){if(!e[n].equals(t[n]))return!1}else if(e[n]!==t[n])return!1;return!0}}},88344:()=>{},32019:()=>{}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={id:e,loaded:!1,exports:{}};return r[e].call(n.exports,n,n.exports,o),n.loaded=!0,n.exports}o.m=r,o.amdO={},e=[],o.O=(t,n,r,a)=>{if(!n){var i=1/0;for(d=0;d<e.length;d++){for(var[n,r,a]=e[d],l=!0,s=0;s<n.length;s++)(!1&a||i>=a)&&Object.keys(o.O).every((e=>o.O[e](n[s])))?n.splice(s--,1):(l=!1,a<i&&(i=a));if(l){e.splice(d--,1);var c=r();void 0!==c&&(t=c)}}return t}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[n,r,a]},o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var a=Object.create(null);o.r(a);var i={};t=t||[null,n({}),n([]),n(n)];for(var l=2&r&&e;"object"==typeof l&&!~t.indexOf(l);l=n(l))Object.getOwnPropertyNames(l).forEach((t=>i[t]=()=>e[t]));return i.default=()=>e,o.d(a,i),a},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e={87:0};o.O.j=t=>0===e[t];var t=(t,n)=>{var r,a,[i,l,s]=n,c=0;if(i.some((t=>0!==e[t]))){for(r in l)o.o(l,r)&&(o.m[r]=l[r]);if(s)var d=s(o)}for(t&&t(n);c<i.length;c++)a=i[c],o.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return o.O(d)},n=self.webpackChunk_questdb_web_console=self.webpackChunk_questdb_web_console||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),o.nc=void 0;var i=o.O(void 0,[736],(()=>o(53905)));i=o.O(i)})();